<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Carriages;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class SetCarriagePostingsAction
{
    public function run(string $carriageId, array $postingNumbers): array
    {
        $carriage = $this->getCarriageWithIntegration($carriageId);
        
        $this->validateCarriageStatus($carriage);
        $this->validatePostingsExist($carriage->integration_id, $postingNumbers);

        try {
            DB::beginTransaction();

            $ozonResponse = $this->setCarriagePostingsInOzon($carriage, $postingNumbers);

            $this->updateCarriagePostings($carriageId, $carriage->integration_id, $postingNumbers);

            DB::commit();

            return [
                'carriage_id' => $carriageId,
                'ozon_carriage_id' => $carriage->ozon_carriage_id,
                'posting_numbers' => $postingNumbers,
                'postings_count' => count($postingNumbers),
                'ozon_response' => $ozonResponse->result ?? [],
            ];

        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function getCarriageWithIntegration(string $carriageId): object
    {
        $carriage = DB::table('ozon_carriages as c')
            ->join('ozon_integrations as i', 'c.integration_id', '=', 'i.id')
            ->where('c.id', $carriageId)
            ->select([
                'c.id',
                'c.ozon_carriage_id',
                'c.status',
                'c.integration_id',
                'i.api_key',
                'i.client_id'
            ])
            ->first();

        if (!$carriage) {
            throw new NotFoundException('Carriage not found');
        }

        return $carriage;
    }

    protected function validateCarriageStatus(object $carriage): void
    {
        if ($carriage->status !== 'new') {
            throw new Exception('Can only modify postings for carriages with status "new"');
        }
    }

    protected function validatePostingsExist(string $integrationId, array $postingNumbers): void
    {
        $existingPostings = DB::table('ozon_fbs_orders')
            ->where('integration_id', $integrationId)
            ->whereIn('posting_number', $postingNumbers)
            ->where('status', 'ready_to_ship')
            ->pluck('posting_number')
            ->toArray();

        $missingPostings = array_diff($postingNumbers, $existingPostings);
        
        if (!empty($missingPostings)) {
            throw new NotFoundException('Postings not found or not ready to ship: ' . implode(', ', $missingPostings));
        }
    }

    protected function setCarriagePostingsInOzon(object $carriage, array $postingNumbers): object
    {
        $api = new API(
            apiKey: decrypt($carriage->api_key),
            clientId: decrypt($carriage->client_id)
        );

        return $api->FBS()->setCarriagePostings(
            carriageId: (int) $carriage->ozon_carriage_id,
            postingNumbers: $postingNumbers
        );
    }

    protected function updateCarriagePostings(string $carriageId, string $integrationId, array $postingNumbers): void
    {
        DB::table('ozon_carriage_postings')->where('carriage_id', $carriageId)->delete();

        $orders = DB::table('ozon_fbs_orders')
            ->where('integration_id', $integrationId)
            ->whereIn('posting_number', $postingNumbers)
            ->select(['id', 'posting_number', 'status'])
            ->get();

        $postingRecords = [];
        $now = now();

        foreach ($orders as $order) {
            $postingRecords[] = [
                'id' => \Illuminate\Support\Str::orderedUuid()->toString(),
                'carriage_id' => $carriageId,
                'order_id' => $order->id,
                'posting_number' => $order->posting_number,
                'status_at_creation' => $order->status,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        if (!empty($postingRecords)) {
            DB::table('ozon_carriage_postings')->insert($postingRecords);
        }
    }
}
