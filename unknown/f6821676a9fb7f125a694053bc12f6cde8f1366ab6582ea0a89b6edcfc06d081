<?php

namespace App\Services\Api\Internal\GoodsTransferService\Handlers;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\GoodsTransferRepositoryContract;
use App\Contracts\Repositories\GoodsTransferItemsRepositoryContract;
use App\Jobs\FIFOJobs\BulkHandleGoodsTransferFifoJob;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\Queue;
use RuntimeException;

class GoodsTransferDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly GoodsTransferRepositoryContract $transfersRepository,
        private readonly GoodsTransferItemsRepositoryContract $transferItemsRepository,
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly DocumentsRepositoryContract $documentsRepository,
        private readonly CabinetSettingRepositoryContract $cabinetSettingRepository
    ) {
    }

    public function run(string $resourceId): void
    {
        $resource = $this->transfersRepository->show($resourceId);
        if (!$resource) {
            throw new RuntimeException('Transfer not found in handler.');
        }

        // Получаем все элементы перемещения для обработки FIFO
        $transferItems = $this->transferItemsRepository->getItemsByTransferId($resourceId);

        // Удаляем перемещение
        $this->transfersRepository->delete($resourceId);

        // Запускаем обработку FIFO для всех элементов перемещения
        if ($transferItems->isNotEmpty()) {
            Queue::push(new BulkHandleGoodsTransferFifoJob($transferItems, true));
        }
    }
}
