<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Warehouses\WarehousePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Warehouses\WarehouseService\DTO\WarehouseDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class WarehousePolicy implements WarehousePolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet(
            $dto->cabinet_id,
            [PermissionNameEnum::WAREHOUSES->value => 'create']
        );

        if ($dto->work_schedule_id) {
            $this->authService->validateRelationAccess(
                'warehouse_work_schedules',
                $dto->work_schedule_id,
                $dto->cabinet_id
            );
        }

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinet_id
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseDTO) {
            return;
        }

        $warehouse = $this->authService->validateRelationAccess(
            entity: 'warehouses',
            entityId: $dto->resourceId,
            permission: PermissionNameEnum::WAREHOUSES->value,
            operation: 'update'
        );

        if ($dto->work_schedule_id && $dto->work_schedule_id != $warehouse->work_schedule_id) {
            $this->authService->validateRelationAccess(
                'warehouse_work_schedules',
                $dto->work_schedule_id,
                $warehouse->cabinet_id
            );
        }

        if (
            $dto->employeeId != $warehouse->employee_id
            ||
            $dto->departmentId != $warehouse->department_id
        ) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $warehouse->cabinet_id
            );
        }
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouses',
            entityId: $resourceId,
            permission: PermissionNameEnum::WAREHOUSES->value,
            operation: 'view'
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouses',
            entityId: $resourceId,
            permission: PermissionNameEnum::WAREHOUSES->value,
            operation: 'delete'
        );
    }
}
