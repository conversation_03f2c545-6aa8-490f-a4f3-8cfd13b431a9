<?php

namespace Tests\Unit\Traits;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Traits\HasFiles;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class HasFilesTest extends TestCase
{
    use HasFiles;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Bus::fake();

        // Clean up the database before each test
        DB::table('file_relations')->delete();
        DB::table('files')->delete();
    }

    public function test_generates_urls_for_files(): void
    {
        // Arrange
        $files = [
            ['path' => 'path1.jpg', 'is_private' => false],
            ['path' => 'path2.jpg', 'is_private' => true]
        ];

        // Act
        $this->generateUrls($files);

        // Assert
        foreach ($files as $file) {
            $this->assertNotEmpty($file['path'], 'URL should not be empty');
            $this->assertIsString($file['path'], 'URL should be a string');
            $this->assertStringContainsString($file['path'], $file['path'], 'URL should contain the original path');
        }

        // Verify that private and public URLs are different
        $this->assertNotEquals(
            $files[0]['path'],
            $files[1]['path'],
            'Private and public URLs should be different'
        );
    }

    public function test_sets_relation_to_files(): void
    {
        // Arrange
        $relationId = '550e8400-e29b-41d4-a716-446655440010';
        $fileIds = [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001'
        ];
        $type = 'document';

        // Create files in the database
        foreach ($fileIds as $fileId) {
            DB::table('files')->insert([
                'id' => $fileId,
                'path' => 'test/path.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 1024,
                'created_at' => now(),
                'updated_at' => now(),
                'cabinet_id' => Cabinet::factory()->create()->id,
                'name' => 'TESTFILE' . random_int(0, 999),
                'employee_id' => Employee::factory()->create()->id
            ]);
        }

        // Act
        $this->setRelationToFiles($relationId, $fileIds, $type);

        // Assert
        $this->assertDatabaseHas('file_relations', [
            'related_id' => $relationId,
            'related_type' => $type,
            'file_id' => $fileIds[0]
        ]);
        $this->assertDatabaseHas('file_relations', [
            'related_id' => $relationId,
            'related_type' => $type,
            'file_id' => $fileIds[1]
        ]);
    }

    public function test_updates_relation_to_files(): void
    {
        // Arrange
        $relationId = '550e8400-e29b-41d4-a716-446655440020';
        $oldFileIds = [
            '550e8400-e29b-41d4-a716-446655440100',
            '550e8400-e29b-41d4-a716-446655440101'
        ];
        $newFileIds = [
            '550e8400-e29b-41d4-a716-446655440102',
            '550e8400-e29b-41d4-a716-446655440103'
        ];
        $type = 'document';

        // Create all files in the database
        foreach (array_merge($oldFileIds, $newFileIds) as $fileId) {
            DB::table('files')->insert([
                'id' => $fileId,
                'path' => 'test/path.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 1024,
                'created_at' => now(),
                'updated_at' => now(),
                'cabinet_id' => Cabinet::factory()->create()->id,
                'name' => 'TESTFILE' . random_int(0, 999),
                'employee_id' => Employee::factory()->create()->id
            ]);
        }

        $this->setRelationToFiles($relationId, $oldFileIds, $type);

        // Act
        $this->setRelationToFiles($relationId, $newFileIds, $type, true);

        // Assert
        foreach ($oldFileIds as $fileId) {
            $this->assertDatabaseMissing('file_relations', [
                'related_id' => $relationId,
                'file_id' => $fileId
            ]);
        }

        foreach ($newFileIds as $fileId) {
            $this->assertDatabaseHas('file_relations', [
                'related_id' => $relationId,
                'file_id' => $fileId
            ]);
        }
    }
}
