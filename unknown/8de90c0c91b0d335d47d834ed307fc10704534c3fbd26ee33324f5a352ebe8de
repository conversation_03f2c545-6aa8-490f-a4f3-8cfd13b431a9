<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Sales\CustomerOrderPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\DTO\CustomerOrderDTO;
use App\Traits\HasEmployeeAndDepartment;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class CustomerOrderPolicy implements CustomerOrderPolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     * @throws \Exception
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof CustomerOrderDTO) {
            return;
        }

        $requiredPermissions = [
            PermissionNameEnum::CUSTOMER_ORDERS->value => 'create'
        ];

        if ($dto->held) {
            $requiredPermissions[PermissionNameEnum::CUSTOMER_ORDERS->value] = ['create', 'held'];
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId, $requiredPermissions);

        if ($dto->status_id) {
            $this->authService->validateRelationAccess('statuses', $dto->status_id, $dto->cabinetId);
        }

        $legal = $this->authService->validateRelationAccess(
            'legal_entities',
            $dto->legal_entity_id,
            $dto->cabinetId
        );
        if ($legal->archived_at) {
            throw new AccessDeniedException('Legal is archived');
        }
        $contractor = $this->authService->validateRelationAccess(
            'contractors',
            $dto->contractor_id,
            $dto->cabinetId
        );
        if ($contractor->archived_at) {
            throw new AccessDeniedException('ContractorEntity is archived');
        }
        if ($dto->warehouse_id) {
            $this->authService->validateRelationAccess('warehouses', $dto->warehouse_id, $dto->cabinetId);
        }

        if ($dto->sales_channel_id) {
            $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->sales_channel_id,
                $dto->cabinetId,
                PermissionNameEnum::SALE_CHANNELS->value,
                'view'
            );
        }

        if ($dto->files) {
            $this->authService->validateResourcesAccess(
                'files',
                $dto->cabinetId,
                $dto->files,
            );
        }
        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );
    }

    /**
     * @throws AccessDeniedException
     * @throws \Exception
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof CustomerOrderDTO) {
            return;
        }

        $order = $this->authService->validateRelationAccess('customer_orders', $dto->resourceId);

        $requiredPermissions = [
            PermissionNameEnum::CUSTOMER_ORDERS->value => 'update',
        ];

        if ($dto->held !== $order->held) {
            $requiredPermissions[PermissionNameEnum::CUSTOMER_ORDERS->value] .= 'held';
        }

        $this->authService->hasAccessToCabinet($order->cabinet_id, $requiredPermissions);
        $this->authService->hasEntityPermission(
            $order,
            PermissionNameEnum::CUSTOMER_ORDERS->value,
            'update'
        );

        if ($dto->status_id && $order->status_id != $dto->status_id) {
            $this->authService->validateRelationAccess('statuses', $dto->status_id, $order->cabinet_id);
        }

        if ($order->legal_entity_id != $dto->legal_entity_id) {
            $legal = $this->authService->validateRelationAccess(
                'legal_entities',
                $dto->legal_entity_id,
                $order->cabinet_id
            );
            if ($legal->archived_at) {
                throw new AccessDeniedException('Legal is archived');
            }
        }

        if ($order->contractor_id != $dto->contractor_id) {
            $contractor = $this->authService->validateRelationAccess(
                'contractors',
                $dto->contractor_id,
                $order->cabinet_id
            );
            if ($contractor->archived_at) {
                throw new AccessDeniedException('ContractorEntity is archived');
            }
        }
        if ($dto->warehouse_id && $order->warehouse_id != $dto->warehouse_id) {
            $this->authService->validateRelationAccess('warehouses', $dto->warehouse_id, $order->cabinet_id);
        }

        if ($dto->sales_channel_id && $dto->sales_channel_id != $order->sales_channel_id) {
            $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->sales_channel_id,
                $order->cabinet_id,
                PermissionNameEnum::SALE_CHANNELS->value,
                'view'
            );
        }
        if ($dto->files) {
            $this->authService->validateResourcesAccess(
                'files',
                $order->cabinet_id,
                $dto->files,
            );
        }

        if ($order->employee_id != $dto->employeeId || $order->department_id != $dto->departmentId) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $order->cabinet_id
            );
        }


    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('customer_orders', $resourceId, null, PermissionNameEnum::CUSTOMER_ORDERS->value, 'delete');

        $node = DB::table('documents')
            ->select(['lft', 'rgt', 'tree_id'])
            ->where('documentable_id', $resourceId)
            ->first();

        if (!$node) {
            return;
        }

        // Проверяем, что это листовой узел (разница между rgt и lft должна быть 1)
        if ($node->rgt - $node->lft !== 1) {
            throw new RuntimeException('Cannot delete node with children');
        }
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('customer_orders', $resourceId, null, PermissionNameEnum::CUSTOMER_ORDERS->value, 'view');
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'customer_orders',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CUSTOMER_ORDERS->value,
            'delete',
        );
    }

    public function bulkHeld(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'customer_orders',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CUSTOMER_ORDERS->value,
            'held',
        );
    }

    public function bulkUpdate(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'customer_orders',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CUSTOMER_ORDERS->value,
            'update',
        );
    }

    public function bulkCopy(array $data): void
    {
        $this->authService->hasAccessToCabinet($data['cabinet_id'], [
            PermissionNameEnum::CUSTOMER_ORDERS->value => 'create',
        ]);

        $this->authService->validateResourcesAccess(
            'customer_orders',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::CUSTOMER_ORDERS->value,
            'view',
        );
    }
}
