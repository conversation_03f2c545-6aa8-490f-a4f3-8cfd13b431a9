<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions;

use App\Modules\Marketplaces\Services\Ozon\Data\OzonMarketData;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RuntimeException;

readonly class CreateMarketAction
{
    use HasOrderedUuid;
    private array $syncTables;

    public function __construct()
    {
        $this->syncTables = [
            'ozon_price_settings',
            'ozon_order_settings',
            'ozon_report_settings'
        ];
    }

    public function run(OzonMarketData $data): string
    {
        $integrationId = $this->generateUuid();
        $time = Carbon::now();

        try {
            DB::beginTransaction();

            $salesChannelId = $this->generateUuid();

            $typeId = DB::table('sales_channel_types')
                ->where('name', 'Маркетплейс')
                ->value('id');

            DB::table('sales_channels')
                ->insert([
                    'id' => $salesChannelId,
                    'created_at' => $time,
                    'updated_at' => $time,
                    'cabinet_id' => $data->cabinetId,
                    'name' => $data->name,
                    'sales_channel_type_id' => $typeId,
                    'is_common' => true
                ]);

            DB::table('ozon_integrations')
                ->insert([
                    'id' => $integrationId,
                    'created_at' => $time,
                    'updated_at' => $time,

                    'cabinet_id' => $data->cabinetId,
                    'shop_name' => $data->name,

                    'client_id' => encrypt($data->clientId),
                    'api_key' => encrypt($data->apiKey),

                    'legal_entity_id' => $data->legalEntityId,
                    'department_id' => $data->departmentId,
                    'contractor_id' => $data->contractorId,
                    'commission_contract_id' => $data->comissionContractId,
                    'sales_channel_id' => $salesChannelId
                ]);

            foreach ($this->syncTables as $syncTable) {
                $toInsert = [
                    'id' => Str::orderedUuid()->toString(),
                    'integration_id' => $integrationId,

                    'created_at' => $time,
                    'updated_at' => $time
                ];

                DB::table($syncTable)
                    ->insert($toInsert);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new RuntimeException($e->getMessage());
        }

        return $integrationId;
    }

}
