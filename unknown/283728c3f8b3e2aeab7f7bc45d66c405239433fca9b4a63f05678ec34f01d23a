<?php

namespace App\Http\Requests\Api\Internal\Acceptances;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\AcceptanceEntity;
use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AcceptanceIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly AcceptanceEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],

            'filters.warehouses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.warehouses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.warehouses.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.warehouses.value');
                    $condition = $this->input('filters.warehouses.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.contractor_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractor_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractor_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.contractor_owners.value');
                    $condition = $this->input('filters.contractor_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.incoming_number.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.incoming_numbers.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),

            'filters.incoming_number.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.incoming_number.value');
                    $condition = $this->input('filters.incoming_number.condition');
                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.legal_entity.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.legal_entity.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.legal_entity.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.legal_entity.value');
                    $condition = $this->input('filters.legal_entity.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employee_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.employee_owners.value');
                    $condition = $this->input('filters.employee_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.contractors.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractors.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractors.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.contractors.value');
                    $condition = $this->input('filters.contractors.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.department_owners.value');
                    $condition = $this->input('filters.department_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.contractor_groups.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractor_groups.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractor_groups.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.contractor_groups.value');
                    $condition = $this->input('filters.contractor_groups.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.statuses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.statuses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.statuses.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.statuses.value');
                    $condition = $this->input('filters.statuses.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.is_common.value' => 'boolean',

            'filters.products.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.products.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.products.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.products.value');
                    $condition = $this->input('filters.products.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.is_held.value' => 'boolean',
            'filters.search.value' => 'string',

            'filters.period.from' => 'date_format:d.m.Y H:i',
            'filters.period.to' => 'date_format:d.m.Y H:i|after_or_equal:filters.period.from',

            'filters.incoming_date.from' => 'exclude_if:filters.incoming_date.condition, '. FilterConditionEnum::IN->value . '|date_format:d.m.Y H:i',
            'filters.incoming_date.to' => 'exclude_if:filters.incoming_date.condition, '. FilterConditionEnum::IN->value . '|date_format:d.m.Y H:i|after_or_equal:filters.incoming_date.from',
            'filters.incoming_date.condition' => 'string|' . Rule::in(FilterConditionEnum::cases()),

            'filters.updated_at.from' => 'exclude_if:filters.updated_at.condition, '. FilterConditionEnum::IN->value . '|date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'exclude_if:filters.updated_at.condition, '. FilterConditionEnum::IN->value . '|date_format:d.m.Y H:i|after_or_equal:filters.updated_at.from',


            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],

            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
