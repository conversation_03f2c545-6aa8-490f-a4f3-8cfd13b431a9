<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\Handlers;

use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class WorkScheduleCalendarGenerateHandler
{
    use HasOrderedUuid;

    protected ?object $schedule;
    protected Carbon $dateFrom;
    protected Carbon $dateTo;
    protected mixed $fillingType;
    protected mixed $cycleDayLength;
    protected Carbon $cycleDayFrom;
    protected mixed $keepHolidays;
    protected mixed $planningHorizon;
    protected mixed $fillingTemplate;

    public function __construct(string $sheduleId)
    {
        $this->schedule = DB::table('warehouse_work_schedules')->find($sheduleId);

        if (!$this->schedule) {
            throw new RuntimeException('Schedule not found');
        }

        $this->dateFrom = Carbon::parse($this->schedule->date_from);
        $this->dateTo = $this->schedule->planning_horizon > 0
            ? Carbon::parse($this->schedule->date_to)->addMonths($this->schedule->planning_horizon)
            : Carbon::parse($this->schedule->date_to);
        $this->fillingType = $this->schedule->filling_type;
        $this->cycleDayLength = $this->schedule->cycle_day_lenght;
        $this->cycleDayFrom = Carbon::parse($this->schedule->cycle_day_from);
        $this->keepHolidays = $this->schedule->keep_holidays;
        $this->planningHorizon = $this->schedule->planning_horizon;
        $this->fillingTemplate = json_decode($this->schedule->filling_template, true);
    }

    public function generate(): void
    {
        if ($this->fillingType == 1) {
            $this->generateByWeeks();
        } elseif ($this->fillingType == 2) {
            $this->generateByCycles();
        }

        //TODO придумать где брать список выходных
        /*if ($this->keepHolidays) {
            $this->applyHolidays();
        }*/
    }

    protected function generateByWeeks(): void
    {
        $currentDate = $this->dateFrom;
        DB::transaction(function () use ($currentDate) {
            while ($currentDate <= $this->dateTo) {

                $dayOfWeek = $currentDate->format('l');

                if ($this->fillingTemplate[$dayOfWeek] ?? null) {
                    DB::table('warehouse_calendars')->insert([
                        'id' => $this->generateUuid(),
                        'created_at' => Carbon::now(),
                        'schedule_id' => $this->schedule->id,
                        'date' => $currentDate->toDateString(),
                        'is_working_day' => true,
                        'is_holiday' => false,
                        'template_reference' => $dayOfWeek
                    ]);
                }

                $currentDate->addDay();
            }
        });
    }
    protected function generateByCycles(): void
    {
        $currentDate = $this->cycleDayFrom;
        $cycleDays = array_keys($this->fillingTemplate);
        $cycleLength = count($cycleDays);
        $cycleIndex = 0;

        DB::transaction(function () use ($currentDate, $cycleLength, $cycleDays, &$cycleIndex) {
            while ($currentDate <= $this->dateTo) {
                $dayOfCycle = $cycleDays[$cycleIndex % $cycleLength];
                if ($this->fillingTemplate[$dayOfCycle] ?? null) {
                    DB::table('warehouse_calendars')->insert([
                        'id' => $this->generateUuid(),
                        'created_at' => Carbon::now(),
                        'schedule_id' => $this->schedule->id,
                        'date' => $currentDate->toDateString(),
                        'is_working_day' => true,
                        'is_holiday' => false,
                        'template_reference' => $dayOfCycle
                    ]);
                }

                $currentDate->addDay();
                $cycleIndex++;
            }
        });
    }

    protected function applyHolidays(): void
    {
        foreach ($this->schedule->holiday_shedule as $holiday) {
            $holidayDate = Carbon::parse($holiday['date']);

            DB::table('warehouse_calendars')
                ->where('schedule_id', $this->schedule->id)
                ->where('date', $holidayDate->toDateString())
                ->update([
                    'is_working_day' => false,
                    'is_holiday' => true,
                    'template_reference' => 'Holiday'
                ]);
        }
    }
}
