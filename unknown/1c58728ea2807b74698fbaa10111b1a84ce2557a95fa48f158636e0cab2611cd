<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WorkSchedulesRepositoryContract;
use App\Jobs\GenerateCalendarJob;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\DTO\WorkScheduleDTO;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\Helpers\FilterTemplateCleaner;
use App\Traits\HasOrderedUuid;
use http\Exception\InvalidArgumentException;

class WorkSchedulesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly WorkSchedulesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WorkScheduleDTO) {
            throw new InvalidArgumentException();
        }

        $data = $dto->toUpdateArray();


        $cleanedTemplate = FilterTemplateCleaner::cleanAndFilterTemplate($dto->filling_template);
        $data['filling_template'] = json_encode($cleanedTemplate);

        $cleanedTemplate = FilterTemplateCleaner::cleanAndFilterTemplate($dto->holiday_schedule);
        $data['holiday_schedule'] = json_encode($cleanedTemplate);
        $this->repository->update($dto->resourceId, $data);

        GenerateCalendarJob::dispatch($dto->resourceId);
    }
}
