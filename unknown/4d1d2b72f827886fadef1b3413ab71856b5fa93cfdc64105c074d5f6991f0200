<?php

namespace App\Services\Api\Internal\ContractService\Handlers;

use App\Traits\HasFiles;
use InvalidArgumentException;
use App\Traits\HasOrderedUuid;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ContractRepositoryContract;
use App\Services\Api\Internal\ContractService\DTO\ContractDTO;

readonly class ContractCreateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public string $resourceId;

    public function __construct(
        private ContractRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ContractDTO) {
            throw new InvalidArgumentException();
        }

        if (!$dto->number) {
            $contractNumber = $this->repository->getMaxNumber($dto->cabinetId);
            $dto->number = $contractNumber
                ? str_pad((string)((int) $contractNumber + 1), 6, '0', STR_PAD_LEFT)
                : '000001';
        }

        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
