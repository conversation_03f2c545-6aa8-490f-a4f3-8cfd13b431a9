<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Warehouses\WarehousePhonePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\DTO\WarehousePhoneDTO;

readonly class WarehousePhonePolicy implements WarehousePhonePolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehousePhoneDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehousePhoneDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            entity: 'warehouse_phones',
            entityId: $dto->resourceId
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_phones',
            entityId: $resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_phones',
            entityId: $resourceId
        );
    }
}
