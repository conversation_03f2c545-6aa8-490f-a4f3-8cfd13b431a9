<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonWarehousesService\Handlers;

use App\Contracts\Repositories\OzonWarehousesRepositoryContract;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsShowHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonWarehousesService\DTO\OzonWarehousesDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class OzonWarehousesCreateHandler
{
    use HasOrderedUuid;
    private string $resourceId;

    public function __construct(
        private readonly OzonWarehousesRepositoryContract $repository,
        protected OzonApiRequestHandler $ozonApiRequestHandler,
        protected OzonCredentialsShowHandler $ozonCredentialsShowHandler,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(OzonWarehousesDTO $dto): string
    {

        $ozonCredential = $this->ozonCredentialsShowHandler->run($dto->ozonCredentialId);

        $response = $this->ozonApiRequestHandler->run($ozonCredential->client_id, $ozonCredential->api_key);

        $soldAmount = new Collection($response['result']);

        $orderNumberChunks = $soldAmount->chunk(1000);

        $cabinetId = $dto->cabinetId;
        $departmentId = $dto->departmentId;
        $employeeId = $dto->employeeId;
        $ozonCompanyId = $ozonCredential->client_id;

        $orderNumberChunks->each(function ($chunk) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId) {
            $warehouses = [];
            $existingOrders = $this->repository->getWarehousesByWarehouseIds($chunk->pluck('warehouse_id')->toArray());

            $chunk->each(function ($item) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId, $existingOrders, &$warehouses) {
                $orderId = $existingOrders[$item['warehouse_id']] ?? $this->generateUuid();

                $warehouses[] = [
                    'id' => $orderId,
                    'cabinet_id' => $cabinetId,
                    'department_id' => $departmentId,
                    'employee_id' => $employeeId,
                    'ozon_company_id' => $ozonCompanyId,
                    'name' => $item['name'],
                    'warehouse_id' => $item['warehouse_id'],
                    'is_rfbs' => $item['is_rfbs'],
                    'is_able_to_set_price' => $item['is_able_to_set_price'],
                    'has_entrusted_acceptance' => $item['has_entrusted_acceptance'],
                    'first_mile_dropoff_point_id' => $item['first_mile_type']['dropoff_point_id'],
                    'first_mile_dropoff_timeslot_id' => $item['first_mile_type']['dropoff_timeslot_id'],
                    'first_mile_is_changing' =>  $item['first_mile_type']['first_mile_is_changing'],
                    'first_mile_type' =>  $item['first_mile_type']['first_mile_type'],
                    'is_kgt' => $item['is_kgt'],
                    'can_print_act_in_advance' => $item['can_print_act_in_advance'],
                    'min_working_days' => $item['min_working_days'],
                    'is_karantin' => $item['is_karantin'],
                    'has_postings_limit' => $item['has_postings_limit'],
                    'postings_limit' => $item['postings_limit'],
                    'working_days' => json_encode($item['working_days']),
                    'min_postings_limit' => $item['min_postings_limit'],
                    'is_timetable_editable' => $item['is_timetable_editable'],
                    'status' => $item['status'],
                    'is_economy' => $item['is_economy'],
                    'is_presorted' => $item['is_presorted'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            });

            $this->saveProcessedData($warehouses);
        });

        return $this->resourceId ;
    }

    private function saveProcessedData(array $warehouses): void
    {
        if (!empty($warehouses)) {
            $this->repository->upsert($warehouses);
        }
    }

}
