<?php

namespace App\Services\Api\Internal\Contractors\ContractorsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ContractorAccountsRepositoryContract;
use App\Contracts\Repositories\ContractorAdressesRepositoryContract;
use App\Contracts\Repositories\ContractorContactsRepositoryContract;
use App\Contracts\Repositories\ContractorContractorGroupRepositoryContract;
use App\Contracts\Repositories\ContractorDetailAddressRepositoryContract;
use App\Contracts\Repositories\ContractorDetailsRepositoryContract;
use App\Contracts\Repositories\ContractorsRepositoryContract;
use App\Services\Api\Internal\Contractors\ContractorsService\DTO\ContractorDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use InvalidArgumentException;
use RuntimeException;

class ContractorUpdateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    private string $resourceId;

    public function __construct(
        private ContractorsRepositoryContract $repository,
        private ContractorAdressesRepositoryContract $contractorAdressesRepository,
        private ContractorDetailsRepositoryContract $contractorDetailsRepository,
        private ContractorContractorGroupRepositoryContract $groupRepository,
        private ContractorAccountsRepositoryContract $accountsRepository,
        private ContractorContactsRepositoryContract $contactsRepository,
        private ContractorDetailAddressRepositoryContract $contractorDetailAddressRepository,
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ContractorDTO) {
            throw new InvalidArgumentException('Invalid DTO');
        }
        $this->resourceId = $dto->resourceId;

        $this->repository->update($dto->resourceId, $dto->toUpdateArray());
        $this->contractorAdressesRepository->upsert($dto->toInsertAdressArray($dto->resourceId));

        $detailId = $this->contractorDetailsRepository->getDetailsByContractorId($dto->resourceId);
        $this->contractorDetailsRepository->upsert($dto->toInsertDetailArray($detailId->id, $this->resourceId));
        $this->contractorDetailAddressRepository->upsert($dto->toInsertDetailAddressArray($detailId->id));


        $this->manageAccounts($dto->accounts);
        $this->manageContacts($dto->contacts);
        $this->manageGroups($dto->contractorGroups);

        $this->setRelationToFiles($this->resourceId, $dto->files, 'contractors', true);

    }

    private function manageAccounts(array $accounts): void
    {
        $accounts = collect($accounts);

        $defaultKeys = [
            'is_main' => false,
            'bank' => null,
            'bik' => null,
            'correspondent_account' => null,
            'payment_account' => null,
            'contractor_id' => $this->resourceId,
            'address' => null,
            'updated_at' => Carbon::now(),
            'created_at' => Carbon::now(),
        ];

        $hasMain = false;
        $accounts = $accounts->map(function ($item) use ($defaultKeys, &$hasMain) {
            if (!isset($item['id'])) {
                $item['id'] = $this->generateUuid();
            }

            if (isset($item['is_main']) && $item['is_main']) {
                if ($hasMain) {
                    throw new RuntimeException('Only one main account allowed');
                }
                $hasMain = true;
            }
            return array_merge($defaultKeys, $item);
        });

        $existingRecords = $this->accountsRepository->oldContractorAccountIdsForContractor($this->resourceId);

        $newRecords = $accounts->pluck('id')->toArray();

        $recordsToDelete = array_diff($existingRecords->toArray(), $newRecords);

        if (!empty($recordsToDelete)) {
            $this->accountsRepository->deleteOldContractorAccountWhereGroupIds($recordsToDelete);
        }

        $this->accountsRepository->upsert($accounts->toArray());

    }

    private function manageContacts(array $contacts): void
    {
        $contacts = collect($contacts);

        $contacts = $contacts->map(function ($item) {

            if (!isset($item['id'])) {
                $item['id'] = $this->generateUuid();
            }

            $item['contractor_id'] = $this->resourceId;
            $item['created_at'] = now();
            $item['updated_at'] = now();

            return $item;
        });

        $existingRecords = $this->contactsRepository->oldContractorContactsIdsForContractor($this->resourceId);

        $newRecords = $contacts->pluck('id')->toArray();

        $recordsToDelete = array_diff($existingRecords->toArray(), $newRecords);

        if (!empty($recordsToDelete)) {
            $this->contactsRepository->deleteOldContractorContactsWhereContactsIds($recordsToDelete);
        }

        $this->contactsRepository->upsert($contacts->toArray());

    }

    private function manageGroups(array $contractorGroups): void
    {
        $contractorGroups = collect($contractorGroups);

        $existingRecords = $this->groupRepository->oldContractorGroupIdsForContractor($this->resourceId);

        $newRecords = $contractorGroups->pluck('group_id')->toArray();

        $recordsToDelete = array_diff($existingRecords->toArray(), $newRecords);

        if (!empty($recordsToDelete)) {
            $this->groupRepository->deleteOldContractorGroupWhereGroupIds($this->resourceId, $recordsToDelete);
        }

        $contractorGroups = $contractorGroups->map(function ($item) {

            $item['id'] = $this->generateUuid();

            $item['contractor_id'] = $this->resourceId;

            return $item;
        });

        $this->groupRepository->upsert($contractorGroups->toArray());

    }
}
