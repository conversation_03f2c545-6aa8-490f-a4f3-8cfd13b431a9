<?php

namespace App\Contracts\Policies\Directories;

use App\Models\User;
use App\Contracts\Policies\BaseResourcePolicyContract;

interface DiscountPolicyContract extends BaseResourcePolicyContract
{
    public function index(string $cabinetId): void;
    public function getSavings(User $user, string $resourceId): void;
    public function getProducts(User $user, string $resourceId): void;
    public function getGroups(User $user, string $resourceId): void;
    public function setStatus(User $user, string $resourceId): void;
}
