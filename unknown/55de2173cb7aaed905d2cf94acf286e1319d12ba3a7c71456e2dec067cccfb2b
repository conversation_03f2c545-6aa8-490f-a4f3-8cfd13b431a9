<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class WarehouseCellSizeDTO implements HasInsertArrayDtoContract, DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public string $name,
        public ?string $resourceId,
        public bool $unlimited_size = false,
        public ?string $height = null,
        public ?string $width = null,
        public ?string $length = null,
        public ?string $measurement_unit_size_id = null,
        public ?string $volume = null,
        public ?string $measurement_unit_volume_id = null,
        public ?bool $unlimited_load_capacity = false,
        public ?string $load_capacity = null,
        public ?string $measurement_unit_load_capacity_id = null
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,

            'cabinet_id' => $this->cabinet_id,
            'name' => $this->name,
            'unlimited_size' => $this->unlimited_size,
            'height' => $this->height,
            'width' => $this->width,
            'length' => $this->length,
            'measurement_unit_size_id' => $this->measurement_unit_size_id,
            'volume' => $this->volume,
            'measurement_unit_volume_id' => $this->measurement_unit_volume_id,
            'unlimited_load_capacity' => $this->unlimited_load_capacity,
            'load_capacity' => $this->load_capacity,
            'measurement_unit_load_capacity_id' => $this->measurement_unit_load_capacity_id,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            name: $data['name'],
            resourceId: $data['id'] ?? null,
            unlimited_size: $data['unlimited_size'] ?? false,
            height: $data['height'] ?? null,
            width: $data['width'] ?? null,
            length: $data['length'] ?? null,
            measurement_unit_size_id: $data['measurement_unit_size_id'] ?? null,
            volume: $data['volume'] ?? null,
            measurement_unit_volume_id: $data['measurement_unit_volume_id'] ?? null,
            unlimited_load_capacity: $data['unlimited_load_capacity'] ?? false,
            load_capacity: $data['load_capacity'] ?? null,
            measurement_unit_load_capacity_id: $data['measurement_unit_load_capacity_id'] ?? null
        );
    }
    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'unlimited_size' => $this->unlimited_size,
            'height' => $this->height,
            'width' => $this->width,
            'length' => $this->length,
            'measurement_unit_size_id' => $this->measurement_unit_size_id,
            'volume' => $this->volume,
            'measurement_unit_volume_id' => $this->measurement_unit_volume_id,
            'unlimited_load_capacity' => $this->unlimited_load_capacity,
            'load_capacity' => $this->load_capacity,
            'measurement_unit_load_capacity_id' => $this->measurement_unit_load_capacity_id
        ];
    }
}
