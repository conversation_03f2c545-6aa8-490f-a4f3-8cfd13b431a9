<?php

namespace App\Modules\Marketplaces\Contracts\Wildberries;

use Illuminate\Support\Collection;

interface WildberriesOrdersRepositoryContract
{
    /**
     * Получить заказ по ID
     *
     * @param string $id ID заказа
     * @return object|null Заказ
     */
    public function show(string $id): ?object;

    /**
     * Получить заказы по ID кабинета и фильтрам
     *
     * @param string $cabinetId ID кабинета
     * @param array $filters Фильтры
     * @return Collection Коллекция заказов
     */
    public function findByCabinetId(string $cabinetId, array $filters = []): Collection;

    /**
     * Получить заказы по ID интеграции
     *
     * @param string $integrationId ID интеграции
     * @param array $filters Фильтры
     * @return Collection Коллекция заказов
     */
    public function findByIntegrationId(string $integrationId, array $filters = []): Collection;

    /**
     * Создать заказ
     *
     * @param array $data Данные заказа
     * @return string ID созданного заказа
     */
    public function create(array $data): string;

    /**
     * Создать несколько заказов
     *
     * @param array $data Массив данных заказов
     * @return bool Результат создания
     */
    public function insert(array $data): bool;

    /**
     * Обновить заказ
     *
     * @param string $id ID заказа
     * @param array $data Данные заказа
     * @return bool Результат обновления
     */
    public function update(string $id, array $data): bool;

    /**
     * Обновить статус заказа
     *
     * @param string $id ID заказа
     * @param string $status Новый статус
     * @return bool Результат обновления
     */
    public function updateStatus(string $id, string $status): bool;

    /**
     * Подтвердить заказ
     *
     * @param string $id ID заказа
     * @return bool Результат подтверждения
     */
    public function confirm(string $id): bool;

    /**
     * Собрать заказ и создать заказ в УТ
     *
     * @param string $id ID заказа
     * @param array $data Дополнительные данные для создания заказа в УТ
     * @return string|null ID созданного заказа в УТ
     */
    public function collect(string $id, array $data = []): ?string;

    /**
     * Получить заказы по статусу
     *
     * @param string $cabinetId ID кабинета
     * @param string $status Статус заказа
     * @return Collection Коллекция заказов
     */
    public function findByStatus(string $cabinetId, string $status): Collection;

    /**
     * Получить новые заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция новых заказов
     */
    public function findNewOrders(string $cabinetId): Collection;

    /**
     * Получить заказы, ожидающие сборки
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция заказов, ожидающих сборки
     */
    public function findAwaitingCollectionOrders(string $cabinetId): Collection;

    /**
     * Получить заказы, ожидающие отгрузки
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция заказов, ожидающих отгрузки
     */
    public function findAwaitingShipmentOrders(string $cabinetId): Collection;

    /**
     * Получить спорные заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция спорных заказов
     */
    public function findDisputedOrders(string $cabinetId): Collection;

    /**
     * Получить заказы в доставке
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция заказов в доставке
     */
    public function findDeliveringOrders(string $cabinetId): Collection;

    /**
     * Получить доставленные заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция доставленных заказов
     */
    public function findDeliveredOrders(string $cabinetId): Collection;

    /**
     * Получить отмененные заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция отмененных заказов
     */
    public function findCanceledOrders(string $cabinetId): Collection;
}
