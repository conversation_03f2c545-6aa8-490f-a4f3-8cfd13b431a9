<?php

namespace Tests\Unit\Traits;

use App\Traits\SummaryPriceCalculator;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SummaryPriceCalculatorTest extends TestCase
{
    use RefreshDatabase;

    private $calculator;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a concrete class that uses the trait
        $this->calculator = new class () {
            use SummaryPriceCalculator;
        };
    }

    public function test_it_calculates_total_price_without_discount(): void
    {
        $item = [
            'price' => 100,
            'quantity' => 5,
            'discount' => 0
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(500, $result);
    }

    public function test_it_calculates_total_price_with_discount(): void
    {
        $item = [
            'price' => 100,
            'quantity' => 5,
            'discount' => 20 // 20% discount
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(400, $result);
    }

    public function test_it_handles_zero_quantity(): void
    {
        $item = [
            'price' => 100,
            'quantity' => 0,
            'discount' => 10
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(0, $result);
    }

    public function test_it_handles_zero_price(): void
    {
        $item = [
            'price' => 0,
            'quantity' => 5,
            'discount' => 10
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(0, $result);
    }

    public function test_it_handles_float_price(): void
    {
        $item = [
            'price' => 99.99,
            'quantity' => 2,
            'discount' => 0
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(199.98, $result);
    }

    public function test_it_handles_null_discount(): void
    {
        $item = [
            'price' => 100,
            'quantity' => 2,
            'discount' => null
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(200, $result);
    }

    public function test_it_handles_hundred_percent_discount(): void
    {
        $item = [
            'price' => 100,
            'quantity' => 5,
            'discount' => 100
        ];

        $result = $this->calculator->calculateSumPrice($item);

        $this->assertEquals(0, $result);
    }

    public function test_it_handles_fractional_discount(): void
    {
        $item = [
            'price' => 100,
            'quantity' => 1,
            'discount' => 33.33
        ];

        $result = $this->calculator->calculateSumPrice($item);

        // Из-за особенностей работы с float, используем delta
        $this->assertEqualsWithDelta(66.67, $result, 0.01);
    }
}
