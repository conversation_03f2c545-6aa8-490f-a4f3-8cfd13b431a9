<?php

namespace App\Services\Api\Internal\GoodsTransferService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use Illuminate\Support\Carbon;

class GoodsTransferDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public ?string $number,
        public ?string $resourceId,
        public string $employeeId,
        public string $departmentId,
        public bool $isCommon,
        public ?string $dateFrom,
        public ?string $statusId,
        public bool $held,
        public string $legalEntityId,
        public string $toWarehouseId,
        public string $fromWarehouseId,
        public string $currencyId,
        public string $currencyValue,
        public ?string $comment,
        public string $overheadCost,
        public string $totalPrice,
        public array $files = []
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'number' => $this->number,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'is_common' => $this->isCommon,
            'date_from' => $this->dateFrom,
            'status_id' => $this->statusId,
            'held' => $this->held,
            'legal_entity_id' => $this->legalEntityId,
            'to_warehouse_id' => $this->toWarehouseId,
            'from_warehouse_id' => $this->fromWarehouseId,
            'currency_id' => $this->currencyId,
            'currency_value' => $this->currencyValue,
            'comment' => $this->comment,
            'overhead_cost' => $this->overheadCost,
            'total_price' => $this->totalPrice
        ];
    }
    public function toUpdateArray(): array
    {
        return [
            'number' => $this->number,
            'date_from' => $this->dateFrom,
            'status_id' => $this->statusId,
            'held' => $this->held,
            'legal_entity_id' => $this->legalEntityId,
            'to_warehouse_id' => $this->toWarehouseId,
            'from_warehouse_id' => $this->fromWarehouseId,
            'currency_id' => $this->currencyId,
            'currency_value' => $this->currencyValue,
            'comment' => $this->comment,
            'overhead_cost' => $this->overheadCost,
            'total_price' => $this->totalPrice
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            number: $data['number'] ?? null,
            resourceId: $data['id'] ?? null,
            employeeId: $data['employee_id'],
            departmentId: $data['department_id'],
            isCommon: $data['is_common'] ?? false,
            dateFrom: $data['date_from'] ?? Carbon::now(),
            statusId: $data['status_id'] ?? null,
            held: $data['held'] ?? true,
            legalEntityId: $data['legal_entity_id'],
            toWarehouseId: $data['to_warehouse_id'],
            fromWarehouseId: $data['from_warehouse_id'],
            currencyId: $data['currency_id'],
            currencyValue: $data['currency_value'] ?? 1,
            comment: $data['comment'] ?? null,
            overheadCost: $data['overhead_cost'] ?? 0,
            totalPrice: $data['total_price'] ?? 0,
            files: $data['files'] ?? []
        );
    }
}
