<?php

namespace App\Modules\Marketplaces\Services\Actions;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetMarketplacesAction
{
    public function execute(string $cabinetId): Collection
    {
        return DB::table('ozon_integrations')
            ->unionAll(
                DB::table('wildberries_integrations')
                    ->select(
                        [
                            'id',
                            'cabinet_id',
                            'created_at',
                            'updated_at',
                            'shop_name',
                            DB::raw("'wildberries' as marketplace_type")
                        ]
                    )
            )
            ->select(
                [
                    'id',
                    'cabinet_id',
                    'created_at',
                    'updated_at',
                    'shop_name',
                    DB::raw("'ozon' as marketplace_type")
                ]
            )
            ->where('cabinet_id', $cabinetId)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
