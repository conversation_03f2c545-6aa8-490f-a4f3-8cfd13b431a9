<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Finances\IncomingPaymentItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\DTO\IncomingPaymentItemDTO;

readonly class IncomingPaymentItemPolicy implements IncomingPaymentItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof IncomingPaymentItemDTO) {
            return;
        }

        $payment = $this->authService->validateRelationAccess(
            'incoming_payments',
            $dto->incoming_payment_id,
            null,
            PermissionNameEnum::INCOMING_PAYMENTS->value,
            'update',
        );

        $this->authService->validateRelationAccess(
            'documents',
            $dto->document_id,
            $payment->cabinet_id,
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof IncomingPaymentItemDTO) {
            return;
        }

        // Проверяем права на acceptance_items, используя cabinet_id из acceptance
        $this->authService->validateRelationAccess(
            'incoming_payment_items',
            $dto->resourceId,
            null,
            PermissionNameEnum::INCOMING_PAYMENTS->value,
            'update',
            null,
            [
               'table' => 'incoming_payments',
               'field' => 'incoming_payment_id',
               'value' => $dto->resourceId
            ]
        );
    }

    public function index(string $paymentId): void
    {
        $this->authService->validateRelationAccess(
            'incoming_payments',
            $paymentId,
            null,
            PermissionNameEnum::INCOMING_PAYMENTS->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'incoming_payment_items',
            $resourceId,
            null,
            PermissionNameEnum::INCOMING_PAYMENTS->value,
            'view',
            null,
            [
                'table' => 'incoming_payments',
                'field' => 'incoming_payment_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'incoming_payment_items',
            $resourceId,
            null,
            PermissionNameEnum::ACCEPTANCES->value,
            'update',
            null,
            [
                'table' => 'incoming_payments',
                'field' => 'incoming_payment_id',
                'value' => $resourceId
            ]
        );
    }
}
