<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers;

use App\Contracts\Repositories\WarehouseCellSizesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehouseCellSizesGetHandler
{
    public function __construct(
        private WarehouseCellSizesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
