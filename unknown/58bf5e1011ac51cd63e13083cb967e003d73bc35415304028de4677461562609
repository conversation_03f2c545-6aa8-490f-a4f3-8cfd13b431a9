<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Purchases\VendorOrderServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderBulkCopyHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderBulkDeleteHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderBulkHeldHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderBulkUnheldHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderBulkUnwaitingHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderBulkWaitingHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderCreateHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderDeleteHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderGetHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderShowHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers\VendorOrderUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class VendorOrdersService implements VendorOrderServiceContract
{
    public function __construct(
        private VendorOrderCreateHandler $createHandler,
        private VendorOrderGetHandler $getHandler,
        private VendorOrderShowHandler $showHandler,
        private VendorOrderUpdateHandler $updateHandler,
        private VendorOrderDeleteHandler $deleteHandler,
        private VendorOrderBulkDeleteHandler $bulkDeleteHandler,
        private VendorOrderBulkHeldHandler $bulkHeldHandler,
        private VendorOrderBulkUnheldHandler $bulkUnheldHandler,
        private VendorOrderBulkWaitingHandler $bulkWaitingHandler,
        private VendorOrderBulkUnwaitingHandler $bulkUnwaitingHandler,
        private VendorOrderBulkCopyHandler $bulkCopyHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws BindingResolutionException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }

    public function bulkHeld(array $ids): void
    {
        $this->bulkHeldHandler->run($ids);
    }

    public function bulkUnheld(array $ids): void
    {
        $this->bulkUnheldHandler->run($ids);
    }

    public function bulkWaiting(array $ids): void
    {
        $this->bulkWaitingHandler->run($ids);
    }
    public function bulkUnwaiting(array $ids): void
    {
        $this->bulkUnwaitingHandler->run($ids);
    }

    public function bulkCopy(array $ids): void
    {
        $this->bulkCopyHandler->run($ids);
    }
}
