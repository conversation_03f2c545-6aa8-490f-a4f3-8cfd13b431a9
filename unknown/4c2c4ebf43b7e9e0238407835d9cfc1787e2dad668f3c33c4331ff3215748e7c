<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers;

use App\Contracts\Repositories\WarehouseGroupsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehouseGroupsGetHandler
{
    public function __construct(
        private WarehouseGroupsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
