<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceItemJob;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Bus;
use Symfony\Component\Process\Exception\InvalidArgumentException;

class AcceptanceItemsUpdateHandler
{
    use HasOrderedUuid;
    use AcceptanceTotalCalculator;

    private HasUpdateArrayDtoContract $dto;

    public function __construct(
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof AcceptanceItemDto) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }
        $this->dto = $dto;

        $item = $this->acceptanceItemsRepository->show($this->dto->resourceId);

        if (!$item) {
            throw new \RuntimeException('Item not found in handler');
        }

        $acceptance = $this->acceptanceRepository->show($item->acceptance_id);

        if (!$acceptance) {
            throw new \RuntimeException('Acceptance not found in handler');
        }

        // Если has_vat = false и vat_rate_id не указан, автоматически выбираем ставку "Без НДС"
        if ($acceptance->has_vat === false && $this->dto->vat_rate_id === null) {
            $noVatRate = $this->vatRatesRepository->getByRateAndCabinet(0, $acceptance->cabinet_id);
            if ($noVatRate) {
                $this->dto->vat_rate_id = $noVatRate->id;
            }
        }

        // Рассчитываем total_price и total_vat_sum
        $calculatedData = $this->calculateAcceptanceItemTotals($this->dto, $acceptance);

        // Устанавливаем рассчитанные значения в DTO
        $this->dto->totalVatSum = $calculatedData['total_vat_sum'];

        $this->acceptanceItemsRepository->update(
            $this->dto->resourceId,
            $this->dto->toUpdateArray(),
        );

        // Пересчитываем общую сумму приемки с учетом НДС
        $this->updateAcceptanceTotal($acceptance->id);

        Bus::dispatch(new RecalculationAfterUpdateAcceptanceItemJob($item, $this->dto));
    }

    private function calculateAcceptanceItemTotals(AcceptanceItemDto $dto, object $acceptance): array
    {
        // Получаем ставку НДС
        $vatRate = '0';
        if ($dto->vat_rate_id) {
            $vatRateEntity = $this->vatRatesRepository->show($dto->vat_rate_id);
            $vatRate = $vatRateEntity ? (string)$vatRateEntity->rate : '0';
        }

        // Базовая сумма = цена × количество
        $baseAmount = $this->multiply($dto->price, (string)$dto->quantity);

        // Применяем скидку
        $amountAfterDiscount = $this->applyDiscount($baseAmount, $dto->discount);

        // Рассчитываем НДС в зависимости от настроек
        if (!$acceptance->has_vat || $this->compare($vatRate, '0') == 0) {
            // Без НДС
            $totalPrice = $amountAfterDiscount;
            $totalVatSum = '0';
        } elseif ($acceptance->price_includes_vat) {
            // НДС включен в цену
            $vatCalculation = $this->extractVat($amountAfterDiscount, $vatRate);
            $totalPrice = $amountAfterDiscount;
            $totalVatSum = $vatCalculation['vat_amount'];
        } else {
            // НДС сверх цены
            $vatCalculation = $this->calculateVat($amountAfterDiscount, $vatRate);
            $totalPrice = $vatCalculation['total_amount'];
            $totalVatSum = $vatCalculation['vat_amount'];
        }

        return [
            'total_price' => $totalPrice,
            'total_vat_sum' => $totalVatSum
        ];
    }
}
