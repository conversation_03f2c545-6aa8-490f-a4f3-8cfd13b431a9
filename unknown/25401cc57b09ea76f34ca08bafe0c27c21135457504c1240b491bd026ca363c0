<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseWorkSchedulePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\DTO\WorkScheduleDTO;

readonly class WarehouseWorkSchedulePolicy implements WarehouseWorkSchedulePolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WorkScheduleDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinet_id);
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WorkScheduleDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            entity: 'warehouse_work_schedules',
            entityId: $dto->resourceId
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_work_schedules',
            entityId: $resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_work_schedules',
            entityId: $resourceId
        );
    }
}
