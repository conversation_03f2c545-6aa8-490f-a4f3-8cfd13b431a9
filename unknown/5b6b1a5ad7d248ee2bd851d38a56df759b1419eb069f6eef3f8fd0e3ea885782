<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\DTO\WarehouseCellGroupDTO;
use App\Traits\HasOrderedUuid;
use http\Exception\InvalidArgumentException;

class WarehouseCellGroupsUpdateHandler
{
    use HasOrderedUuid;

    private HasUpdateArrayDtoContract $dto;

    public function __construct(
        private readonly WarehouseCellGroupsRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseCellGroupDTO) {
            throw new InvalidArgumentException();
        }
        $this->dto = $dto;

        $resource = $this->repository->show($dto->resourceId);

        $this->repository->update($dto->resourceId, $dto->toUpdateArray());

        if ($resource->warehouse_id != $dto->warehouse_id) {
            $this->updateChildrenRecursive($dto->resourceId);
        }
    }

    private function updateChildrenRecursive(string $resourceId): void
    {
        foreach ($this->repository->getByParentId($resourceId) as $child) {
            $this->repository->update(
                $child->id,
                $this->dto->toUpdateArray()
            );
            $this->updateChildrenRecursive($child->id);
        }
    }
}
