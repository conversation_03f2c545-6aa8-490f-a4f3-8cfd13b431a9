<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class MeasurementUnitDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public string $cabinetId,
        public string $shortName,
        public ?string $code,
        public ?string $name,
        public string $conversionFactor,
        public ?string $groupId,
        public ?string $conversionFactorToOldBase = null,
        public ?string $resourceId
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'short_name' => $this->shortName,
            'code' => $this->code,
            'name' => $this->name,
            'conversion_factor' => $this->conversionFactor,
            'group_id' => $this->groupId,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'short_name' => $this->shortName,
            'code' => $this->code,
            'name' => $this->name,
            'conversion_factor' => $this->conversionFactor,
            'group_id' => $this->groupId,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'],
            shortName: $data['short_name'],
            code: $data['code'] ?? null,
            name: $data['name'] ?? null,
            conversionFactor: $data['conversion_factor'],
            groupId: $data['group_id'] ?? null,
            conversionFactorToOldBase: $data['conversion_factor_to_old_base'] ?? null,
            resourceId: $data['id'] ?? null
        );
    }
}
