<?php

namespace App\Http\Requests\Api\Internal\Dadata;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Other\SuggestsService\DTO\SuggestAddressDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AddressRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'query' => 'required|string|max:300',
            'language' => 'nullable|string|in:ru,en',
            'division' => 'nullable|string|in:ADMINISTRATIVE, MUNICIPAL',
            'locations.*.country_iso_code' => 'nullable|string',
            'locations.*.region_iso_code' => 'nullable|string',
            'locations.*.kladr_id' => 'nullable|numeric',
            'locations.*.region' => 'nullable|string',
            'locations.*.city' => 'nullable|string',
            'locations.*.area' => 'nullable|string',
            'locations.*.settlement' => 'nullable|string',
            'locations.*.street' => 'nullable|string',
            'locations.*.country' => 'nullable|string',
            'locations.*.region_type_full' => 'nullable|string',
            'locations.*.area_type_full' => 'nullable|string',
            'locations.*.city_type_full' => 'nullable|string',
            'locations.*.settlement_type_full' => 'nullable|string',
            'locations.*.street_type_full' => 'nullable|string',
            'locations.*.fias_id' => 'nullable|string',
            'locations_geo.*' => 'nullable|array',
            'locations_geo.*.lat' => 'required|decimal',
            'locations_geo.*.lon' => 'required|decimal',
            'locations_geo.*.radius_meters' => 'nullable|numeric|max:100000',
            'locations_boost.*' => 'nullable|array',
            'locations_boost.*.kladr_id' => 'nullable|numeric',
            'from_bound.*' => 'nullable|array',
            'from_bound.*.value' => 'nullable|in:country,region,area,city,settlement,street,house,flat',
            'to_bound.*.value' => 'nullable|in:country,region,area,city,settlement,street,house,flat',
            ];
    }

    public function toDTO(): SuggestAddressDTO
    {
        return SuggestAddressDTO::fromArray($this->validated());
    }
}
