<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AcceptanceBulkCopyHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly DocumentsRepositoryContract $documentsRepository,
        private readonly WarehouseItemsRepositoryContract $warehouseItemsRepository,
    ) {
    }

    public function run(array $resourceIds): array
    {
        $originalAcceptances = $this->acceptanceRepository->getWhereInIds($resourceIds);

        $newAcceptanceIds = [];
        $newAcceptances = [];
        $newDocuments = [];
        $now = Carbon::now();

        foreach ($originalAcceptances as $acceptance) {
            $newId = $this->generateUuid();
            $newAcceptanceIds[] = $newId;

            $documentNumberGenerator = new DocumentNumberGenerator(
                'acceptances',
                $acceptance->cabinet_id,
                $acceptance->number,
                $acceptance->legal_entity_id
            );
            $newNumber = $documentNumberGenerator->generateNumber();

            $newAcceptances[] = [
                'id' => $newId,
                'created_at' => $now,
                'updated_at' => $now,
                'cabinet_id' => $acceptance->cabinet_id,
                'employee_id' => $acceptance->employee_id,
                'department_id' => $acceptance->department_id,
                'number' => $newNumber,
                'date_from' => $acceptance->date_from,
                'status_id' => $acceptance->status_id,
                'held' => false, // Новые приемки создаются непроведенными
                'legal_entity_id' => $acceptance->legal_entity_id,
                'contractor_id' => $acceptance->contractor_id,
                'warehouse_id' => $acceptance->warehouse_id,
                'incoming_number' => $acceptance->incoming_number,
                'incoming_date' => $acceptance->incoming_date,
                'currency_id' => $acceptance->currency_id,
                'comment' => $acceptance->comment,
                'price_includes_vat' => $acceptance->price_includes_vat,
                'has_vat' => $acceptance->has_vat,
                'overhead_cost' => $acceptance->overhead_cost,
                'total_price' => $acceptance->total_price
            ];

            $newDocuments[] = [
                'documentable_id' => $newId,
                'documentable_type' => 'acceptances',
                'lft' => 1,
                'rgt' => 2,
                'parent_id' => null,
                'tree_id' => $this->generateUuid(),
                'cabinet_id' => $acceptance->cabinet_id,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }

        DB::transaction(function () use ($newAcceptances, $newDocuments, $originalAcceptances, $newAcceptanceIds) {
            $this->acceptanceRepository->insert($newAcceptances);
            $this->documentsRepository->insert($newDocuments);

            $this->copyAcceptanceItems($originalAcceptances, $newAcceptanceIds);
        });

        return $newAcceptanceIds;
    }

    private function copyAcceptanceItems(Collection $originalAcceptances, array $newAcceptanceIds): void
    {
        $newAcceptanceItems = [];
        $newWarehouseItems = [];
        $now = Carbon::now();

        foreach ($originalAcceptances as $index => $acceptance) {
            $items = $this->acceptanceItemsRepository->getByAcceptanceId($acceptance->id);

            foreach ($items as $item) {
                $newItemId = $this->generateUuid();

                // Подготавливаем данные новой позиции приемки
                $newAcceptanceItems[] = [
                    'id' => $newItemId,
                    'created_at' => $now,
                    'updated_at' => $now,
                    'acceptance_id' => $newAcceptanceIds[$index],
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'vat_rate_id' => $item->vat_rate_id,
                    'discount' => $item->discount,
                    'total_price' => $item->total_price,
                    'country_id' => $item->country_id,
                    'gtd_number' => $item->gtd_number,
                ];

                // Подготавливаем данные нового warehouse item
                $newWarehouseItems[] = [
                    'id' => $this->generateUuid(),
                    'created_at' => $now,
                    'updated_at' => $now,
                    'warehouse_id' => $acceptance->warehouse_id,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'acceptance_id' => $newAcceptanceIds[$index],
                    'batch_number' => $acceptance->number,
                    'unit_price' => $item->price,
                    'total_price' => $item->total_price,
                    'received_at' => $acceptance->date_from,
                    'status' => 'in_stock'
                ];
            }
        }

        if (!empty($newAcceptanceItems)) {
            $this->acceptanceItemsRepository->insert($newAcceptanceItems);
            $this->warehouseItemsRepository->create($newWarehouseItems);
        }
    }
}
