<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseStorageAreasRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\DTO\WarehouseStorageAreaDTO;
use App\Traits\HasOrderedUuid;
use http\Exception\InvalidArgumentException;
use Illuminate\Support\Facades\DB;

class WarehouseStorageAreasUpdateHandler
{
    use HasOrderedUuid;

    private string $resourceId;
    private WarehouseStorageAreaDTO $dto;

    public function __construct(
        private readonly WarehouseStorageAreasRepositoryContract $repository
    ) {
    }
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseStorageAreaDTO) {
            throw new InvalidArgumentException();
        }
        $this->dto = $dto;
        $this->resourceId = $dto->resourceId;

        $this->repository->update(
            $this->resourceId,
            $dto->toUpdateArray()
        );

        $this->updateCellsPivot();

        $this->updateProductsPivot();
    }

    public function checkAccessToCellsAndGenerateDataArray(): array
    {
        $resourceId = $this->resourceId;

        return array_map(static function ($cellId) use ($resourceId) {
            return [
                'cell_id' => $cellId,
                'storage_area_id' => $resourceId,
            ];
        }, array_unique($this->dto->cells_id));
    }

    private function updateCellsPivot(): void
    {
        $updateData = $this->checkAccessToCellsAndGenerateDataArray();

        DB::table('warehouse_storage_area_cells')
            ->where('storage_area_id', $this->resourceId)
            ->delete();

        if (!empty($updateData)) {
            DB::table('warehouse_storage_area_cells')
                ->insert($updateData);
        }
    }

    private function updateProductsPivot(): void
    {
        $updateData = $this->checkAccessToProductsAndGenerateDataArray();

        DB::table('warehouse_storage_area_products')
            ->where('storage_area_id', $this->resourceId)
            ->delete();

        if (!empty($updateData)) {
            DB::table('warehouse_storage_area_products')
                ->insert($updateData);
        }
    }

    private function checkAccessToProductsAndGenerateDataArray(): array
    {
        $resourceId = $this->resourceId;

        return array_map(static function ($productId) use ($resourceId) {
            return [
                'product_id' => $productId,
                'storage_area_id' => $resourceId,
            ];
        }, array_unique($this->dto->products_id));
    }

}
