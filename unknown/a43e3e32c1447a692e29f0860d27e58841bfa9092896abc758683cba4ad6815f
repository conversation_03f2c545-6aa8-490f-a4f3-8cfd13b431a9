<?php

namespace Tests\Unit\Rules;

use Tests\TestCase;
use App\Rules\Barcode;
use App\Enums\Api\Internal\BarcodeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BarcodeTest extends TestCase
{
    use RefreshDatabase;

    private Barcode $rule;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new Barcode();
    }

    public function test_it_validates_valid_ean13_barcode(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'barcode' => '4003301018398' // Валидный EAN-13
                ]
            ]
        ];

        $this->rule->setData($data);
        $validationPassed = true;
        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass for valid EAN-13');
    }

    public function test_it_validates_valid_ean8_barcode(): void
    {
        // Arrange
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN8->value,
                    'barcode' => '21500015'
                ]
            ]
        ];

        // Act
        $this->rule->setData($data);
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid EAN-8');
        $this->assertNull($errorMessage, 'No error message should be set for valid EAN-8');
        $this->assertEquals(8, strlen($data['barcodes'][0]['barcode']), 'Barcode should be exactly 8 digits long');
        $this->assertEquals(BarcodeEnum::EAN8->value, $data['barcodes'][0]['type'], 'Type should be EAN8');
    }

    public function test_it_fails_for_invalid_ean13_length(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'barcode' => '400330101839' // Неверная длина (12 цифр)
                ]
            ]
        ];

        $errorMessage = '';
        $this->rule->setData($data);
        $this->rule->validate('barcodes', null, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertNotEmpty($errorMessage, 'Error message should not be empty');
        $this->assertEquals('The :attribute 400330101839 EAN-13 must be 13 digits long.', $errorMessage);
    }

    public function test_it_fails_for_invalid_ean8_length(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN8->value,
                    'barcode' => '4003301' // Неверная длина (7 цифр)
                ]
            ]
        ];

        $errorMessage = '';
        $this->rule->setData($data);
        $this->rule->validate('barcodes', null, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertNotEmpty($errorMessage, 'Error message should not be empty');
        $this->assertEquals('The :attribute 4003301 EAN-8 must be 8 digits long.', $errorMessage);
    }

    public function test_it_fails_for_invalid_ean13_checksum(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'barcode' => '4003301018399' // Неверная контрольная сумма
                ]
            ]
        ];

        $errorMessage = '';
        $this->rule->setData($data);
        $this->rule->validate('barcodes', null, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertNotEmpty($errorMessage, 'Error message should not be empty');
        $this->assertEquals('The :attribute 4003301018399 EAN-13 code is invalid.', $errorMessage);
    }

    public function test_it_fails_for_invalid_ean8_checksum(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN8->value,
                    'barcode' => '40033011' // Неверная контрольная сумма
                ]
            ]
        ];

        $errorMessage = '';
        $this->rule->setData($data);
        $this->rule->validate('barcodes', null, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        $this->assertNotEmpty($errorMessage, 'Error message should not be empty');
        $this->assertEquals('The :attribute 40033011 EAN-8 code is invalid.', $errorMessage);
    }

    public function test_it_validates_multiple_barcodes(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'barcode' => '4003301018398'
                ],
                [
                    'type' => BarcodeEnum::EAN8->value,
                    'barcode' => '21500015'
                ]
            ]
        ];

        $this->rule->setData($data);
        $validationPassed = true;
        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass for multiple valid barcodes');
    }

    public function test_it_handles_missing_barcode_field(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value
                ]
            ]
        ];

        $this->rule->setData($data);
        $validationPassed = true;
        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass when barcode field is missing');
    }

    public function test_it_handles_missing_type_field(): void
    {
        $data = [
            'barcodes' => [
                [
                    'barcode' => '4003301018398'
                ]
            ]
        ];

        $this->rule->setData($data);
        $validationPassed = true;
        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass when type field is missing');
    }

    public function test_it_validates_case_sensitive_type_field(): void
    {
        $data = [
            'barcodes' => [
                [
                    'type' => strtoupper(BarcodeEnum::EAN13->value),
                    'barcode' => '4003301018398'
                ]
            ]
        ];

        $this->rule->setData($data);
        $validationPassed = true;
        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass with case-sensitive type field');
    }

    public function test_it_validates_empty_barcodes_array(): void
    {
        $data = [
            'barcodes' => []
        ];

        $this->rule->setData($data);
        $validationPassed = true;
        $this->rule->validate('barcodes', null, function ($message) use (&$validationPassed) {
            $validationPassed = false;
        });

        $this->assertTrue($validationPassed, 'Validation should pass with empty barcodes array');
    }
}
