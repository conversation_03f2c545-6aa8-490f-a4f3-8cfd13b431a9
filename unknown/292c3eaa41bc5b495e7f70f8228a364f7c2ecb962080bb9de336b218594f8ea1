<?php

namespace App\Entities;

class ProductAccountingFeatureEntity extends BaseEntity
{
    public static string $table = 'product_accounting_features';

    public static array $fields = [
        'id',
        'product_id',                   // id продукта
        'pack_type',                      // Фасовка - селект
        'type_accounting',              // Тип учета
        'accounting_series',            // Учет по сериям           boolean
        'product_siz_name_id',          // Наименование для Средства инд защиты - из БД
        'product_siz_type_id',          // Тип для средств инд защиты - из БД
        'product_type_code',            // Код вида продукции       string
        'container_capacity',           // Емкость тары, л.         integer
        'strength',                     // Крепость, максимум 100
        'excise',                       // Содержит акцизную марку  boolean
        'product_type_id',              // Тип продукции            enum
        'tnwed_id',                        // ТН ВЭД                   string
        'target_gender',                // Целевой пол              enum
        'type_production',              // Тип производства         enum
        'age_category',                 // Возрастная категория     enum
        'set',                          // Комплект                 boolean
        'partial_sale',                 // Частичная продажа        boolean
        'model',                        // Модель                   string
        'traceable',                    // Прослеживаемый           boolean
    ];
}
