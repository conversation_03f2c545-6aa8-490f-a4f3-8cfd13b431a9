<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Sales\ShipmentItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\Sales\ShipmentItemsService\DTO\ShipmentItemCalculateDTO;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;

readonly class ShipmentItemPolicy implements ShipmentItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ShipmentItemDTO) {
            return;
        }

        $shipment = $this->authService->validateRelationAccess(
            'shipments',
            $dto->shipmentId,
            null,
            PermissionNameEnum::SHIPMENTS->value,
            'update',
        );

        $product = $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $shipment->cabinet_id,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );
        if ($product->archived_at) {
            throw new AccessDeniedException('Product is archived');
        }

        if ($dto->vat_rate_id) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vat_rate_id,
                $shipment->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }
    }

    /**
     * @throws AccessDeniedException
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ShipmentItemDTO) {
            return;
        }

        // Проверяем права на acceptance_items, используя cabinet_id из acceptance
        $item = $this->authService->validateRelationAccess(
            'shipment_items',
            $dto->resourceId,
            null,
            PermissionNameEnum::SHIPMENTS->value,
            'update',
            null,
            [
                'table' => 'shipments',
                'field' => 'shipment_id',
                'value' => $dto->resourceId
            ]
        );

        if ($dto->vat_rate_id && $item->vat_rate_id !== $dto->vat_rate_id) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vat_rate_id,
                $item->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }
    }

    public function index(string $shipmentId): void
    {
        $this->authService->validateRelationAccess(
            'shipments',
            $shipmentId,
            null,
            PermissionNameEnum::SHIPMENTS->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'shipment_items',
            $resourceId,
            null,
            PermissionNameEnum::SHIPMENTS->value,
            'view',
            null,
            [
                'table' => 'shipments',
                'field' => 'shipment_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'shipment_items',
            $resourceId,
            null,
            PermissionNameEnum::SHIPMENTS->value,
            'update',
            null,
            [
                'table' => 'shipments',
                'field' => 'shipment_id',
                'value' => $resourceId
            ]
        );
    }

    public function calculateMetrics(ShipmentItemCalculateDTO $dto): void
    {
        $this->authService->hasAccessToCabinet(
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $dto->cabinetId,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );

        $this->authService->validateRelationAccess(
            'warehouses',
            $dto->warehouseId,
            $dto->cabinetId,
            PermissionNameEnum::WAREHOUSES->value,
            'view'
        );
    }
}
