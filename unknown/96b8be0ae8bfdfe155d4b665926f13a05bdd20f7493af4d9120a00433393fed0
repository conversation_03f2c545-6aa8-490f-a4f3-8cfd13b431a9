<?php

namespace App\Services\Api\Internal\Ozon\OzonService\Performance\Handlers;

use App\Services\Api\Internal\OzonService\Performance\Handlers\ParceFileExcelHelper;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTokenPerformanceCredential;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OzonePerformanceClientOrdersGenerateUUIDHelper
{
    use HasOrderedUuid;
    use HasTokenPerformanceCredential;

    // public function __construct(
    //     private ParceFileExcelHelper $parceFileHandler,
    // ) {
    // }

    public function run(string $uuid, string $cabinetId, string $departmentId, string $employeeId, string $ozonCredentialId): object
    {

        $accessToken = $this->getToken($ozonCredentialId);

        $statistic = OzonApiPerformanceOrdersGenerateUUIDRequestHelper::sendOrdersGenerateUUIDRequest(
            $uuid,
            $accessToken
        );

        Log::info("Отправили запрос OzonePerformanceClientOrdersGenerateUUIDHelper");


        if ($statistic['state'] == 'OK' && isset($statistic['link'])) {

            $response = OzonApiPerformanceOrdersGenerateUUIDReportRequestHelper::sendOrdersGenerateUUIDReportRequest(
                $uuid,
                $accessToken
            );

            if (isset($response['error'])) {

                Log::error(
                    "Ошибка в OzonePerformanceClientOrdersGenerateUUIDHelper:",
                    [
                    $response['error'],
                    'ozonCredentialId' => $ozonCredentialId,
                ]
                );

            }


            $returns = new Collection($response);

            $objects = $this->prepareOrderData($cabinetId, $departmentId, $employeeId, $campaignId, $ozonCredentialId, $returns);

            $this->saveProcessedData($objects);


        }

        return $statistic;
    }

    private function prepareOrderData(string $cabinetId, string $departmentId, string $employeeId, string $campaignId, string $ozonCredentialId, $returns): array
    {
        return [
            'id' => $this->generateUuid(),
            'ozon_performance_credential_id' => $ozonCredentialId,
            'cabinet_id' => $cabinetId,
            'department_id' => $departmentId,
            'employee_id' => $employeeId,
            'campaign_id' => $campaignId,
            'title' => $returns['title'],
            'moneySpent' => $returns['moneySpent'] ?? null,
            'corrections' => $returns['corrections'] ?? null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    private function saveProcessedData(array $objects): void
    {
        if (!empty($objects)) {

            DB::table('ozon_performance_client_statistics_orders_generate')->upsert(
                $objects,
                ['campaign_id', 'title'],
                [
                    'updated_at',
                ]
            );

        }
    }
}
