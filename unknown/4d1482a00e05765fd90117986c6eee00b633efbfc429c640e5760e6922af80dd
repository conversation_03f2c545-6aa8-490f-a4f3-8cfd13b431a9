<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\Traits;

use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Traits\PrecisionCalculator;
use Illuminate\Support\Facades\DB;

/**
 * Трейт для расчета общей суммы заказа поставщика с учетом НДС
 * Использует BcMath для точных расчетов с varchar полями
 */
trait VendorOrderTotalCalculator
{
    use PrecisionCalculator;

    /**
     * Пересчитывает общую сумму заказа поставщика на основе позиций с учетом НДС
     *
     * @param string $orderId ID заказа
     * @return string Общая сумма заказа
     */
    public function recalculateVendorOrderTotal(string $orderId): string
    {
        $order = DB::table('vendor_orders')->where('id', $orderId)->first();
        
        if (!$order) {
            return '0';
        }

        $items = DB::table('vendor_order_items as voi')
            ->leftJoin('vat_rates as vr', 'voi.vat_rate_id', '=', 'vr.id')
            ->where('voi.order_id', $orderId)
            ->select(['voi.price_in_currency', 'voi.quantity', 'voi.discount', 'vr.rate as vat_rate'])
            ->get();

        $totalSum = '0';

        foreach ($items as $item) {
            $itemTotal = $this->calculateVendorOrderItemTotal(
                (string)($item->price_in_currency ?? '0'),
                (string)($item->quantity ?? '1'),
                (string)($item->discount ?? '0'),
                (string)($item->vat_rate ?? '0'),
                $order->price_includes_vat,
                $order->has_vat
            );

            $totalSum = $this->add($totalSum, $itemTotal);
        }

        return $totalSum;
    }

    /**
     * Рассчитывает итоговую сумму позиции заказа поставщика с учетом НДС
     *
     * @param string $priceInCurrency Цена в валюте
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string $vatRate Ставка НДС
     * @param bool $priceIncludesVat Цена включает НДС
     * @param bool $hasVat Документ с НДС
     * @return string Итоговая сумма позиции
     */
    public function calculateVendorOrderItemTotal(
        string $priceInCurrency,
        string $quantity,
        string $discount,
        string $vatRate,
        bool $priceIncludesVat,
        bool $hasVat
    ): string {
        // Рассчитываем сумму без скидки
        $subtotal = $this->multiply($priceInCurrency, $quantity);

        // Применяем скидку
        if ($this->compare($discount, '0') > 0) {
            $discountPercent = $this->divide($discount, '100');
            $discountAmount = $this->multiply($subtotal, $discountPercent);
            $subtotal = $this->subtract($subtotal, $discountAmount);
        }

        // Если документ без НДС, возвращаем сумму без НДС
        if (!$hasVat) {
            return $subtotal;
        }

        // Если ставка НДС равна 0, возвращаем сумму без НДС
        if ($this->compare($vatRate, '0') === 0) {
            return $subtotal;
        }

        $vatDecimal = $this->divide($vatRate, '100');

        if ($priceIncludesVat) {
            // Цена включает НДС - возвращаем сумму как есть
            return $subtotal;
        } else {
            // Цена без НДС - добавляем НДС к цене
            $vatAmount = $this->multiply($subtotal, $vatDecimal);
            return $this->add($subtotal, $vatAmount);
        }
    }

    /**
     * Рассчитывает итоговую сумму позиции с учетом скидки (старый метод для совместимости)
     *
     * @param string $amountInBase Сумма в базовой валюте
     * @param string $discount Скидка в процентах
     * @return string Итоговая сумма позиции
     */
    public function calculateItemTotal(string $amountInBase, string $discount): string
    {
        if ($this->compare($discount, '0') === 0) {
            return $amountInBase;
        }

        $discountPercent = $this->divide($discount, '100');
        $discountAmount = $this->multiply($amountInBase, $discountPercent);

        return $this->subtract($amountInBase, $discountAmount);
    }

    /**
     * Обновляет общую сумму заказа в базе данных
     *
     * @param string $orderId ID заказа
     * @return void
     */
    public function updateVendorOrderTotal(string $orderId): void
    {
        $totalPrice = $this->recalculateVendorOrderTotal($orderId);

        DB::table('vendor_orders')
            ->where('id', $orderId)
            ->update([
                'total_price' => $totalPrice,
                'updated_at' => now()
            ]);
    }

    /**
     * Получает информацию о НДС для заказа поставщика
     *
     * @param string $orderId ID заказа
     * @param string|null $vatRateId ID ставки НДС
     * @return array
     */
    public function getVatInfo(string $orderId, ?string $vatRateId = null): array
    {
        $order = DB::table('vendor_orders')
            ->where('id', $orderId)
            ->select(['has_vat', 'price_includes_vat'])
            ->first();

        if (!$order) {
            return [
                'vat_rate' => '0',
                'price_includes_vat' => true,
                'has_vat' => true
            ];
        }

        $vatRate = '0';
        if ($vatRateId) {
            $vatRateRepository = app(VatRatesRepositoryContract::class);
            $vatRateRecord = $vatRateRepository->show($vatRateId);
            $vatRate = $vatRateRecord?->rate ?? '0';
        }

        return [
            'vat_rate' => $vatRate,
            'price_includes_vat' => $order->price_includes_vat,
            'has_vat' => $order->has_vat
        ];
    }
}
