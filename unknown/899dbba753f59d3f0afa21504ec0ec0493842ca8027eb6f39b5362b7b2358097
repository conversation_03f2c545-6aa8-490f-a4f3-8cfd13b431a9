<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\DiscountPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\References\DiscountsService\DTO\DiscountDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class DiscountPolicy implements DiscountPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DiscountDTO) {
            return;
        }

        // Проверяем доступ к кабинету
        $this->authService->hasAccessToCabinet($dto->cabinetId);

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        if ($dto->cabinetPriceId) {
            $this->authService->validateRelationAccess(
                'cabinet_prices',
                $dto->cabinetPriceId,
                $dto->cabinetId
            );
        }

        if (isset($dto->contractorGroups)) {

            $contractorGroups = collect($dto->contractorGroups);

            $this->authService->validateResourcesAccess(
                'contractor_groups',
                $dto->cabinetId,
                $contractorGroups->pluck('group_id')->toArray(),
            );
        }

        // Если есть продукты и они не для всех (productsServices = true), проверяем доступ к ним
        if (!empty($dto->products)) {
            $productIds = collect($dto->products)->pluck('product_id');
            $this->authService->validateProductsAccess($productIds->toArray(), $dto->cabinetId);
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DiscountDTO) {
            return;
        }

        $this->authService->init();

        // Проверяем доступ к кабинету
        $this->authService->hasAccessToCabinet($dto->cabinetId);

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        $discount = $this->authService->validateRelationAccess(
            'discounts',
            $dto->resourceId,
            null,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'update',
        );

        if ($dto->cabinetPriceId && $dto->cabinetPriceId != $discount->cabinet_price_id) {
            $this->authService->validateRelationAccess(
                'cabinet_prices',
                $dto->cabinetPriceId,
                $dto->cabinetId
            );
        }

        if (isset($dto->contractorGroups)) {

            $contractorGroups = collect($dto->contractorGroups);

            $this->authService->validateResourcesAccess(
                'contractor_groups',
                $dto->cabinetId,
                $contractorGroups->pluck('group_id')->toArray(),
            );
        }

    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('discounts', $resourceId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('discounts', $resourceId);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    public function getSavings(User $user, string $resourceId): void
    {
        $this->authService->init();
        $this->authService->validateRelationAccess('discounts', $resourceId);
    }

    public function getProducts(User $user, string $resourceId): void
    {
        $this->authService->init();
        $this->authService->validateRelationAccess('discounts', $resourceId);
    }

    public function getGroups(User $user, string $resourceId): void
    {
        $this->authService->init();
        $this->authService->validateRelationAccess('discounts', $resourceId);
    }

    public function setStatus(User $user, string $resourceId): void
    {
        $this->authService->init();
        $this->authService->validateRelationAccess('discounts', $resourceId);
    }
}
