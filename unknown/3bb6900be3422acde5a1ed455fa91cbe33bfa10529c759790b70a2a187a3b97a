<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Helpers\CustomerOrderCreator;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Helpers\SupplyValidator;
use App\Modules\Marketplaces\Services\Wildberries\DTO\OrderDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Экшн для добавления заказа в поставку Wildberries
 */
readonly class AddOrderToSupplyAction
{
    use HasOrderedUuid;

    /**
     * @param SupplyValidator $supplyValidator Валидатор поставок
     * @param CustomerOrderCreator $customerOrderCreator Создатель заказов клиентов
     */
    public function __construct(
        private SupplyValidator $supplyValidator,
        private CustomerOrderCreator $customerOrderCreator
    ) {
    }

    /**
     * Добавление заказа в поставку
     *
     * @param OrderDTO $order Данные заказа
     * @param string $supplyId ID поставки
     * @param string $token Токен API
     * @return void
     *
     * @throws NotFoundException Если поставка не найдена
     * @throws WBSellerException При ошибке API Wildberries
     * @throws RuntimeException При ошибках валидации
     */
    public function run(OrderDTO $order, string $supplyId, string $token): void
    {
        $supply = $this->supplyValidator->validateSupply($supplyId);
        $this->supplyValidator->validateOrderInSupply($order, $supply);

        $this->deleteOldRelationsToSupplyAndBoxes($order);
        $api = new API(['masterkey' => $token]);
        $api->Marketplace()->addSupplyOrder($supply->supply_id, $order->wbNumber);

        $this->updateSupplyCargoTypeIfNeeded($supply, (string)$order->cargoType);

        $this->updateOrderStatusIfNeeded($order);

        $this->createSupplyOrderRelation($supplyId, $order->id);

        $customerOrderId = $this->customerOrderCreator->createCustomerOrder($order);

        $this->updateCustomerOrderRelationInOrder($order->id, $customerOrderId);
    }

    /**
     * Обновляет тип груза поставки, если он не установлен
     *
     * @param object $supply Данные поставки
     * @param string $cargoType Тип груза
     * @return void
     */
    private function updateSupplyCargoTypeIfNeeded(object $supply, string $cargoType): void
    {
        if (!$supply->cargo_type) {
            DB::table('wildberries_supplies')
                ->where('id', $supply->id)
                ->update([
                    'cargo_type' => $cargoType,
                    'updated_at' => Carbon::now(),
                ]);
        }
    }

    /**
     * Обновляет статус заказа, если он не "confirm"
     *
     * @param OrderDTO $order Данные заказа
     * @return void
     */
    private function updateOrderStatusIfNeeded(OrderDTO $order): void
    {
        if ($order->supplierStatus !== 'confirm') {
            DB::table('wildberries_fbs_orders')
                ->where('id', $order->id)
                ->update([
                    'supplier_status' => 'confirm',
                    'updated_at' => Carbon::now(),
                ]);
        }
    }

    /**
     * Создает связь заказа с поставкой
     *
     * @param string $supplyId ID поставки
     * @param string $orderId ID заказа
     * @return void
     */
    private function createSupplyOrderRelation(string $supplyId, string $orderId): void
    {
        $id = $this->generateUuid();
        DB::table('wildberries_supply_orders')
            ->insert([
                'id' => $id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'supply_id' => $supplyId,
                'order_id' => $orderId,
                'requires_reshipping' => false,
            ]);
    }

    /**
     * @param string $orderId
     * @param string $customerOrderId
     * @return void
     */
    public function updateCustomerOrderRelationInOrder(string $orderId, string $customerOrderId): void
    {
        DB::table('wildberries_fbs_orders')
            ->where('id', $orderId)
            ->update([
                'customer_order_id' => $customerOrderId
            ]);
    }

    private function deleteOldRelationsToSupplyAndBoxes(OrderDTO $order): void
    {
        DB::table('wildberries_supply_orders')
            ->where('order_id', $order->id)
            ->delete();
        DB::table('wildberries_box_orders')
            ->where('order_id', $order->id)
            ->delete();
    }
}
