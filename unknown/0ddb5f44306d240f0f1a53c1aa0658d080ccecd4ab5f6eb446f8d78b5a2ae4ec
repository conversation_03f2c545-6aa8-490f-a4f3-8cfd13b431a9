<?php

namespace App\Services\Api\Internal\References\GlobalCurrenciesService\Handlers;

use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\Exceptions\NotFoundException;

class CurrenciesShowHandler
{
    public function __construct(
        private readonly GlobalCurrenciesRepositoryContract $repository
    ) {
    }

    /**
     * @throws NotFoundException
     */
    public function run(string $resourceId): ?object
    {
        $currencies = $this->repository->firstById($resourceId);

        if (!$currencies) {
            throw new NotFoundException();
        }

        return $currencies;
    }
}
