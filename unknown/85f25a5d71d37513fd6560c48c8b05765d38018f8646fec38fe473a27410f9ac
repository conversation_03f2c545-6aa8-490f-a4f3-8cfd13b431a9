<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use Illuminate\Support\Carbon;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EmployeeTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_employees_list(): void
    {
        // Создаем 3 сотрудника для нашего кабинета
        foreach (Employee::factory()->count(3)->create() as $employee) {
            CabinetEmployee::factory()->create([
                'employee_id' => $employee->id,
                'cabinet_id' => $this->cabinet->id
            ]);
        }

        // Создаем сотрудника для другого кабинета
        $otherEmployee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $otherEmployee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'archived_at',
                        'user_id',
                        'lastname',
                        'firstname',
                        'patronymic',
                        'telephone',
                        'email',
                        'status',
                        'job_number',
                        'citizenship',
                        'gender',
                        'inn',
                        'id_card',
                        'passport_series',
                        'passport_number',
                        'passport_issue_date',
                        'who_issued_passport',
                        'division_code',
                        'registration_address',
                        'temporary_registration_address',
                        'driver_license_series',
                        'driver_license_number',
                        'driver_license_issue_date',
                        'driver_license_category',
                        'military_card',
                        'hire_date',
                        'dismissal_date',
                        'position',
                        'salary',
                        'labor_fund',
                        'planned_advance',
                        'work_schedule',
                        'role_id',
                        'department_id',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 4 записи (3 созданных + 1 из setUp)
        $response->assertJsonCount(4, 'data');

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_cannot_access_other_cabinet_employees(): void
    {
        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Создаем тестовых сотрудников
        $employees = Employee::factory()->count(2)->create();
        foreach ($employees as $employee) {
            CabinetEmployee::factory()->create([
                'employee_id' => $employee->id,
                'cabinet_id' => $this->cabinet->id
            ]);
        }

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей (2 созданных + 1 из setUp)
        $response->assertJsonCount(3, 'data');
    }

    public function test_index_filter_inn_equals(): void
    {
        // Создаем сотрудников с одинаковым ИНН в разных кабинетах
        $employee1 = Employee::factory()->create(['inn' => '123456789012']);
        $employee2 = Employee::factory()->create(['inn' => '987654321098']);
        $employeeOtherCabinet = Employee::factory()->create(['inn' => '123456789012']); // Такой же ИНН в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'value' => '123456789012',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals('123456789012', $response->json('data.0.inn'));

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_inn_not_equal(): void
    {
        // Создаем сотрудников с разными ИНН
        $employee1 = Employee::factory()->create(['inn' => '123456789012']);
        $employee2 = Employee::factory()->create(['inn' => '987654321098']);
        $employeeOtherCabinet = Employee::factory()->create(['inn' => '987654321098']); // Такой же ИНН в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'value' => '123456789012',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что в результатах нет сотрудника с ИНН 123456789012
        foreach ($responseData as $employee) {
            $this->assertNotEquals('123456789012', $employee['inn']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_inn_empty(): void
    {
        // Создаем сотрудников с ИНН и без в разных кабинетах
        $employee1 = Employee::factory()->create(['inn' => null]);
        $employee2 = Employee::factory()->create(['inn' => '987654321098']);
        $employeeOtherCabinet = Employee::factory()->create(['inn' => null]); // Сотрудник без ИНН в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что все сотрудники в результатах не имеют ИНН
        foreach ($responseData as $employee) {
            $this->assertNull($employee['inn']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_inn_not_empty(): void
    {
        // Создаем сотрудников с ИНН и без в разных кабинетах
        $employee1 = Employee::factory()->create(['inn' => null]);
        $employee2 = Employee::factory()->create(['inn' => '987654321098']);
        $employeeOtherCabinet = Employee::factory()->create(['inn' => '987654321098']); // Сотрудник с ИНН в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что все сотрудники в результатах имеют ИНН
        foreach ($responseData as $employee) {
            $this->assertNotNull($employee['inn']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_phone_equals(): void
    {
        // Создаем сотрудников с одинаковым телефоном в разных кабинетах
        $employee1 = Employee::factory()->create(['telephone' => '+79001234567']);
        $employee2 = Employee::factory()->create(['telephone' => '+79009876543']);
        $employeeOtherCabinet = Employee::factory()->create(['telephone' => '+79001234567']); // Такой же телефон в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'value' => '+79001234567',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals('+79001234567', $response->json('data.0.telephone'));

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_phone_not_equal(): void
    {
        // Создаем сотрудников с разными телефонами в разных кабинетах
        $employee1 = Employee::factory()->create(['telephone' => '+79001234567']);
        $employee2 = Employee::factory()->create(['telephone' => '+79009876543']);
        $employeeOtherCabinet = Employee::factory()->create(['telephone' => '+79009876543']); // Такой же телефон в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'value' => '+79001234567',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        // Проверяем что в результатах нет сотрудника с телефоном +79001234567
        foreach ($response->json('data') as $employee) {
            $this->assertNotEquals('+79001234567', $employee['telephone']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_phone_empty(): void
    {
        // Создаем сотрудников с телефоном и без в разных кабинетах
        $employee1 = Employee::factory()->create(['telephone' => null]);
        $employee2 = Employee::factory()->create(['telephone' => '+79009876543']);
        $employeeOtherCabinet = Employee::factory()->create(['telephone' => null]); // Сотрудник без телефона в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что все сотрудники в результатах не имеют телефона
        foreach ($responseData as $employee) {
            $this->assertNull($employee['telephone']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_phone_not_empty(): void
    {
        // Создаем сотрудников с телефоном и без в разных кабинетах
        $employee1 = Employee::factory()->create(['telephone' => null]);
        $employee2 = Employee::factory()->create(['telephone' => '+79009876543']);
        $employeeOtherCabinet = Employee::factory()->create(['telephone' => '+79009876543']); // Сотрудник с телефоном в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что все сотрудники в результатах имеют телефон
        foreach ($responseData as $employee) {
            $this->assertNotNull($employee['telephone']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_email_equals(): void
    {
        // Создаем сотрудников с одинаковым email в разных кабинетах
        $employee1 = Employee::factory()->create(['email' => '<EMAIL>']);
        $employee2 = Employee::factory()->create(['email' => '<EMAIL>']);
        $employeeOtherCabinet = Employee::factory()->create(['email' => '<EMAIL>']); // Такой же email в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'value' => '<EMAIL>',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals('<EMAIL>', $response->json('data.0.email'));

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_email_not_equal(): void
    {
        // Создаем сотрудников с разными email
        $employee1 = Employee::factory()->create(['email' => '<EMAIL>']);
        $employee2 = Employee::factory()->create(['email' => '<EMAIL>']);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'value' => '<EMAIL>',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что в результатах нет сотрудника с email <EMAIL>
        foreach ($responseData as $employee) {
            $this->assertNotEquals('<EMAIL>', $employee['email']);
        }
    }

    public function test_index_filter_email_empty(): void
    {
        // Создаем сотрудников с email и без
        $employee2 = Employee::factory()->create(['email' => '<EMAIL>']);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        // Проверяем что все сотрудники в результатах не имеют email
        $response->assertJsonCount(0, 'data');
    }

    public function test_index_filter_email_not_empty(): void
    {
        // Создаем сотрудников с email и без
        $employee2 = Employee::factory()->create(['email' => '<EMAIL>']);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_filter_position_equals(): void
    {
        // Создаем сотрудников с одинаковой должностью в разных кабинетах
        $employee1 = Employee::factory()->create(['position' => 'Manager']);
        $employee2 = Employee::factory()->create(['position' => 'Developer']);
        $employeeOtherCabinet = Employee::factory()->create(['position' => 'Manager']); // Такая же должность в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'position' => [
                    'value' => 'Manager',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals('Manager', $response->json('data.0.position'));

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_position_not_equal(): void
    {
        // Создаем сотрудников с разными должностями в разных кабинетах
        $employee1 = Employee::factory()->create(['position' => 'Manager']);
        $employee2 = Employee::factory()->create(['position' => 'Developer']);
        $employeeOtherCabinet = Employee::factory()->create(['position' => 'Developer']); // Такая же должность в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'position' => [
                    'value' => 'Manager',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        // Проверяем что в результатах нет сотрудника с должностью Manager
        foreach ($response->json('data') as $employee) {
            $this->assertNotEquals('Manager', $employee['position']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_position_empty(): void
    {
        // Создаем сотрудников с должностью и без в разных кабинетах
        $employee1 = Employee::factory()->create(['position' => null]);
        $employee2 = Employee::factory()->create(['position' => 'Manager']);
        $employeeOtherCabinet = Employee::factory()->create(['position' => null]); // Сотрудник без должности в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'position' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что все сотрудники в результатах не имеют должности
        foreach ($responseData as $employee) {
            $this->assertNull($employee['position']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_position_not_empty(): void
    {
        // Создаем сотрудников с должностью и без в разных кабинетах
        $employee1 = Employee::factory()->create(['position' => null]);
        $employee2 = Employee::factory()->create(['position' => 'Manager']);
        $employeeOtherCabinet = Employee::factory()->create(['position' => 'Manager']); // Сотрудник с должностью в другом кабинете

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'position' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $responseData = $response->json('data');

        // Проверяем что все сотрудники в результатах имеют должность
        foreach ($responseData as $employee) {
            $this->assertNotNull($employee['position']);
        }

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($responseData as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_search(): void
    {
        $name = $this->faker->name;
        // Создаем сотрудников с одинаковыми данными в разных кабинетах
        $employee1 = Employee::factory()->create([
            'firstname' => $name,
            'lastname' => 'Doe',
            'email' => '<EMAIL>'
        ]);
        $employee2 = Employee::factory()->create([
            'firstname' => 'Jane',
            'lastname' => 'Smith',
            'email' => '<EMAIL>'
        ]);
        $employeeOtherCabinet = Employee::factory()->create([
            'firstname' => $name, // Такое же имя в другом кабинете
            'lastname' => 'Doe',
            'email' => '<EMAIL>'
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeOtherCabinet->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => $name
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($name, $response->json('data.0.firstname'));

        // Проверяем что все записи принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_index_filter_updated_at(): void
    {
        // Создаем сотрудников с разными датами обновления
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Обновляем время для первого сотрудника
        $employee1->updated_at = now()->subDays(5);
        $employee1->save();

        // Обновляем время для второго сотрудника
        $employee2->updated_at = now()->subDays(1);
        $employee2->save();

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'updated_at' => [
                    'from' => now()->subDays(3)->format('d.m.Y H:i'),
                    'to' => now()->format('d.m.Y H:i')
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($employee2->id, $response->json('data.0.id'));
    }

    public function test_index_filter_show_only_archived(): void
    {
        // Создаем архивных и неархивных сотрудников
        $employee1 = Employee::factory()->create(['archived_at' => Carbon::now()]);
        $employee2 = Employee::factory()->create(['archived_at' => null]);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee1->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee2->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => [
                    'value' => ShowOnlyEnum::ARCHIVED->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        // Проверяем что все сотрудники в результатах архивные
        foreach ($response->json('data') as $employee) {
            $this->assertNotEquals(null, $employee['archived_at']);
        }
    }

    public function test_index_filter_show_only_all(): void
    {
        // Создаем архивных и неархивных сотрудников
        $employee3 = Employee::factory()->create(['archived_at' => Carbon::now()]);
        $employee4 = Employee::factory()->create(['archived_at' => null]);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee3->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee4->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/employees?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => [
                    'value' => ShowOnlyEnum::ALL->value
                ]
            ]
        ]));

        $response->assertStatus(200);

        // Проверяем что получили все записи (2 созданных + 1 из setUp)
        $response->assertJsonCount(3, 'data');
    }

    public function test_can_update_employee(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'lastname' => 'Новая',
            'firstname' => 'Фамилия',
            'patronymic' => 'Отчество',
            'telephone' => '+79001234567',
            'email' => '<EMAIL>',
            'status' => 'active',
            'position' => 'Новая должность',
            'inn' => '123456789012',
            'salary' => 100000,
            'gender' => 'm',
            'citizenship' => 'РФ',
            'job_number' => 'ABC123',
            'hire_date' => '2024-01-01',
            'work_schedule' => [
                'monday_from' => '09:00',
                'monday_to' => '18:00',
                'tuesday_from' => '09:00',
                'tuesday_to' => '18:00',
                'wednesday_from' => '09:00',
                'wednesday_to' => '18:00',
                'thursday_from' => '09:00',
                'thursday_to' => '18:00',
                'friday_from' => '09:00',
                'friday_to' => '17:00'
            ]
        ];

        $response = $this->putJson("/api/internal/employees/{$employee->id}", $updateData);

        $response->assertStatus(204);

        // Проверяем что данные обновились в БД
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'lastname' => 'Новая',
            'firstname' => 'Фамилия',
            'patronymic' => 'Отчество',
            'telephone' => '+79001234567',
            'email' => '<EMAIL>',
            'status' => 'active',
            'position' => 'Новая должность',
            'inn' => '123456789012',
            'salary' => 100000,
            'gender' => 'm',
            'citizenship' => 'РФ',
            'job_number' => 'ABC123',
            'hire_date' => '2024-01-01'
        ]);
    }

    public function test_cannot_update_employee_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'lastname' => 'Новая',
            'firstname' => 'Фамилия',
            'email' => '<EMAIL>',
            'status' => 'active'
        ];

        $response = $this->putJson("/api/internal/employees/{$employee->id}", $updateData);

        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseMissing('employees', [
            'id' => $employee->id,
            'lastname' => 'Новая',
            'firstname' => 'Фамилия'
        ]);
    }

    public function test_cannot_update_employee_with_invalid_data(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'lastname' => '', // Пустая фамилия
            'firstname' => '', // Пустое имя
            'email' => 'invalid-email', // Неверный формат email
            'status' => 'invalid-status', // Неверный статус
            'gender' => 'invalid', // Неверный пол
            'salary' => 'not-a-number', // Неверный формат зарплаты
            'work_schedule' => [
                'monday_from' => 'invalid-time', // Неверный формат времени
                'monday_to' => '25:00' // Неверное время
            ]
        ];

        $response = $this->putJson("/api/internal/employees/{$employee->id}", $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'lastname',
                'firstname',
                'email',
                'status',
                'gender',
                'salary',
                'work_schedule.monday_from',
                'work_schedule.monday_to'
            ]);
    }

    public function test_cannot_update_non_existent_employee(): void
    {
        $fakeId = $this->faker->uuid;

        $updateData = [
            'lastname' => 'Новая',
            'firstname' => 'Фамилия',
            'email' => '<EMAIL>',
            'status' => 'active'
        ];

        $response = $this->putJson("/api/internal/employees/{$fakeId}", $updateData);

        $response->assertStatus(404);
    }

    public function test_can_update_employee_with_minimal_required_fields(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'lastname' => 'Новая',
            'firstname' => 'Фамилия',
            'email' => '<EMAIL>',
            'status' => 'active'
        ];

        $response = $this->putJson("/api/internal/employees/{$employee->id}", $updateData);

        $response->assertStatus(204);

        // Проверяем что обязательные поля обновились
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'lastname' => 'Новая',
            'firstname' => 'Фамилия',
            'email' => '<EMAIL>',
            'status' => 'active'
        ]);
    }

    public function test_can_update_employee_work_schedule(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'lastname' => 'Тестов',
            'firstname' => 'Тест',
            'email' => '<EMAIL>',
            'status' => 'active',
            'work_schedule' => [
                'monday_from' => '10:00',
                'monday_to' => '19:00',
                'tuesday_from' => '10:00',
                'tuesday_to' => '19:00',
                'wednesday_from' => '10:00',
                'wednesday_to' => '19:00',
                'thursday_from' => '10:00',
                'thursday_to' => '19:00',
                'friday_from' => '10:00',
                'friday_to' => '18:00'
            ]
        ];

        $response = $this->putJson("/api/internal/employees/{$employee->id}", $updateData);

        $response->assertStatus(204);

        // Получаем обновленного сотрудника из БД
        $updatedEmployee = Employee::find($employee->id);

        $schedule = json_decode($updatedEmployee->work_schedule);
        // Проверяем что расписание обновилось
        $this->assertEquals('10:00', $schedule->monday_from);
        $this->assertEquals('19:00', $schedule->monday_to);
        $this->assertEquals('18:00', $schedule->friday_to);
    }

    public function test_can_show_employee(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson("/api/internal/employees/{$employee->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'archived_at',
                'user_id',
                'lastname',
                'firstname',
                'patronymic',
                'telephone',
                'email',
                'status',
                'job_number',
                'citizenship',
                'gender',
                'inn',
                'id_card',
                'passport_series',
                'passport_number',
                'passport_issue_date',
                'who_issued_passport',
                'division_code',
                'registration_address',
                'temporary_registration_address',
                'driver_license_series',
                'driver_license_number',
                'driver_license_issue_date',
                'driver_license_category',
                'military_card',
                'hire_date',
                'dismissal_date',
                'position',
                'salary',
                'labor_fund',
                'planned_advance',
                'work_schedule',
                'role_id',
                'department_id'
            ]);
    }

    public function test_cannot_show_employee_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson("/api/internal/employees/{$employee->id}");

        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_employee(): void
    {
        $fakeId = $this->faker->uuid;

        $response = $this->getJson("/api/internal/employees/{$fakeId}");

        $response->assertStatus(404);
    }

    public function test_can_delete_employee(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->deleteJson("/api/internal/employees/{$employee->id}");

        $response->assertStatus(204);

        // Проверяем что запись удалена
        $this->assertDatabaseMissing('employees', [
            'id' => $employee->id
        ]);
        $this->assertDatabaseMissing('cabinet_employee', [
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_cannot_delete_employee_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->deleteJson("/api/internal/employees/{$employee->id}");

        $response->assertNotFound();

        // Проверяем что запись не удалена
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id
        ]);
        $this->assertDatabaseHas('cabinet_employee', [
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);
    }

    public function test_cannot_delete_non_existent_employee(): void
    {
        $fakeId = $this->faker->uuid;

        $response = $this->deleteJson("/api/internal/employees/{$fakeId}");

        $response->assertStatus(404);
    }

    public function test_can_bulk_delete_employees(): void
    {
        // Создаем сотрудников
        $employees = Employee::factory()->count(3)->create();
        foreach ($employees as $employee) {
            CabinetEmployee::factory()->create([
                'employee_id' => $employee->id,
                'cabinet_id' => $this->cabinet->id
            ]);
        }

        $employeeIds = $employees->pluck('id')->toArray();

        $response = $this->deleteJson('/api/internal/employees/bulk-delete', [
            'ids' => $employeeIds,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertStatus(204);

        // Проверяем что все записи удалены
        foreach ($employeeIds as $id) {
            $this->assertDatabaseMissing('employees', ['id' => $id]);
            $this->assertDatabaseMissing('cabinet_employee', [
                'employee_id' => $id,
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_cannot_bulk_delete_employees_from_other_cabinet(): void
    {
        // Создаем сотрудников в другом кабинете
        $employees = Employee::factory()->count(2)->create();
        foreach ($employees as $employee) {
            CabinetEmployee::factory()->create([
                'employee_id' => $employee->id,
                'cabinet_id' => $this->otherCabinet->id
            ]);
        }

        $employeeIds = $employees->pluck('id')->toArray();

        $response = $this->deleteJson('/api/internal/employees/bulk-delete', [
            'ids' => $employeeIds,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response->assertStatus(403);

        // Проверяем что записи не удалены
        foreach ($employeeIds as $id) {
            $this->assertDatabaseHas('employees', ['id' => $id]);
            $this->assertDatabaseHas('cabinet_employee', [
                'employee_id' => $id,
                'cabinet_id' => $this->otherCabinet->id
            ]);
        }
    }

    public function test_can_archive_employee(): void
    {
        // Создаем сотрудника
        $employee = Employee::factory()->create(['archived_at' => null]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->postJson('/api/internal/employees/archive', [
            'ids' => [$employee->id],
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertStatus(204);

        // Проверяем что сотрудник помечен как архивный
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
        ]);
        $archivedEmployee = Employee::find($employee->id);
        $this->assertNotNull($archivedEmployee->archived_at);
    }

    public function test_can_unarchive_employee(): void
    {
        // Создаем архивного сотрудника
        $employee = Employee::factory()->create(['archived_at' => now()]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->postJson('/api/internal/employees/unarchive', [
            'ids' => [$employee->id],
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertStatus(204);

        // Проверяем что сотрудник больше не в архиве
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'archived_at' => null
        ]);
    }

    public function test_cannot_archive_employee_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $employee = Employee::factory()->create(['archived_at' => null]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->postJson('/api/internal/employees/archive', [
            'ids' => [$employee->id],
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response->assertStatus(403);

        // Проверяем что сотрудник не помечен как архивный
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'archived_at' => null
        ]);
    }

    public function test_cannot_unarchive_employee_from_other_cabinet(): void
    {
        // Создаем архивного сотрудника в другом кабинете
        $archivedAt = now();
        $employee = Employee::factory()->create(['archived_at' => $archivedAt]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->postJson('/api/internal/employees/unarchive', [
            'ids' => [$employee->id],
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response->assertStatus(403);

        // Проверяем что сотрудник остался в архиве
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'archived_at' => $archivedAt
        ]);
    }

    public function test_bulk_archive_validation_errors(): void
    {
        $response = $this->postJson('/api/internal/employees/archive', [
            'ids' => ['not-a-uuid']
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids.0']);
    }

    public function test_bulk_unarchive_validation_errors(): void
    {
        $response = $this->postJson('/api/internal/employees/unarchive', [
            'ids' => ['not-a-uuid']
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids.0']);
    }

    public function test_can_get_employee_profile(): void
    {
        $response = $this->getJson("/api/internal/employees/{$this->cabinet->id}/profile");

        $response->assertStatus(200);

        $responseData = $response->json();
        $this->assertEquals($this->user->id, $responseData['user_id']);
    }

    public function test_cannot_get_employee_profile_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $employee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson("/api/internal/employees/{$this->otherCabinet->id}/profile");

        $response->assertStatus(403);
    }

    public function test_cannot_get_employee_profile_for_non_existent_cabinet(): void
    {
        $nonExistentCabinetId = Str::uuid();

        $response = $this->getJson("/api/internal/employees/{$nonExistentCabinetId}/profile");

        $response->assertStatus(403);
    }

    public function test_employee_profile_without_driver_license(): void
    {
        // Создаем сотрудника без водительских прав
        $employee = Employee::factory()->create([
            'firstname' => 'Иван',
            'lastname' => 'Петров',
            'driver_license_series' => null,
            'driver_license_number' => null,
            'driver_license_issue_date' => null,
            'driver_license_expiration_date' => null,
            'driver_license_category' => null
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson("/api/internal/employees/{$this->cabinet->id}/profile");

        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertNull($responseData['driver_license_series']);
    }
}
