<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Data;

use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderNumberingTypeEnum;
use App\Modules\Marketplaces\Services\Wildberries\Enums\PrintLabelSizeEnum;

class OrderSyncSettingsData
{
    public function __construct(
        public OrderNumberingTypeEnum $numType,
        public PrintLabelSizeEnum $printLabelSize,
        public bool $addPrefix = false,
        public string $prefix = 'WB',
        public bool $useCommonBlockContract = false,
        public bool $reserve = true,
        public bool $syncOrderStatuses = false,
        public bool $fairMark = false,
        public bool $autoSync = false,
    ) {
    }
}
