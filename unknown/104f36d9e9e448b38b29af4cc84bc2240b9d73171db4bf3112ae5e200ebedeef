<?php

namespace App\Contracts\Services\Internal\Finances;

use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

interface FinanceServiceContract
{
    public function index(array|IndexRequestDTO $data): Collection;
    public function bulkDelete(array $ids): void;
    public function bulkHeld(array $ids): void;
    public function bulkUnheld(array $ids): void;
    public function bulkCopy(array $ids): void;
}
