<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class WarehouseStorageAreaDTO implements HasInsertArrayDtoContract, DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public ?string $resourceId,
        public string $name,
        public ?string $warehouse_id,
        public ?string $description = null,
        public ?string $temperature_from = null,
        public ?string $temperature_to = null,
        public array $cells_id = [],
        public array $products_id = []
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'name' => $this->name,
            'description' => $this->description,
            'temperature_from' => $this->temperature_from,
            'temperature_to' => $this->temperature_to,
            'warehouse_id' => $this->warehouse_id,
        ];
    }
    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'temperature_from' => $this->temperature_from,
            'temperature_to' => $this->temperature_to,
        ];
    }
    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            resourceId: $data['id'] ?? null,
            name: $data['name'],
            warehouse_id: $data['warehouse_id'] ?? null,
            description: $data['description'] ?? null,
            temperature_from: $data['temperature_from'] ?? null,
            temperature_to: $data['temperature_to'] ?? null,
            cells_id: $data['cells_id'] ?? [],
            products_id: $data['products_id'] ?? []
        );
    }
}
