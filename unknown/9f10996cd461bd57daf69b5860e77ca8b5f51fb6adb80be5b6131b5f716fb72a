<?php

namespace App\Contracts\Requests;

use App\Contracts\DtoContract;

/**
 * Юзается в стор реквестах!!!
 * Return type меняем в методе на дто, который подписывает DtoContract
 * Иначе PHPStan ругается =(
 *
 * Пример:
 *
 * public function toDTO(): CustomerOrderDTO (А не DtoContract)
 * {
 *      return new CustomerOrderDTO($this->validated());
 * }
 *
 */
interface ToDtoContract
{
    public function toDTO(): DtoContract;
}
