<?php

namespace App\Entities;

class VendorOrderEntity extends BaseEntity
{
    public static string $table = 'vendor_orders';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'number',
        'date_from',
        'status_id',
        'held',
        'waiting',
        'legal_entity_id',
        'contractor_id',
        'plan_date',
        'warehouse_id',
        'total_price',
        'employee_id',
        'department_id',
        'is_default',
        'is_common',
        'comment',
        'has_vat',
        'price_includes_vat'
    ];

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legal_entities(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractors(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function warehouses(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function vendor_order_items(): RelationBuilder
    {
        return $this->hasMany(VendorOrderItemEntity::class, 'id', 'order_id');
    }

    public function documents(): RelationBuilder
    {
        return $this->hasOne(DocumentEntity::class, 'id', 'documentable_id');
    }
}
