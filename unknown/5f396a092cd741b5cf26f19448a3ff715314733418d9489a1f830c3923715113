<?php

namespace App\Http\Requests\Api\Internal\Files;

use App\DTO\IndexRequestDTO;
use App\Entities\FileEntity;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Contracts\Requests\ToDtoContract;
use Illuminate\Foundation\Http\FormRequest;

class FileIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly FileEntity $entity
    ) {
        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],

            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
