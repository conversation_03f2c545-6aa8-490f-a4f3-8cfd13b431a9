<?php

namespace App\Contracts\Services\Internal;

use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Ozon\OzonService\OzonV1ReturnsFboFbsListService\DTO\OzonV1ReturnsFboFbsListDTO;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface OzonV1ReturnsFboFbsListServiceContract
{
    public function index(array|IndexRequestDTO $data): Collection|LengthAwarePaginator;
    public function show(string $id): ?object;
    public function create(OzonV1ReturnsFboFbsListDTO $dto): void;
}
