<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\WarehouseCellsRepositoryContract;
use App\Contracts\Repositories\WarehouseStorageAreaCellsRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseCellsService\DTO\WarehouseCellDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class WarehouseCellsCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly WarehouseCellsRepositoryContract $repository,
        private readonly WarehouseStorageAreaCellsRepositoryContract $storageAreaCellsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof WarehouseCellDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }
        $this->repository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        if ($dto->storage_area_id) {
            $this->storageAreaCellsRepository->insert(
                [
                    'storage_area_id' => $dto->storage_area_id,
                    'cell_id' => $this->resourceId
                ]
            );
        }

        return $this->resourceId;
    }
}
