<?php

namespace App\Contracts\Services\Internal;

use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\DTO\OzonV3FinanceTransactionListDTO;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface OzonV3FinanceTransactionListServiceContract
{
    public function index(array|IndexRequestDTO $data): Collection|LengthAwarePaginator;
    public function show(string $id): ?object;
    public function create(OzonV3FinanceTransactionListDTO $dto): void;
}
