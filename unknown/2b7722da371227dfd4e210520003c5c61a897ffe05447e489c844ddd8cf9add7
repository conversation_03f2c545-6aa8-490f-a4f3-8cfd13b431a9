<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonWarehousesService\Handlers;

use App\Contracts\Repositories\OzonWarehousesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

class OzonWarehousesGetHandler
{
    public function __construct(
        private readonly OzonWarehousesRepositoryContract $repository,
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        $data = $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage,
        );

        return $data;
    }
}
