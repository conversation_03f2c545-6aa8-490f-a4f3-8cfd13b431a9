<?php

namespace App\Http\Requests\Api\Internal\Products;

use Illuminate\Foundation\Http\FormRequest;

class ProductPriceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'required|UUID|exists:product,id',                      // id продукта
            'cabinet_price_id' => 'required|UUID|exists:cabinet_prices,id',         // id цены из кабинета
            'cabinet_currency_id' => 'required|UUID|exists:cabinet_currencies,id',  // id валюты
            'title' => 'nullable|string|max:255',
            'sort' => 'nullable|integer',
        ];

    }
}
