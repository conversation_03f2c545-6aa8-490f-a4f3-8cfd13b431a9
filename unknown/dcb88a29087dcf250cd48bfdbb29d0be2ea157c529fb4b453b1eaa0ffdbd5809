<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EmailVerificationTest extends TestCase
{
    use RefreshDatabase;
    /**
     * A basic feature test example.
     */
    public function test_send_email_notification_and_accept_verification(): void
    {
        $user = User::factory()->create([
            'email_verified_at' => null
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken
        ])->post('/api/email/verification-notification');

        $response->assertStatus(200);

        sleep(1);

        $code = DB::table('email_verification_codes')
            ->where('user_id', $user->id)
            ->first();

        $this->assertNotNull($code, 'Код верификации не найден в БД');
        $this->assertFalse($user->hasVerifiedEmail());

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken
        ])->post('/api/verify-email', [
            'code' => $code->code
        ]);

        $response->assertStatus(200);

        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
    }

    public function test_email_verification_with_invalid_validation_code(): void
    {
        $user = User::factory()->create([
            'email_verified_at' => null
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken
        ])->post('/api/verify-email', [
            'code' => 'invalid_code'
        ]);

        $response->assertJsonValidationErrors('code');

        $user->refresh();
        $this->assertFalse($user->hasVerifiedEmail());
    }

    public function test_email_verification_with_invalid_code(): void
    {
        $user = User::factory()->create([
            'email_verified_at' => null
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken
        ])->post('/api/verify-email', [
            'code' => '1234'
        ]);

        $response->assertStatus(422);

        $user->refresh();
        $this->assertFalse($user->hasVerifiedEmail());
    }

    public function test_email_verification_with_already_verified(): void
    {
        $user = User::factory()->create([
            'email_verified_at' => now()
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken
        ])->post('/api/verify-email', [
            'code' => '1234'
        ]);

        $response->assertStatus(422);

        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
    }

    public function test_fail_send_email_notification_if_already_verified(): void
    {
        $user = User::factory()->create(
            [
                'email_verified_at' => now()
            ]
        );
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer '.$user->createToken('Api-token')->plainTextToken
        ])
            ->post('/api/email/verification-notification');

        $response->assertStatus(422);
    }
}
