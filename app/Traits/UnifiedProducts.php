<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait UnifiedProducts
{
   /**
     * Добавить или обновить запись в unified_products.
     *
     * @param string $article
     * @param int|null $cabinetId
     * @param string $entityType
     * @param int $entityId
     */
    public function upsertUnifiedProduct(string $article, int|null $cabinetId = null,string $entityType,int $entityId): void
    {
        // Проверяем, существует ли уже запись с таким article, cabinet_id и сущностью
        $existingRecord = DB::table('unified_products')
            ->where('article', $article)
            ->where('cabinet_id', $cabinetId)
            ->where('entity_id', $entityId)
            ->where('entity_type', $entityType)
            ->first();

        if ($existingRecord) {
            // Если запись существует, обновляем timestamp
            DB::table('unified_products')
                ->where('id', $existingRecord->id)
                ->update([
                    'updated_at' => now(),
                ]);
        } else {
            // Если записи нет, добавляем новую
            DB::table('unified_products')->insert([
                'article' => $article,
                'cabinet_id' => $cabinetId,
                'entity_id' => $entityId,
                'entity_type' => $entityType,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
