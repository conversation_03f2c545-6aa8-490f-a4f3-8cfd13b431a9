<?php

namespace App\Entities;

class WarehouseReceiptOrderItemEntity extends BaseEntity
{
    public static string $table = 'warehouse_receipt_order_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'receipt_order_id',
        'product_id',
        'quantity',
        'unit_price',
        'total_price',
        'batch_number',
        'lot_number',
        'expiry_date',
        'quality_status',
        'storage_location',
        'vat_rate_id',
    ];

    public function receipt_orders(): RelationBuilder
    {
        return $this->hasOne(WarehouseReceiptOrderEntity::class, 'receipt_order_id', 'id');
    }

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function vat_rates(): RelationBuilder
    {
        return $this->hasOne(VatRateEntity::class, 'vat_rate_id', 'id');
    }
}
