<?php

namespace App\Entities;

class IssuedComissionReportEntity extends BaseEntity
{
    public static string $table = 'issued_comission_reports';

    public static array $fields = [
        "id",
        "created_at",
        "updated_at",
        "cabinet_id",
        "status_id",
        "number",
        "date_from",
        "is_held",
        "is_printed",
        "legal_entity_id",
        "contractor_id",
        "sales_channel_id",
        "currency_id",
        "sum",
        "comment",
        "employee_id",
        "department_id",
        "comission_type",
        "comission_value",
        "is_common",
        "period_from",
        "period_to",
        "contract_id",
    ];

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legal_entities(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractors(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function sales_channels(): RelationBuilder
    {
        return $this->hasOne(SalesChannelEntity::class, 'sales_channel_id', 'id');
    }

    public function cabinet_currencies(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function contracts(): RelationBuilder
    {
        return $this->hasOne(ContractEntity::class, 'contract_id', 'id');
    }

    public function issued_comission_report_items(): RelationBuilder
    {
        return $this->hasMany(IssuedComissionReportItemEntity::class, 'id', 'report_id');
    }
}
