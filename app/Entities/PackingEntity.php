<?php

namespace App\Entities;

class PackingEntity extends BaseEntity
{
    public static string $table = 'packings';

    public static array $fields = [
        'id',
        'deleted_at',
        'created_at',
        'updated_at',
        'cabinet_id',
        'name',
        'description',
        'length',
        'width',
        'height',
        'measurement_unit_size_id',
        'weight',
        'measurement_unit_weight_id',
        'volume',
        'measurement_unit_volume_id',
        'employee_id',
        'department_id'
    ];

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function size_measurement_units(): RelationBuilder
    {
        return $this->hasOne(MeasurementUnitEntity::class, 'measurement_unit_size_id', 'id');
    }

    public function weight_measurement_units(): RelationBuilder
    {
        return $this->hasOne(MeasurementUnitEntity::class, 'measurement_unit_weight_id', 'id');
    }

    public function volume_measurement_units(): RelationBuilder
    {
        return $this->hasOne(MeasurementUnitEntity::class, 'measurement_unit_volume_id', 'id');
    }
}
