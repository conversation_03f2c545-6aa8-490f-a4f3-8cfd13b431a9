<?php

namespace App\Entities;

class WarehouseTransactionEntity extends BaseEntity
{
    public static string $table = 'warehouse_transactions';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'warehouse_id',
        'product_id',
        'quantity',
        'document_type',
        'document_id',
        'operation_type',
        'batch_number',
        'lot_number',
        'expiry_date',
        'quality_status',
        'cost_per_unit',
        'reservation_id',
        // Старые поля для обратной совместимости
        'item_id',
        'shipment_id',
        'transaction_type',
        'total_cost',
        'transaction_date',
    ];

    public function warehouses(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function warehouse_reservations(): RelationBuilder
    {
        return $this->hasOne(WarehouseReservationEntity::class, 'reservation_id', 'id');
    }

    public function warehouse_items(): RelationBuilder
    {
        return $this->hasOne(WarehouseItemEntity::class, 'item_id', 'id');
    }

    public function shipments(): RelationBuilder
    {
        return $this->hasOne(ShipmentEntity::class, 'shipment_id', 'id');
    }
}
