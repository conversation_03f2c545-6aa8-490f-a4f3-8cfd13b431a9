<?php

namespace App\Entities;

class ContractEntity extends BaseEntity
{
    public static string $table = 'contracts';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'archived_at',
        'cabinet_id',
        'number',
        'date_from',
        'legal_entity_id',
        'contractor_id',
        'currency_id',
        'code',
        'amount',
        'comment',
        'employee_id',
        'department_id',
        'status_id',
        'type',
        'is_printed',
        'is_sended',
        'shared_access'
    ];

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legal_entities(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractors(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function cabinet_currencies(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }
}
