<?php

namespace App\Entities;

class IssuedComissionReportItemEntity extends BaseEntity
{
    public static string $table = 'issued_comission_report_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'report_id',
        'product_id',
        'quantity',
        'price',
        'vat_rate_id',
        'summary_price',
        'comission_value',
    ];

    public function vatRate(): RelationBuilder
    {
        return $this->hasOne(VatRateEntity::class, 'vat_rate_id', 'id');
    }

    public function product(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function report(): RelationBuilder
    {
        return $this->hasOne(ReceivedComissionReportEntity::class, 'report_id', 'id');
    }

}
