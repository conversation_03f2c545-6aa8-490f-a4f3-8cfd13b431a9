<?php

namespace App\Entities;

class WarehouseReceiptOrderEntity extends BaseEntity
{
    public static string $table = 'warehouse_receipt_orders';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'warehouse_id',
        'number',
        'date_from',
        'status_id',
        'held',
        'document_basis_type',
        'document_basis_id',
        'reason',
        'total_quantity',
        'total_cost',
        'comment',
    ];

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function warehouses(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function receipt_order_items(): RelationBuilder
    {
        return $this->hasMany(WarehouseReceiptOrderItemEntity::class, 'id', 'receipt_order_id');
    }
}
