<?php

namespace App\Entities;

use App\Contracts\EntityContract;

class WarehouseReceiptOrderEntity implements EntityContract
{
    public function getAllowedFields(): array
    {
        return [
            'id',
            'created_at',
            'updated_at',
            'cabinet_id',
            'employee_id',
            'department_id',
            'warehouse_id',
            'number',
            'date_from',
            'status_id',
            'held',
            'document_basis_type',
            'document_basis_id',
            'reason',
            'total_quantity',
            'total_cost',
            'comment',
        ];
    }

    public function getAllowedSortFields(): array
    {
        return [
            'id',
            'created_at',
            'updated_at',
            'number',
            'date_from',
            'total_quantity',
            'total_cost',
            'held',
        ];
    }

    public function getTableName(): string
    {
        return 'warehouse_receipt_orders';
    }
}
