<?php

namespace App\Entities;

class ContractorDetailEntity extends BaseEntity
{
    public static string $table = 'contractor_details';

    public static array $fields = [
        'id',
        'contractor_id',
        'taxation_type',
        'vat_rate_id',
        'type',
        'inn',
        'kpp',
        'ogrn',
        'okpo',
        'full_name',
        'firstname',
        'patronymic',
        'lastname',
        'ogrnip',
        'certificate_number',
        'certificate_date',
        'created_at',
        'updated_at',
    ];

    public function address(): RelationBuilder
    {
        return $this->hasOne(ContractorDetailAddressEntity::class, 'id', 'contractor_detail_id');
    }
}
