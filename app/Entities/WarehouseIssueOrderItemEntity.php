<?php

namespace App\Entities;

class WarehouseIssueOrderItemEntity extends BaseEntity
{
    public static string $table = 'warehouse_issue_order_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'issue_order_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'unit_price',
        'total_price',
        'batch_number',
        'lot_number',
        'expiry_date',
        'vat_rate_id',
    ];

    public function issue_orders(): RelationBuilder
    {
        return $this->hasOne(WarehouseIssueOrderEntity::class, 'issue_order_id', 'id');
    }

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function warehouse_items(): RelationBuilder
    {
        return $this->hasOne(WarehouseItemEntity::class, 'warehouse_item_id', 'id');
    }

    public function vat_rates(): RelationBuilder
    {
        return $this->hasOne(VatRateEntity::class, 'vat_rate_id', 'id');
    }
}
