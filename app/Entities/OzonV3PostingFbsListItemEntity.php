<?php

namespace App\Entities;

class OzonV3PostingFbsListItemEntity extends BaseEntity
{
    public static string $table = 'ozon_order_items';

    public static array $fields = [
        'id',
        'ozon_order_id',
        'posting_number',
        'inner_posting_number',
        'in_process_at',
        'shipment_date',
        'status',
        'delivering_date',
        'delivery_date_end',
        'amount',
        'currency_code',
        'name',
        'sku',
        'offer_id',
        'products_price',
        'currency_code_products',
        'cost_bayer',
        'currency_code_buyer',
        'quantity',
        'delivery_price',
        'related_postings',
        'purchased',
        'old_price',
        'total_discount_percent',
        'total_discount_value',
        'actions',
        'floor',
        'upper_barcode',
        'lower_barcode',
        'cluster_from',
        'cluster_to',
        'region',
        'city',
        'delivery_type',
        'is_premium',
        'payment_type_group_name',
        'is_legal',
        'client_name',
        'client_email',
        'recipient_name',
        'recipient_tel',
        'delivery_address',
        'index',
        'warehouse',
        'tpl_provider',
        'delivery_method_name',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

}

