<?php

namespace App\Entities;

class WarehouseReservationEntity extends BaseEntity
{
    public static string $table = 'warehouse_reservations';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'warehouse_item_id',
        'customer_order_item_id',
        'reserved_quantity',
        'reserved_at',
        'status',
        'reservation_type',
        'document_type',
        'document_id',
        'priority',
        'expires_at',
        'auto_release',
    ];

    public function warehouse_items(): RelationBuilder
    {
        return $this->hasOne(WarehouseItemEntity::class, 'warehouse_item_id', 'id');
    }

    public function customer_order_items(): RelationBuilder
    {
        return $this->hasOne(CustomerOrderItemEntity::class, 'customer_order_item_id', 'id');
    }
}
