<?php

namespace App\Entities;

class GoodsTransferItemEntity extends BaseEntity
{
    public static string $table = 'goods_transfer_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'goods_transfer_id',
        'product_id',
        'quantity',
        'price',
        'total_price',
        'recidual_from',
        'recidual_to'
    ];

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function goods_transfers(): RelationBuilder
    {
        return $this->hasOne(GoodsTransferEntity::class, 'goods_transfer_id', 'id');
    }
}
