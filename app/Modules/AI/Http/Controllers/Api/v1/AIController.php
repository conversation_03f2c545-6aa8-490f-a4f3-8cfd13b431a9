<?php

namespace App\Modules\AI\Http\Controllers\Api\v1;

use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Modules\AI\Contracts\v1\ModuleAuthInterface;
use App\Modules\AI\Contracts\v1\Services\AIServiceContract;

class AIController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly AIServiceContract $service,
        private readonly ModuleAuthInterface $moduleAuth
    ) {
    }

    public function warehouses(Request $request): ?JsonResponse
    {
        $cabinetId = $request->validate([
            'cabinet_id' => 'required|uuid'
        ])['cabinet_id'];

        return $this->executeAction(function () use ($cabinetId) {

            $this->moduleAuth->hasAccessToCabinet($cabinetId);
            $data = $this->service->getWarehouseData($cabinetId);

            return $this->successResponse(compact('data'));
        });
    }

    public function legals(Request $request): ?JsonResponse
    {
        $cabinetId = $request->validate([
            'cabinet_id' => 'required|uuid'
        ])['cabinet_id'];

        return $this->executeAction(function () use ($cabinetId) {

            $this->moduleAuth->hasAccessToCabinet($cabinetId);
            $data = $this->service->getLegalsData($cabinetId);

            return $this->successResponse(compact('data'));
        });
    }

    public function employees(Request $request): ?JsonResponse
    {
        $cabinetId = $request->validate([
            'cabinet_id' => 'required|uuid'
        ])['cabinet_id'];

        return $this->executeAction(function () use ($cabinetId) {

            $this->moduleAuth->hasAccessToCabinet($cabinetId);
            $data = $this->service->getEmployeesData($cabinetId);

            return $this->successResponse(compact('data'));
        });
    }

    public function departments(Request $request): ?JsonResponse
    {
        $cabinetId = $request->validate([
            'cabinet_id' => 'required|uuid'
        ])['cabinet_id'];

        return $this->executeAction(function () use ($cabinetId) {

            $this->moduleAuth->hasAccessToCabinet($cabinetId);
            $data = $this->service->getDepartmentsData($cabinetId);

            return $this->successResponse(compact('data'));
        });
    }

    public function salesChannels(Request $request): ?JsonResponse
    {
        $cabinetId = $request->validate([
            'cabinet_id' => 'required|uuid'
        ])['cabinet_id'];

        return $this->executeAction(function () use ($cabinetId) {

            $this->moduleAuth->hasAccessToCabinet($cabinetId);
            $data = $this->service->getSalesChannelsData($cabinetId);

            return $this->successResponse(compact('data'));
        });
    }

    public function cabinetCurrency(Request $request): ?JsonResponse
    {
        $cabinetId = $request->validate([
            'cabinet_id' => 'required|uuid'
        ])['cabinet_id'];

        return $this->executeAction(function () use ($cabinetId) {

            $this->moduleAuth->hasAccessToCabinet($cabinetId);
            $data = $this->service->getCabinetCurrencyData($cabinetId);

            return $this->successResponse(compact('data'));
        });
    }
}
