<?php

namespace App\Modules\AI\Repository\v1;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Modules\AI\Contracts\v1\Repository\DepartmentRepositoryContract;

class DepartmentRepository implements DepartmentRepositoryContract
{
    public function get(string $cabinetId): Collection
    {
        $result = DB::table('departments')
            ->where('departments.cabinet_id', $cabinetId)
            ->leftJoin('employees as e', 'departments.id', '=', 'e.department_id')
            ->select([
                'departments.id',
                'departments.cabinet_id',
                'departments.name',
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', e.id,
                                    'fullname', CONCAT(e.lastname, ' ', e.firstname),
                                    'email', e.email,
                                    'telephone', e.telephone,
                                    'driver_license', CASE
                                    WHEN e.driver_license_series IS NOT NULL
                                        OR e.driver_license_number IS NOT NULL
                                        OR e.driver_license_issue_date IS NOT NULL
                                        OR e.driver_license_expiration_date IS NOT NULL
                                        OR e.driver_license_category IS NOT NULL
                                        THEN true ELSE false
                                    END
                                )
                            ) filter (where e.id is not null), '[]'
                        ) AS employees
                    ")
            ])
            ->groupBy('departments.id')
            ->get();

        foreach ($result as $item) {
            $item->employees = json_decode($item->employees);
        }

        return $result;
    }
}
