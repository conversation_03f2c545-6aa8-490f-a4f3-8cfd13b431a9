<?php

namespace App\Modules\AI\Repository\v1;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Modules\AI\Contracts\v1\Repository\SalesChannelsRepositoryContract;

class SalesChannelsRepository implements SalesChannelsRepositoryContract
{
    public function get(string $cabinetId): Collection
    {
        return DB::table('sales_channels')
            ->where('sales_channels.cabinet_id', $cabinetId)
            ->select([
                'sales_channels.id',
                'sales_channels.cabinet_id',
                'sales_channels.name',
                'sales_channels.description',
            ])
            ->get();
    }
}
