<?php

namespace App\Modules\AI\Contracts\v1\Services;

use Illuminate\Support\Collection;

interface AIServiceContract
{
    public function getWarehouseData(string $cabinetId): Collection;

    public function getLegalsData(string $cabinetId): Collection;

    public function getEmployeesData(string $cabinetId): Collection;

    public function getDepartmentsData(string $cabinetId): Collection;

    public function getSalesChannelsData(string $cabinetId): Collection;

    public function getCabinetCurrencyData(string $cabinetId): Collection;
}
