<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base;

use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use Exception;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

/**
 * Базовый класс для Actions отмены заказов
 */
abstract class BaseCancelOrderAction
{
    /**
     * Получение заказа с проверкой типа
     *
     * @param string $orderId ID заказа
     * @return object Данные заказа
     * @throws NotFoundException
     * @throws RuntimeException
     */
    protected function getOrderWithValidation(string $orderId): object
    {
        $tableName = $this->getOrderTableName();

        $order = DB::table($tableName)
            ->join('ozon_integrations', "{$tableName}.integration_id", '=', 'ozon_integrations.id')
            ->where("{$tableName}.id", $orderId)
            ->select([
                "{$tableName}.*",
                'ozon_integrations.api_key as api_key',
                'ozon_integrations.client_id as client_id',
            ])
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        return $order;
    }

    /**
     * Отмена заказа
     *
     * @param string $orderId ID заказа
     * @return bool Результат отмены
     * @throws NotFoundException
     * @throws WBSellerException
     * @throws Throwable
     */
    public function run(
        string $orderId,
        int $cancelReasonId,
        ?string $cancelReasonMessage,
    ): bool {
        $order = $this->getOrderWithValidation($orderId);

        try {
            DB::beginTransaction();

            $this->cancelOrderInOzon(
                postingNumber: $order->posting_number,
                cancelReasonId: $cancelReasonId,
                cancelReasonMessage: $cancelReasonMessage,
                clientId: decrypt($order->client_id),
                apiKey: decrypt($order->api_key),
            );

            DB::table($this->getOrderTableName())
                ->where('id', $orderId)
                ->delete();

            DB::table('customer_orders')
                ->where('id', $order->customer_order_id)
                ->delete();

            DB::commit();
        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }

        return true;
    }

    /**
     * Получить имя таблицы заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderTableName(): string;

    /**
     * Получить имя поля для связи с заказом в связанных таблицах
     *
     * @return string Имя поля
     */
    abstract protected function getOrderIdFieldName(): string;

    abstract protected function cancelOrderInOzon(
        string $postingNumber,
        int $cancelReasonId,
        ?string $cancelReasonMessage,
        int $clientId,
        string $apiKey,
    ): void;
}
