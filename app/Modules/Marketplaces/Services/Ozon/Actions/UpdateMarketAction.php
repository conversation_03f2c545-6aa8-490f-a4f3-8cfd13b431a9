<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions;

use App\Modules\Marketplaces\Services\Ozon\Data\OzonMarketData;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class UpdateMarketAction
{
    private Carbon $time;

    public function __construct()
    {
        $this->time = Carbon::now();
    }

    public function run(OzonMarketData $data, string $id): void
    {
        try {
            DB::beginTransaction();

            $updated = $this->updateIntegration($id, $data);

            if ($updated) {
                $this->updateOrderSettings($id, $data);
                $this->updatePriceSettings($id, $data);
                $this->updateReportSettings($id, $data);
            }


            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new RuntimeException($e->getMessage());
        }
    }

    /**
     * @param string $id
     * @param OzonMarketData $data
     * @return bool
     */
    private function updateIntegration(string $id, OzonMarketData $data): bool
    {
        return DB::table('ozon_integrations')
            ->where('id', $id)
            ->update([
                'updated_at' => $this->time,

                'shop_name' => $data->name,

                'client_id' => encrypt($data->clientId),
                'api_key' => encrypt($data->apiKey),

                'legal_entity_id' => $data->legalEntityId,
                'department_id' => $data->departmentId,
                'contractor_id' => $data->contractorId,
                'commission_contract_id' => $data->comissionContractId
            ]) > 0;
    }

    /**
     * @param string $id
     * @param OzonMarketData $data
     * @return void
     */
    private function updateOrderSettings(string $id, OzonMarketData $data): void
    {
        DB::table('ozon_order_settings')
            ->where('integration_id', $id)
            ->update(
                [
                    'numbering_type' => $data->orderSyncSettingsData->numType,
                    'add_prefix_to_orders' => $data->orderSyncSettingsData->addPrefix,
                    'order_prefix' => $data->orderSyncSettingsData->prefix,
                    'use_common_agreement' => $data->orderSyncSettingsData->useCommonBlockContract,
                    'sync_statuses' => $data->orderSyncSettingsData->syncOrderStatuses,
                    'auto_confirm_orders' => $data->orderSyncSettingsData->autoAcceptOrders,
                    'reserve_from_inventory' => $data->orderSyncSettingsData->reserve,
                    'send_mark_codes' => $data->orderSyncSettingsData->fairMark,
                    'auto_sync' => $data->orderSyncSettingsData->autoSync,

                    'updated_at' => $this->time,
                ]
            );
    }

    /**
     * @param string $id
     * @param OzonMarketData $data
     * @return void
     */
    private function updatePriceSettings(string $id, OzonMarketData $data): void
    {
        DB::table('ozon_price_settings')
            ->where('integration_id', $id)
            ->update([
                'updated_at' => $this->time,

                'price_id' => $data->priceSyncSettingsData->yourPriceId,
                'prediscount_price_id' => $data->priceSyncSettingsData->prediscountPriceId,
                'min_price' => $data->priceSyncSettingsData->minPriceId,
                'auto_sync' => $data->priceSyncSettingsData->autoSync,
            ]);
    }

    /**
     * @param string $id
     * @param OzonMarketData $data
     * @return void
     */
    private function updateReportSettings(string $id, OzonMarketData $data): void
    {
        DB::table('ozon_report_settings')
            ->where('integration_id', $id)
            ->update([
                'updated_at' => $this->time,

                'auto_sync' => $data->reportSyncSettingsData->autoSync,
            ]);
    }

}
