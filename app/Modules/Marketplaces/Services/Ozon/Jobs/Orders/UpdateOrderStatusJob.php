<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs\Orders;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class UpdateOrderStatusJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly string $postingNumber,
        private readonly string $newState,
        private readonly string $changedStateDate,
        private readonly int $warehouseId
    ) {}

    public function handle(): void
    {
        $sellerApiStatus = $this->mapPushStatusToSellerApi($this->newState);

        $existingOrder = DB::table('ozon_fbs_orders')
            ->where('posting_number', $this->postingNumber)
            ->first();

        if (!$existingOrder) {
            return;
        }

        $updates = [
            'status' => $sellerApiStatus,
            'ozon_warehouse_id' => $this->warehouseId,
            'updated_at' => Carbon::now(),
        ];

        if ($this->changedStateDate) {
            $statusDate = $this->parseDateTime($this->changedStateDate);

            $updates = match ($sellerApiStatus) {
                'delivered' => array_merge($updates, ['shipment_date' => $statusDate]),
                'delivering' => array_merge($updates, ['delivering_date' => $statusDate]),
                default => $updates
            };
        }

        DB::table('ozon_fbs_orders')
            ->where('id', $existingOrder->id)
            ->update($updates);
    }

    private function mapPushStatusToSellerApi(string $pushStatus): string
    {
        return match ($pushStatus) {
            'posting_acceptance_in_progress' => 'acceptance_in_progress',
            'posting_created' => 'awaiting_approve',
            'posting_awaiting_registration' => 'awaiting_registration',
            'posting_transferring_to_delivery', 'posting_not_in_carriage', 'posting_in_carriage' => 'awaiting_deliver',
            'posting_in_arbitration' => 'arbitration',
            'posting_in_client_arbitration' => 'client_arbitration',
            'posting_on_way_to_city', 'posting_transferred_to_courier_service', 'posting_in_courier_service', 'posting_in_pickup_point', 'posting_on_way_to_pickup_point', 'posting_conditionally_delivered' => 'delivering',
            'posting_driver_pick_up' => 'driver_pickup',
            'posting_delivered', 'posting_received' => 'delivered',
            'posting_canceled' => 'cancelled',
            'posting_not_in_sort_center' => 'not_accepted',
            default => $pushStatus
        };
    }

    private function parseDateTime(?string $dateString): ?string
    {
        if (!$dateString) {
            return null;
        }

        try {
            return Carbon::parse($dateString)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }
}
