<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs;

use App\Clients\Ozon\API;
use App\Clients\Ozon\Exception\OzonSellerException;
use App\Modules\Marketplaces\Services\Ozon\Enums\ClusterTypeEnum;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class SyncOzonSystemWarehousesJob implements ShouldQueue
{
    use Dispatchable;
    use HasOrderedUuid;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected API $api;
    protected array $warehousesCache = [];
    protected array $groupsCache = [];
    protected array $existingMappings = [];
    protected ?string $rootGroupId = null;

    public int $tries = 3;
    public int $timeout = 300;

    public function __construct(
        protected string $apiKey,
        protected int $clientId,
        protected ?string $cabinetId = null
    ) {
        $this->api = new API($this->apiKey, $this->clientId);
    }

    public function handle(): void
    {
        $this->loadCache();
        $this->rootGroupId = $this->ensureRootGroup();

        $clusterTypes = config('ozon.warehouse_sync.cluster_types', ['CLUSTER_TYPE_OZON', 'CLUSTER_TYPE_CIS']);

        foreach ($clusterTypes as $clusterType) {
            $clusters = $this->api->Clusters()->getClustersList($clusterType);

            if (empty($clusters)) {
                continue;
            }

            $this->syncClusters($clusters, $clusterType);
        }
    }

    private function loadCache(): void
    {
        $groups = DB::table('warehouse_groups')
            ->where('source_type', 'ozon')
            ->whereNull('cabinet_id')
            ->get();

        foreach ($groups as $group) {
            $this->groupsCache[$group->name] = $group;
        }

        $warehouses = DB::table('warehouses')
            ->where('source_type', 'ozon')
            ->whereNull('cabinet_id')
            ->get();

        foreach ($warehouses as $warehouse) {
            $this->warehousesCache[$warehouse->name] = $warehouse;
        }

        $mappings = DB::table('ozon_system_warehouse_mappings')
            ->pluck('system_warehouse_id', 'ozon_warehouse_id')
            ->toArray();

        $this->existingMappings = $mappings;
    }

    private function ensureRootGroup(): string
    {
        $rootGroupName = 'OZON';

        if (isset($this->groupsCache[$rootGroupName])) {
            return $this->groupsCache[$rootGroupName]->id;
        }

        $rootGroupId = $this->generateUuid();
        $rootGroupData = [
            'id' => $rootGroupId,
            'name' => $rootGroupName,
            'cabinet_id' => null,
            'parent_id' => null,
            'source_type' => 'ozon',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('warehouse_groups')->insert($rootGroupData);
        $this->groupsCache[$rootGroupName] = (object) $rootGroupData;

        return $rootGroupId;
    }

    private function syncClusters(array $clusters, string $clusterType): void
    {
        $clusterTypeEnum = ClusterTypeEnum::from($clusterType);
        $clusterTypeGroupId = $this->ensureClusterTypeGroup($clusterTypeEnum);

        $groupsToInsert = [];
        $warehousesToInsert = [];
        $mappingsToInsert = [];

        $clusterGroups = [];

        foreach ($clusters as $cluster) {
            $clusterGroupName = $cluster->name;

            if (!isset($this->groupsCache[$clusterGroupName])) {
                $clusterGroupId = $this->generateUuid();
                $groupsToInsert[] = [
                    'id' => $clusterGroupId,
                    'name' => $clusterGroupName,
                    'cabinet_id' => null,
                    'parent_id' => $clusterTypeGroupId,
                    'source_type' => 'ozon',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                $clusterGroups[$clusterGroupName] = $clusterGroupId;
            } else {
                $clusterGroups[$clusterGroupName] = $this->groupsCache[$clusterGroupName]->id;
            }

            if (!empty($cluster->warehouses)) {
                foreach ($cluster->warehouses as $warehouse) {
                    $warehouseName = $warehouse->name;

                    if (isset($this->warehousesCache[$warehouseName])) {
                        $systemWarehouseId = $this->warehousesCache[$warehouseName]->id;
                        
                        if (!isset($this->existingMappings[$warehouse->id])) {
                            $mappingsToInsert[] = [
                                'id' => $this->generateUuid(),
                                'ozon_warehouse_id' => $warehouse->id,
                                'system_warehouse_id' => $systemWarehouseId,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }
                        continue;
                    }

                    $warehouseId = $this->generateUuid();
                    $warehousesToInsert[] = [
                        'id' => $warehouseId,
                        'name' => $warehouseName,
                        'cabinet_id' => null,
                        'group_id' => $clusterGroups[$clusterGroupName],
                        'source_type' => 'ozon',
                        'is_default' => false,
                        'is_common' => true,
                        'control_free_residuals' => false,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $mappingsToInsert[] = [
                        'id' => $this->generateUuid(),
                        'ozon_warehouse_id' => $warehouse->id,
                        'system_warehouse_id' => $warehouseId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }

        if (!empty($groupsToInsert)) {
            DB::table('warehouse_groups')->insert($groupsToInsert);
            foreach ($groupsToInsert as $group) {
                $this->groupsCache[$group['name']] = (object) $group;
            }
        }

        if (!empty($warehousesToInsert)) {
            DB::table('warehouses')->insert($warehousesToInsert);
        }

        if (!empty($mappingsToInsert)) {
            DB::table('ozon_system_warehouse_mappings')->insert($mappingsToInsert);
        }
    }

    private function ensureClusterTypeGroup(ClusterTypeEnum $clusterTypeEnum): string
    {
        $clusterTypeGroupName = $clusterTypeEnum->getGroupName();

        if (isset($this->groupsCache[$clusterTypeGroupName])) {
            return $this->groupsCache[$clusterTypeGroupName]->id;
        }

        $clusterTypeGroupId = $this->generateUuid();
        $clusterTypeGroupData = [
            'id' => $clusterTypeGroupId,
            'name' => $clusterTypeGroupName,
            'cabinet_id' => null,
            'parent_id' => $this->rootGroupId,
            'source_type' => 'ozon',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('warehouse_groups')->insert($clusterTypeGroupData);
        $this->groupsCache[$clusterTypeGroupName] = (object) $clusterTypeGroupData;

        return $clusterTypeGroupId;
    }

    public function failed(Exception $exception): void
    {
        //
    }
}
