<?php

namespace App\Modules\Marketplaces\Services\Ozon\DTO;

class UpdateWarehouseDTO
{
    public function __construct(
        public string $name,
        public string $warehouseId,
        public string $FBSWarehouseId
    ) {
    }

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'warehouse_id' => $this->warehouseId,
            'fbs_warehouse_id' => $this->FBSWarehouseId
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            name: $data['name'],
            warehouseId: $data['warehouse_id'],
            FBSWarehouseId: $data['fbs_warehouse_id']
        );
    }
}
