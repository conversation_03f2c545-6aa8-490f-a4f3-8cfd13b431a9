<?php

namespace App\Modules\Marketplaces\Services\Ozon\Enums;

enum SystemWarehouseTypeEnum: string
{
    case FULL_FILLMENT = 'FULL_FILLMENT';
    case EXPRESS_DARK_STORE = 'EXPRESS_DARK_STORE';
    case SORTING_CENTER = 'SORTING_CENTER';
    case ORDERS_RECEIVING_POINT = 'ORDERS_RECEIVING_POINT';
    case CROSS_DOCK = 'CROSS_DOCK';
    case DISTRIBUTION_CENTER = 'DISTRIBUTION_CENTER';

    public function getDescription(): string
    {
        return match ($this) {
            self::FULL_FILLMENT => 'Фулфилмент',
            self::EXPRESS_DARK_STORE => 'Даркстор',
            self::SORTING_CENTER => 'Сортировочный центр',
            self::ORDERS_RECEIVING_POINT => 'Пункт приёма заказов',
            self::CROSS_DOCK => 'Кросс-докинг',
            self::DISTRIBUTION_CENTER => 'Распределительный центр',
        };
    }
} 