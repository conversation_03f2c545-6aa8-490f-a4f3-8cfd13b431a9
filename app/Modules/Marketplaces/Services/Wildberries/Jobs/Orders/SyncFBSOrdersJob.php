<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders;

use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\Base\BaseOrdersSyncJob;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Job для синхронизации FBS заказов из Wildberries
 */
class SyncFBSOrdersJob extends BaseOrdersSyncJob
{
    /**
     * @throws Throwable
     */
    public function handle(): void
    {
        try {
            $this->preloadData();
            $this->processFbsOrders();
        } catch (Throwable $e) {
            Log::error('Error in SyncFBSOrdersJob: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'cabinet_id' => $this->cabinetId
            ]);

            throw $e;
        }
    }

    /**
     * Обработка FBS заказов в streaming режиме для избежания проблем с памятью
     */
    private function processFbsOrders(): void
    {
        try {
            // Обрабатываем заказы в streaming режиме
            $this->processFbsOrdersStreaming();
        } catch (Exception $e) {
            Log::error('Error processing FBS orders: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            ]);
            throw $e;
        }
    }

    /**
     * Обработка FBS заказов в streaming режиме
     * @throws Exception
     */
    private function processFbsOrdersStreaming(): void
    {
        if ($this->dateFrom && $this->dateTo) {
            $dateFrom = new DateTime($this->dateFrom);
            $dateTo = new DateTime($this->dateTo);

            $this->fetchAndProcessOrdersWithPagination($dateFrom, $dateTo);
        } else {
            // Обрабатываем новые заказы
            $this->processNewOrders();

            // Обрабатываем заказы за период
            $dateFrom = new DateTime('-' . self::DEFAULT_DAYS_PERIOD . ' days');
            $dateTo = new DateTime();

            $this->fetchAndProcessOrdersWithPagination($dateFrom, $dateTo);
        }
    }

    /**
     * Обработка новых заказов
     */
    private function processNewOrders(): void
    {
        $newOrders = $this->fetchOrdersWithRateLimit(
            fn () => $this->api->Marketplace()->getNewOrders(),
            'marketplace'
        );

        if (!empty($newOrders)) {
            $this->processOrdersBatch($newOrders, OrderDeliveryTypeEnum::FBS->value);
            unset($newOrders);
        }
    }

    /**
     * Получение и обработка заказов с пагинацией в streaming режиме
     *
     * @param DateTime $dateFrom
     * @param DateTime $dateTo
     * @throws Exception
     */
    private function fetchAndProcessOrdersWithPagination(DateTime $dateFrom, DateTime $dateTo): void
    {
        $next = 0;
        $processedCount = 0;

        do {
            // Применяем рейт-лимитер перед каждым запросом
            $response = $this->fetchOrdersWithRateLimit(
                fn () => $this->api->Marketplace()->getOrders(self::API_LIMIT, $next, $dateFrom, $dateTo),
                'marketplace'
            );

            if (empty($response)) {
                break;
            }

            // Проверяем, есть ли пагинация в ответе
            if (isset($response['orders'], $response['next'])) {
                // Ответ с пагинацией
                $orders = $response['orders'];
                $next = $response['next'];
            } else {
                // Обратная совместимость - ответ без пагинации
                $orders = $response;
                $next = null;
            }

            if (!empty($orders)) {
                // Обрабатываем этот батч заказов немедленно
                $this->processOrdersBatch($orders, OrderDeliveryTypeEnum::FBS->value);
                $processedCount += count($orders);

                // Очищаем заказы из памяти
                unset($orders);
            }

            // Если next равен 0 или null, значит данных больше нет
        } while ($next !== null && $next > 0);
    }

    /**
     * Получить имя таблицы заказов для FBS
     */
    protected function getOrdersTableName(): string
    {
        return 'wildberries_fbs_orders';
    }

    /**
     * Получить имя таблицы товаров заказов для FBS
     */
    protected function getOrderItemsTableName(): string
    {
        return 'wildberries_fbs_order_items';
    }

    /**
     * Получить имя таблицы информации о доставке для FBS
     */
    protected function getOrderDeliveryInfoTableName(): string
    {
        return 'wildberries_fbs_order_delivery_infos';
    }

    /**
     * Получить тип заказа для FBS
     */
    protected function getOrderType(): string
    {
        return OrderDeliveryTypeEnum::FBS->value;
    }

    protected function preloadOrders(): void
    {
        $this->preloadOrdersFromTable('wildberries_fbs_orders');
    }
}
