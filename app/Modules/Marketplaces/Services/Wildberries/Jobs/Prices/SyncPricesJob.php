<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Prices;

use Exception;
use App\Clients\WB\API;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Clients\WB\Exception\WBSellerException;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Services\Api\RateLimiter\Facades\RateLimiter;

/**
 * Job для синхронизации цен с Wildberries
 */
class SyncPricesJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    private API $api;

    /**
     * @throws WBSellerException
     */
    public function __construct(
        private readonly string $token,
        private readonly string $retailPriceId,
        private readonly ?string $salePriceId = null,
        private readonly string $integrationId
    ) {
        $this->api = new API(['masterkey' => $this->token]);
    }

    /**
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $wbPrices = $this->getWildberriesPrices();
            $systemData = $this->getProductPrices();

            $this->processPrices($systemData['integratedProducts'], $systemData['productPrices'], $wbPrices);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * Получает цены товаров из Wildberries
     */
    private function getWildberriesPrices(): array
    {
        $wbPrices = [];
        $sizePrices = [];
        $page = 1;
        $limit = 1000;
        $hasMoreData = true;

        try {
            while ($hasMoreData) {
                // Применяем рейт-лимитер перед запросом
                RateLimiter::throttle('prices');

                try {
                    $response = $this->api->Prices()->getPrices($page, $limit);

                    if (empty($response->data->listGoods)) {
                        $hasMoreData = false;
                        continue;
                    }

                    foreach ($response->data->listGoods as $good) {
                        $nmID = $good->nmID;

                        $wbPrices[$nmID] = [
                            'price' => $good->price ?? 0,
                            'discount' => $good->discount ?? 0,
                            'editableSizePrice' => $good->editableSizePrice ?? false,
                        ];

                        // Если у товара есть размеры и для них можно устанавливать отдельные цены
                        if (!empty($good->sizes) && ($good->editableSizePrice ?? false)) {
                            foreach ($good->sizes as $size) {
                                $sizeID = $size->sizeID;
                                $sizePrices["{$nmID}-{$sizeID}"] = [
                                    'nmID' => $nmID,
                                    'sizeID' => $sizeID,
                                    'price' => $size->price ?? 0,
                                ];
                            }
                        }
                    }

                    $page++;
                } catch (Exception $e) {
                    // Если получили ошибку 409, регистрируем её в рейт-лимитере
                    if ($e->getCode() == 409) {
                        RateLimiter::registerError409('prices');
                    }

                    // Увеличиваем страницу и продолжаем
                    $page++;
                }
            }

            return [
                'products' => $wbPrices,
                'sizes' => $sizePrices
            ];
        } catch (Exception $e) {
            return [
                'products' => [],
                'sizes' => []
            ];
        }
    }

    /**
     * Получает интегрированные товары и их цены из нашей системы
     */
    private function getProductPrices(): array
    {
        $integratedProducts = DB::table('wildberries_matched_products')
            ->where('wildberries_integration_id', $this->integrationId)
            ->get();

        $priceIds = [$this->retailPriceId];
        if ($this->salePriceId) {
            $priceIds[] = $this->salePriceId;
        }

        $productPrices = DB::table('product_prices')
            ->whereIn('cabinet_price_id', $priceIds)
            ->whereIn('product_id', $integratedProducts->pluck('product_id'))
            ->get();

        return [
            'integratedProducts' => $integratedProducts,
            'productPrices' => $productPrices,
        ];
    }

    /**
     * Обрабатывает цены и отправляет их в Wildberries
     * @throws Exception
     */
    private function processPrices(Collection $integratedProducts, Collection $productPrices, array $wbPrices): void
    {
        $groupedPrices = [];
        foreach ($productPrices as $price) {
            $groupedPrices[$price->product_id][$price->cabinet_price_id] = $price->amount;
        }

        $productsToUpdate = [];
        $sizesToUpdate = [];

        foreach ($integratedProducts as $integration) {
            $productId = $integration->product_id;
            $externalId = $integration->wb_id;
            $sizeId = $integration->size_id;

            if (!isset($groupedPrices[$productId][$this->retailPriceId])) {
                continue;
            }

            // Получаем розничную цену (обязательна)
            $retailPrice = $groupedPrices[$productId][$this->retailPriceId] / 100; // Переводим из копеек в рубли

            // Получаем цену со скидкой (если есть)
            $salePrice = null;
            if ($this->salePriceId && isset($groupedPrices[$productId][$this->salePriceId])) {
                $salePrice = $groupedPrices[$productId][$this->salePriceId] / 100; // Переводим из копеек в рубли
            }


            // Если у товара есть размер и для него можно устанавливать отдельные цены
            if ($sizeId && isset($wbPrices['products'][$externalId]) && $wbPrices['products'][$externalId]['editableSizePrice']) {
                $sizesToUpdate[] = [
                    'nmID' => (int)$externalId,
                    'sizeID' => (int)$sizeId,
                    'price' => (int)$retailPrice
                ];
            } else {
                // Рассчитываем скидку в процентах, если есть цена со скидкой
                $discount = 0;
                if ($salePrice && $retailPrice > 0) {
                    $discount = (int)(100 - $salePrice / $retailPrice * 100);
                    // Ограничиваем скидку от 0 до 99%
                    $discount = max(0, min(99, $discount));
                }

                $productsToUpdate[$externalId] = [
                    'nmID' => (int)$externalId,
                    'price' => (int)$retailPrice,
                    'discount' => $discount
                ];
            }
        }
        $productsToUpdate = array_values($productsToUpdate);

        $this->sendPriceUpdates($productsToUpdate, $sizesToUpdate);
    }

    /**
     * Отправляет обновления цен в Wildberries
     * @throws WBSellerException
     */
    private function sendPriceUpdates(array $productsToUpdate, array $sizesToUpdate): void
    {
        // Отправляем обновления цен товаров
        $productChunks = array_chunk($productsToUpdate, 1000);
        foreach ($productChunks as $chunk) {
            RateLimiter::throttle('prices');

            try {
                $response = $this->api->Prices()->upload($chunk);
            } catch (Exception $e) {
                // Если получили ошибку 409, регистрируем её в рейт-лимитере
                if ($e->getCode() == 409) {
                    RateLimiter::registerError409('prices');
                }
                throw $e;
            }
        }

        // Отправляем обновления цен размеров
        $sizeChunks = array_chunk($sizesToUpdate, 1000);
        foreach ($sizeChunks as $chunk) {
            RateLimiter::throttle('prices');

            try {
                $this->api->Prices()->uploadSizes($chunk);
            } catch (Exception $e) {
                // Если получили ошибку 409, регистрируем её в рейт-лимитере
                if ($e->getCode() == 409) {
                    RateLimiter::registerError409('prices');
                }
                throw $e;
            }
        }
    }
}
