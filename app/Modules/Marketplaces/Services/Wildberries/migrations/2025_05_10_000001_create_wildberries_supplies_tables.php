<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wildberries_supplies', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            // Связь с кабинетом и интеграцией
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('integration_id')->references('id')->on('wildberries_integrations')->cascadeOnDelete();

            // Данные поставки
            $table->string('supply_id')->nullable()->comment('ID поставки в Wildberries');
            $table->string('name')->comment('Название поставки');
            $table->boolean('done')->default(false);
            $table->dateTime('closed_at')->nullable()->nullable()->comment('Дата закрытия поставки');
            $table->dateTime('scanned_at')->nullable();
            $table->integer('cargo_type')->nullable()->comment('Габаритный тип поставки');

            // Даты
            $table->dateTime('created_at_wb')->nullable()->comment('Дата создания поставки в Wildberries');

            // Индексы
            $table->index(['cabinet_id', 'integration_id']);
        });

        Schema::create('wildberries_supply_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            // Связь с поставкой и заказом
            $table->foreignUuid('supply_id')->references('id')->on('wildberries_supplies')->cascadeOnDelete();
            $table->foreignUuid('order_id')->references('id')->on('wildberries_fbs_orders')->cascadeOnDelete();

            // Дополнительные данные
            $table->boolean('requires_reshipping')->default(false)->comment('Требуется повторная отгрузка');

            // Индексы
            $table->unique(['supply_id', 'order_id']);
            $table->index('order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wildberries_supply_orders');
        Schema::dropIfExists('wildberries_supplies');
    }
};
