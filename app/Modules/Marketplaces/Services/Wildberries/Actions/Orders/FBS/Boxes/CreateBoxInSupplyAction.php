<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class CreateBoxInSupplyAction
{
    use HasOrderedUuid;

    /**
     * Добавить короба к поставке
     * Метод добавляет требуемое количество коробов в поставку.
     *
     * Можно добавить только пока поставка на сборке.
     *
     * @param string $supplyId
     * @param int $amount
     * @return array ID новой поставки
     *
     * @throws NotFoundException
     * @throws WBSellerException
     */
    public function run(string $supplyId, int $amount): array
    {
        $supply = DB::table('wildberries_supplies')
            ->join('wildberries_integrations', 'wildberries_supplies.integration_id', '=', 'wildberries_integrations.id')
            ->where('wildberries_supplies.id', $supplyId)
            ->select([
                'wildberries_supplies.*',
                'wildberries_integrations.token as token',
            ])
            ->first();

        if (! $supply) {
            throw new NotFoundException('Supply not found');
        }

        if ($supply->done || Carbon::parse($supply->closed_at)->gt(Carbon::parse('0001-01-01T00:00:00Z'))) {
            throw new RuntimeException('Supply is done or closed');
        }

        $token = decrypt($supply->token);
        $api = new API(['masterkey' => $token]);
        $result = $api->Marketplace()->addSupplyBoxes($supply->supply_id, $amount);

        $toInsertBoxes = [];
        $resultIds = [];

        foreach ($result->trbxIds as $box) {

            $id = $this->generateUuid();
            $resultIds[] = $id;
            $toInsertBoxes[] = [
                'id' => $id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'cabinet_id' => $supply->cabinet_id,
                'integration_id' => $supply->integration_id,
                'supply_id' => $supply->id,
                'trbxIds' => $box,
            ];
        }
        DB::table('wildberries_boxes')->insert($toInsertBoxes);

        return $resultIds;
    }
}
