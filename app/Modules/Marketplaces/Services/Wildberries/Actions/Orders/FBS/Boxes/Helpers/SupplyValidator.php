<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\Helpers;

use App\Exceptions\NotFoundException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Класс для валидации поставок Wildberries
 */
readonly class SupplyValidator
{
    /**
     * Валидирует поставку по ID
     *
     * @param string $supplyId ID поставки
     * @return object Данные поставки
     * @throws NotFoundException Если поставка не найдена
     * @throws RuntimeException Если поставка завершена или закрыта
     */
    public function validateSupply(string $supplyId): object
    {
        $supply = DB::table('wildberries_supplies')
            ->join('wildberries_integrations', 'wildberries_supplies.integration_id', '=', 'wildberries_integrations.id')
            ->where('wildberries_supplies.id', $supplyId)
            ->select([
                'wildberries_supplies.*',
                'wildberries_integrations.token as token',
            ])
            ->first();

        if (! $supply) {
            throw new NotFoundException('Поставка не найдена');
        }

        if ($supply->done || Carbon::parse($supply->closed_at)->gt(Carbon::parse('0001-01-01T00:00:00Z'))) {
            throw new RuntimeException('Поставка уже завершена или закрыта');
        }

        return $supply;
    }
}
