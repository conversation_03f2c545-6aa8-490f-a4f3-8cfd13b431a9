<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base;

use App\Exceptions\NotFoundException;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Базовый класс для Actions подтверждения заказов
 */
abstract class BaseConfirmOrderAction
{
    /**
     * Получение заказа с проверкой типа
     *
     * @param string $orderId ID заказа
     * @return object Данные заказа
     * @throws NotFoundException
     * @throws RuntimeException
     */
    protected function getOrderWithValidation(string $orderId): object
    {
        $tableName = $this->getOrderTableName();

        $order = DB::table($tableName)
            ->where('id', $orderId)
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if ($order->module_status !== 'new') {
            throw new RuntimeException('Order is not in new status');
        }

        if ($order->has_unmatched_items) {
            throw new RuntimeException('Order has unmatched items');
        }

        if ($order->needs_warehouse_mapping) {
            throw new RuntimeException('Order needs warehouse mapping');
        }

        return $order;
    }

    /**
     * Обновление статуса заказа
     *
     * @param string $orderId ID заказа
     * @return bool Результат обновления
     */
    protected function updateOrderStatus(string $orderId): bool
    {
        $tableName = $this->getOrderTableName();

        $result = DB::table($tableName)
            ->where('id', $orderId)
            ->update([
                'module_status' => 'confirmed',
                'wb_status' => 'confirm',
                'updated_at' => now(),
            ]);

        return $result > 0;
    }

    /**
     * Подтверждение заказа
     *
     * @param string $orderId ID заказа
     * @return bool Результат подтверждения
     * @throws NotFoundException
     * @throws RuntimeException
     */
    public function run(string $orderId): bool
    {
        $order = $this->getOrderWithValidation($orderId);

        // Выполняем специфичную для типа заказа логику
        $this->performTypeSpecificConfirmation($order);

        return $this->updateOrderStatus($orderId);
    }

    /**
     * Получить ожидаемый тип заказа
     *
     * @return string Тип заказа
     */
    abstract protected function getExpectedOrderType(): string;

    /**
     * Получить имя таблицы заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderTableName(): string;

    /**
     * Выполнить специфичную для типа заказа логику подтверждения
     *
     * @param object $order Данные заказа
     * @return void
     */
    protected function performTypeSpecificConfirmation(object $order): void
    {
        // Базовая реализация - ничего не делаем
        // Наследники могут переопределить этот метод для специфичной логики
    }
}
