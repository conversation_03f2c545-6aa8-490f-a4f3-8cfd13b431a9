<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base;

use App\Clients\WB\APIToken;
use App\Clients\WB\Exception\WBSellerException;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderNumberingTypeEnum;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Базовый класс для Actions загрузки заказов Wildberries
 */
abstract class BaseLoadOrdersAction
{
    /**
     * Получение данных интеграции
     *
     * @param string $integrationId ID интеграции
     * @return object Данные интеграции
     * @throws Exception
     */
    protected function getIntegrationData(string $integrationId): object
    {
        $integration = DB::table('wildberries_integrations')
            ->join('wildberries_order_settings', 'wildberries_integrations.id', '=', 'wildberries_order_settings.integration_id')
            ->where('wildberries_integrations.id', $integrationId)
            ->select([
                'wildberries_integrations.*',
                'wildberries_order_settings.order_prefix as order_prefix',
                'wildberries_order_settings.numbering_type as numbering_type',
                'wildberries_order_settings.add_prefix_to_orders as add_prefix_to_orders',
                'wildberries_order_settings.reserve_from_inventory as reserve_from_inventory',
                'wildberries_order_settings.sync_statuses as sync_statuses',
            ])
            ->groupBy(['wildberries_integrations.id', 'wildberries_order_settings.id'])
            ->first();

        if (!$integration) {
            throw new RuntimeException("Integration with ID {$integrationId} not found");
        }

        return $integration;
    }

    /**
     * Подготовка общих параметров для Job
     *
     * @param object $integration Данные интеграции
     * @param string|null $dateFrom Дата начала
     * @param string|null $dateTo Дата окончания
     * @return array Массив параметров
     * @throws WBSellerException
     * @throws BindingResolutionException
     */
    protected function prepareJobParameters(object $integration, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        $employeeRepository = app()->make(EmployeeRepositoryContract::class);
        $token = decrypt($integration->token);
        $apiToken = new APIToken($token);

        return [
            'cabinetId' => $integration->cabinet_id,
            'token' => $token,
            'integrationId' => $integration->id,
            'sellerId' => $apiToken->sellerUUID(),
            'legalEntityId' => $integration->legal_entity_id,
            'employeeId' => $employeeRepository->getByUserIdAndCabinet(auth()->id(), $integration->cabinet_id)->id,
            'departmentId' => $integration->department_id,
            'contractorId' => $integration->contractor_id,
            'orderNumberingType' => OrderNumberingTypeEnum::from($integration->numbering_type),
            'addPrefix' => $integration->add_prefix_to_orders,
            'prefix' => $integration->order_prefix,
            'reserve' => $integration->reserve_from_inventory,
            'dateFrom' => $dateFrom ? Carbon::parse($dateFrom)->format('Y-m-d') : null,
            'dateTo' => $dateTo ? Carbon::parse($dateTo)->format('Y-m-d') : null,
            'retailPriceId' => '9ec8c3d4-9724-4cef-8efc-a65151fe29e0',
            'salePriceId' => '9ec8c3d4-9751-489e-8605-c4bd90cdc119',
        ];
    }

    /**
     * Абстрактный метод для выполнения загрузки заказов
     *
     * @param string $integrationId ID интеграции
     * @param string|null $dateFrom Дата начала
     * @param string|null $dateTo Дата окончания
     * @return void
     */
    abstract public function run(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void;
}
