<?php

namespace App\Modules\Marketplaces\Services\Traits;

use App\Enums\Api\Internal\StatusTypeEnum;
use Illuminate\Support\Facades\DB;

trait HasStatuses
{
    protected function preloadStatuses(StatusTypeEnum $type): void
    {
        $statuses = DB::table('statuses')
            ->where('cabinet_id', $this->cabinetId)
            ->where('type', $type)
            ->get();

        foreach ($statuses as $status) {
            $this->statusesCache[$status->name] = $status;
        }

        $this->statusesCache['_type'] = $type;
    }
}
