<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSOrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор позиции */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $order_id Идентификатор заказа */
            'order_id' => $this->order_id,
            /** @var string $product_id Идентификатор товара в системе Ozon */
            'product_id' => $this->product_id,
            /** @var string $sku Артикул товара (SKU) */
            'sku' => $this->sku,
            /** @var string $article Артикул продавца */
            'article' => $this->article,
            /** @var string $name Название товара */
            'name' => $this->name,
            /** @var int $quantity Количество товара */
            'quantity' => (int) $this->quantity,
            /** @var string $price Цена за единицу товара */
            'price' => (string) $this->price,
            /** @var string $commission Размер комиссии */
            'commission' => (string) $this->commission,
            /** @var string|null $country_iso2 Код страны происхождения (ISO 3166-1 alpha-2) */
            'country_iso2' => $this->country_iso2,
            /** @var string|null $barcode Штрихкод товара */
            'barcode' => $this->barcode,
            /** @var string|null $weight Вес товара в граммах */
            'weight' => $this->weight,

            /** @var array|null $dimensions Габариты товара */
            'dimensions' => $this->when(!empty($this->dimensions), [
                /** @var string $length Длина в мм */
                'length' => $this->dimensions->length ?? null,
                /** @var string $width Ширина в мм */
                'width' => $this->dimensions->width ?? null,
                /** @var string $height Высота в мм */
                'height' => $this->dimensions->height ?? null,
            ]),

            /** @var array|null $product Информация о товаре */
            'product' => $this->when(!empty($this->product), [
                'id' => $this->product->id ?? null,
                'name' => $this->product->name ?? null,
                'category' => $this->product->category ?? null,
                'brand' => $this->product->brand ?? null,
                'images' => $this->product->images ?? [],
            ]),

            /** @var bool $is_canceled Флаг отмены позиции */
            'is_canceled' => (bool) ($this->is_canceled ?? false),
            /** @var string|null $cancel_reason Причина отмены */
            'cancel_reason' => $this->cancel_reason,
            /** @var string|null $cancel_date Дата отмены */
            'cancel_date' => $this->cancel_date,
        ];
    }
}
