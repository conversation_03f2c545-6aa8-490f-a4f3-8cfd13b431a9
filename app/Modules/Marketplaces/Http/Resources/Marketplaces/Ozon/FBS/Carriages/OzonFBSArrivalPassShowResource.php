<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Carriages;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSArrivalPassShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор пропуска прибытия */
            'id' => $this->id,
            /** @var string|null $ozon_pass_id Идентификатор пропуска в системе Ozon */
            'ozon_pass_id' => $this->ozon_pass_id,
            /** @var string $driver_name Имя водителя */
            'driver_name' => $this->driver_name,
            /** @var string $driver_phone Телефон водителя */
            'driver_phone' => $this->driver_phone,
            /** @var string $vehicle_license_plate Номер транспортного средства */
            'vehicle_license_plate' => $this->vehicle_license_plate,
            /** @var string|null $vehicle_model Модель транспортного средства */
            'vehicle_model' => $this->vehicle_model,
            /** @var bool $with_returns Флаг возврата товаров */
            'with_returns' => (bool) $this->with_returns,
            /** @var string $status Статус пропуска */
            'status' => $this->status,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,

            /** @var array{id: string, ozon_carriage_id: string|null, departure_date: string, status: string} $carriage Информация об отгрузке */
            'carriage' => [
                'id' => $this->carriage->id,
                'ozon_carriage_id' => $this->carriage->ozon_carriage_id,
                'departure_date' => $this->carriage->departure_date,
                'status' => $this->carriage->status,
            ],

            /** @var array{id: string, name: string, ozon_warehouse_id: int} $warehouse Склад */
            'warehouse' => [
                'id' => $this->warehouse->id,
                'name' => $this->warehouse->name,
                'ozon_warehouse_id' => (int) $this->warehouse->ozon_warehouse_id,
            ],

            /** @var array{id: string, name: string, ozon_delivery_method_id: int} $delivery_method Способ доставки */
            'delivery_method' => [
                'id' => $this->delivery_method->id,
                'name' => $this->delivery_method->name,
                'ozon_delivery_method_id' => (int) $this->delivery_method->ozon_delivery_method_id,
            ],
        ];
    }
}
