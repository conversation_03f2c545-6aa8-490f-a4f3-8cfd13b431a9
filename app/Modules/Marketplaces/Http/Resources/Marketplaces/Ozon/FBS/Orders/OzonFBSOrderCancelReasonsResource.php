<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSOrderCancelReasonsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var array<array{id: int, name: string, type_id: int}> $reasons Список причин отмены */
            'reasons' => collect($this->reasons ?? [])->map(function ($reason) {
                return [
                    /** @var int $id Идентификатор причины отмены */
                    'id' => (int) $reason->id,
                    /** @var string $name Название причины отмены */
                    'name' => (string) $reason->name,
                    /** @var int $type_id Тип причины отмены */
                    'type_id' => (int) $reason->type_id,
                ];
            })->toArray(),

            /** @var array<string> $related_posting_numbers Номера отправлений, для которых получены причины */
            'related_posting_numbers' => $this->when(
                property_exists($this->resource, 'related_posting_numbers'),
                $this->related_posting_numbers ?? []
            ) ?: [],

            /** @var string|null $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(
                property_exists($this->resource, 'integration_id'),
                $this->integration_id
            ),
        ];
    }
}
