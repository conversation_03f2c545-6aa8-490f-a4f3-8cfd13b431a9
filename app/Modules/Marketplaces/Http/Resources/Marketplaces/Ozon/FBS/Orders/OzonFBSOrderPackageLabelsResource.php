<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSOrderPackageLabelsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string|null $file_content Содержимое PDF файла с этикетками в формате base64 */
            'file_content' => $this->file_content ?? null,

            /** @var string $file_name Имя файла с этикетками */
            'file_name' => $this->file_name ?? 'package_labels.pdf',

            /** @var string $content_type MIME-тип файла */
            'content_type' => $this->content_type ?? 'application/pdf',

            /** @var array<string> $order_ids Идентификаторы заказов, для которых созданы этикетки */
            'order_ids' => $this->when(
                property_exists($this->resource, 'order_ids'),
                $this->order_ids ?? []
            ) ?: [],

            /** @var int $labels_count Количество этикеток в файле */
            'labels_count' => $this->when(
                property_exists($this->resource, 'labels_count'),
                (int) ($this->labels_count ?? 0)
            ) ?: 0,

            /** @var array<string> $posting_numbers Номера отправлений, для которых созданы этикетки */
            'posting_numbers' => $this->when(
                property_exists($this->resource, 'posting_numbers'),
                $this->posting_numbers ?? []
            ) ?: [],
        ];
    }
}
