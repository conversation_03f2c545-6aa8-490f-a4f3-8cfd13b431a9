<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBO;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OzonFBOIndexCollection extends ResourceCollection
{
    public $collects = OzonFBOIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => $this->additional ? [
                'page' => $this->additional['page'] ?? 1,
                'per_page' => $this->additional['per_page'] ?? 15,
            ] : []
        ];
    }
}
