<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBO\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FBOWarehouseIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции Ozon */
            'integration_id' => $this->integration_id,
            /** @var string|null $name Название склада */
            'name' => $this->name,
            /** @var int $ozon_warehouse_id Идентификатор склада в системе Ozon */
            'ozon_warehouse_id' => (int) $this->ozon_warehouse_id,
            /** @var string $warehouse_id Идентификатор склада в системе */
            'warehouse_id' => $this->warehouse_id,
        ];
    }
}
