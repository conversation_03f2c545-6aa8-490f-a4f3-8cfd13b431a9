<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon\FBS;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Ozon\FBS\Warehouses\FBSWarehouseCollection;
use App\Modules\Marketplaces\Http\Requests\Ozon\UpdateFBSWarehouseRequest;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Modules\Marketplaces\Services\Ozon\DTO\UpdateWarehouseDTO;
use App\Modules\Marketplaces\Services\Ozon\Services\FBS\FBSWarehousesService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS складами Wildberries
 */
class WarehousesController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly OzonPolicy $policy,
        private readonly FBSWarehousesService $warehousesService
    ) {
    }

    /**
     * Получение списка FBS складов
     *
     * @response FBSWarehouseCollection
     */
    public function index(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $warehouses = $this->warehousesService->getWarehouses($integrationId);

            return $this->successResponse(FBSWarehouseCollection::make($warehouses));
        });
    }

    public function load(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $this->warehousesService->loadFbsWarehouses($integrationId);

            return $this->noContentResponse();
        });
    }

    public function update(
        UpdateFBSWarehouseRequest $request,
        string $integrationId,
        string $warehouseId
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $integrationId, $warehouseId) {
            $this->policy->checkPermissionsToIntegration($integrationId);
            $this->policy->checkPermissionsToWarehouse($warehouseId);

            $data = $request->validated();

            $this->warehousesService->updateFbsWarehouse(
                UpdateWarehouseDTO::fromArray(
                    array_merge($data, ['fbs_warehouse_id' => $warehouseId])
                )
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Получить методы доставки склада
     *
     * @response DeliveryMethodCollection
     */
    public function getDeliveryMethods(string $integrationId, string $warehouseId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $warehouseId) {
            $this->policy->checkPermissionsToIntegration($integrationId);
            $this->policy->checkPermissionsToWarehouse($warehouseId);

            $deliveryMethods = $this->warehousesService->getWarehouseDeliveryMethods($warehouseId);

            return $this->successResponse(DeliveryMethodCollection::make($deliveryMethods));
        });
    }
}
