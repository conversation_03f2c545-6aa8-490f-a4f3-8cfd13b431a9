<?php

namespace App\Modules\Marketplaces\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Contracts\ModuleAuthInterface;
use App\Modules\Marketplaces\Factories\MarketplacePolicyFactory;
use App\Modules\Marketplaces\Factories\MarketplaceRequestFactory;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\LoadReportsRequest;
use App\Modules\Marketplaces\Http\Requests\MarketplaceIndexRequest;
use App\Modules\Marketplaces\Http\Resources\Marketplaces\MarketplacesIndexCollection;
use App\Modules\Marketplaces\Services\MarketplaceService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MarketplaceController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly MarketplaceService $service,
        private readonly MarketplacePolicyFactory $policyFactory,
        private readonly ModuleAuthInterface $policy
    ) {
    }


    /**
     * @response MarketplacesIndexCollection<MarketplaceIndexResource>
     */
    public function index(MarketplaceIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $cabinetId = $request->validated('cabinet_id');

            $this->policy->hasAccessToCabinet($cabinetId);

            $data = $this->service->index($cabinetId);

            $collection = new MarketplacesIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    /**
     * Проверка подключения к API
     *
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function connect(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {

            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->connect($id);

            return $this->noContentResponse();
        });
    }

    /**
     * Отключение от API. Может не потребуется
     *
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function disconnect(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);
            $this->service
                ->getDriver($type)
                ->disconnect($id);

            return $this->noContentResponse();
        });
    }

    /**
     * Загрузка товаров из API
     *
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function loadProducts(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);
            $this->service
                ->getDriver($type)
                ->loadProducts($id);

            return $this->noContentResponse();
        });
    }

    /**
     * Загрузка цен из API
     *
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function loadPrices(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadPrices($id);

            return $this->noContentResponse();
        });
    }

    /**
     * Загрузка остатков из API
     *
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function loadResidues(string $type, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($type, $id) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadResidues($id);

            return $this->noContentResponse();
        });
    }

    /**
     * Загрузка заказов из API
     *
     * @param LoadOrdersRequest $request
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function loadOrders(LoadOrdersRequest $request, string $type, string $id): ?JsonResponse
    {
        $dateFrom = $request->validated('date_from');
        $dateTo = $request->validated('date_to');

        return $this->executeAction(function () use ($type, $id, $dateFrom, $dateTo) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadOrders($id, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }

    /**
     * Загрузка отчетов из API
     *
     * @param LoadReportsRequest $request
     * @param string $type Тип интеграции (ozon, wildberries)
     * @param string $id
     * @return JsonResponse|null
     */
    public function loadReports(LoadReportsRequest $request, string $type, string $id): ?JsonResponse
    {
        $period = $request->validated();

        return $this->executeAction(function () use ($type, $id, $period) {
            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->loadReports($id, $period['date_from'], $period['date_to']);

            return $this->noContentResponse();
        });
    }









    /**
     * Обновляет настройки учета затрат
     *
     * @param  Request  $request  Запрос с валидацией
     * @param  string  $type  Тип маркетплейса
     * @param  string  $id  ID интеграции
     */
    public function updateCostAccountingSettings(Request $request, string $type, string $id): JsonResponse
    {
        $request = MarketplaceRequestFactory::updateCostAccountingSettings($type, $request);

        return $this->executeAction(function () use ($request, $type, $id) {
            $dto = $request->toDto($id);

            $this->policyFactory
                ->create($type)
                ->checkPermissionsToIntegration($id);

            $this->service
                ->getDriver($type)
                ->updateCostAccountingSettings($dto, $id);

            return $this->noContentResponse();
        });
    }
}
