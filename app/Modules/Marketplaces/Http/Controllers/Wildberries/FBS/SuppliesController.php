<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\FBS;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Supplies\SupplyIndexCollection;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Supplies\SupplyShowResource;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Supplies\CompleteSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Supplies\CreateSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Supplies\GetSuppliesRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Supplies\GetSupplyDetailsRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\FBS\SuppliesService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS поставками Wildberries
 */
class SuppliesController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly SuppliesService $suppliesService
    ) {
    }

    /**
     * Получение списка FBS поставок
     *
     * @response SupplyIndexCollection<SupplyIndexResource>
     */
    public function index(GetSuppliesRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $filters = $request->validated();

            $data = $this->suppliesService->index($integrationId, $filters);

            $collection = new SupplyIndexCollection($data['data']);
            return $this->successResponse($collection->additional([
                'page' => $data['page'],
                'per_page' => $data['per_page'],
            ]));
        });
    }

    /**
     * Создание новой FBS поставки
     */
    public function store(CreateSupplyRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();

            $this->policy->checkPermissionsToIntegration($data['integration_id']);

            $supplyId = $this->suppliesService->createSupply($data['name'], $data['integration_id']);

            return $this->createdResponse($supplyId);
        });
    }

    /**
     * Получение детальной информации о FBS поставке
     *
     * @response SupplyShowResource
     */
    public function show(GetSupplyDetailsRequest $request, string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $supply = $this->suppliesService->show($supplyId);

            return $this->successResponse(SupplyShowResource::make($supply));
        });
    }

    /**
     * Завершение FBS поставки
     */
    public function deliver(CompleteSupplyRequest $request, string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $this->suppliesService->toDelivery($supplyId);

            return $this->noContentResponse();
        });
    }

    /**
     * Удаление FBS поставки (только пустые поставки)
     */
    public function destroy(string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $this->suppliesService->delete($supplyId);

            return $this->noContentResponse();
        });
    }

    public function loadSupplies(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $this->suppliesService->loadSupplies($integrationId);

            return $this->noContentResponse();
        });
    }
}
