<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\FBS;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\WBGO\AssembleWBGOOrderRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\FBS\FBSOrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class DBWOrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly FBSOrdersService $ordersService
    ) {
    }

    public function collectOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->collectDBWOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Перевод WBGO заказа в доставку (сборка завершена)
     */
    public function assembleOrder(AssembleWBGOOrderRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->assembleDBWOrder($orderId);

            return $this->noContentResponse();
        });
    }
}
