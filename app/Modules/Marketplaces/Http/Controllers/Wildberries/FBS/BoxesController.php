<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\FBS;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Boxes\BoxIndexCollection;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Boxes\BoxStickersResource;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Boxes\AddOrderToBoxInSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Boxes\CreateBoxInSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Boxes\DeleteBoxInSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Boxes\GetBoxStickersRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\FBS\BoxesService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления коробками в FBS поставках Wildberries
 */
class BoxesController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly BoxesService $boxesService
    ) {
    }

    /**
     * Получение списка коробок в FBS поставке
     *
     * @response BoxIndexCollection<BoxIndexResource>
     */
    public function index(string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $boxes = $this->boxesService->getSupplyBoxes($supplyId);

            return $this->successResponse(new BoxIndexCollection($boxes));
        });
    }

    /**
     * Создание коробок в FBS поставке
     */
    public function store(CreateBoxInSupplyRequest $request, string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $amount = $request->validated('amount');
            $boxIds = $this->boxesService->createBoxes($supplyId, $amount);

            return $this->createdResponse($boxIds);
        });
    }

    /**
     * Удаление коробок из FBS поставки
     */
    public function destroy(DeleteBoxInSupplyRequest $request, string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $boxIds = $request->validated('box_ids');
            $this->policy->checkPermissionsToBoxes($boxIds);

            $this->boxesService->deleteBoxes($supplyId, $boxIds);

            return $this->noContentResponse();
        });
    }

    /**
     * Добавление заказов в коробку FBS поставки
     */
    public function addOrders(
        AddOrderToBoxInSupplyRequest $request,
        string $supplyId,
        string $boxId
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $supplyId, $boxId) {
            $orderIds = $request->validated('order_ids');

            $this->policy->addOrders($supplyId, $boxId, $orderIds);

            $this->boxesService->addOrdersToBox($supplyId, $boxId, $orderIds);

            return $this->noContentResponse();
        });
    }

    /**
     * Удаление заказа из коробки FBS поставки
     */
    public function removeOrder(string $supplyId, string $boxId, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($supplyId, $boxId, $orderId) {
            $this->policy->deleteOrderFromBox($supplyId, $boxId, $orderId);

            $this->boxesService->deleteOrderFromBox($supplyId, $boxId, $orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Получение стикеров для коробок FBS поставки
     *
     * @response BoxStickersResource
     */
    public function getStickers(GetBoxStickersRequest $request, string $supplyId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $supplyId) {
            $this->policy->checkPermissionsToSupply($supplyId);

            $boxIds = $request->validated('box_ids');
            $this->policy->checkPermissionsToBoxes($boxIds);

            $stickers = $this->boxesService->getBoxStickers($supplyId, $boxIds);

            return $this->successResponse(BoxStickersResource::make($stickers));
        });
    }
}
