<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\FBS;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Orders\FBSOrderIndexCollection;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Orders\OrderStickersResource;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Orders\AddOrderToNewSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Orders\AddOrderToSupplyRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Orders\GetFBSOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Orders\GetOrderStickersRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\FBS\FBSOrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS заказами Wildberries
 */
class FBSOrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly FBSOrdersService $ordersService
    ) {
    }

    /**
     * Добавление FBS заказа в новую поставку
     */
    public function addToNewSupply(AddOrderToNewSupplyRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $name = $request->validated('name');
            $supplyId = $this->ordersService->addOrderToNewSupply($name, $orderId);

            return $this->createdResponse($supplyId);
        });
    }

    /**
     * Добавление FBS заказа в существующую поставку
     */
    public function addToSupply(AddOrderToSupplyRequest $request, string $supplyId, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId, $supplyId) {
            $this->policy->checkPermissionsToOrder($orderId);
            $this->policy->checkPermissionsToSupply($supplyId);

            $this->ordersService->addOrderToSupply($orderId, $supplyId);

            return $this->noContentResponse();
        });
    }

    /**
     * Получение стикеров для FBS заказов
     *
     * @response OrderStickersResource
     */
    public function getStickers(GetOrderStickersRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $orderIds = $request->validated('order_ids');

            $this->policy->checkPermissionsToOrders($orderIds);

            $stickers = $this->ordersService->getOrderStickers($orderIds);

            return $this->successResponse(OrderStickersResource::make($stickers));
        });
    }

    /**
     * Загрузка FBS заказов
     */
    public function loadFBSOrders(LoadOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $dateFrom = $request->validated('date_from');
            $dateTo = $request->validated('date_to');

            $this->ordersService->loadOrders($integrationId, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }

    /**
     * Подтверждение FBS заказа
     */
    public function confirmOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->confirmFBSOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Отмена FBS заказа
     */
    public function cancelOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Получение списка FBS заказов
     *
     * @response FBSOrderIndexCollection<FBSOrderIndexResource>
     */
    public function indexFbs(GetFBSOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $filters = $request->validated();
            $orders = $this->ordersService->getOrders($integrationId, $filters);

            $collection = new FBSOrderIndexCollection($orders);
            return $this->successResponse($collection->additional([
                'page' => $filters['page'] ?? 1,
                'per_page' => $filters['per_page'] ?? 15,
            ]));
        });
    }
}
