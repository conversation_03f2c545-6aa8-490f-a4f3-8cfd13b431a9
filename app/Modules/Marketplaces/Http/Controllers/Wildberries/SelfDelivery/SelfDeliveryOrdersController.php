<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\SelfDelivery;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Wildberries\SelfDelivery\Orders\SelfDeliveryOrderIndexCollection;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\SelfDelivery\Orders\GetSelfDeliveryOrdersRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\SelfDeliveryOrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS заказами Wildberries
 */
class SelfDeliveryOrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly SelfDeliveryOrdersService $ordersService
    ) {
    }

    /**
     * Загрузка заказов самовывоза
     */
    public function loadSelfDeliveryOrders(LoadOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $dateFrom = $request->validated('date_from');
            $dateTo = $request->validated('date_to');

            $this->ordersService->loadSelfDeliveryOrders($integrationId, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }

    public function confirmSelfDeliveryOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->confirmSelfDeliveryOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Отмена заказа самовывоза
     */
    public function cancelSelfDeliveryOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelSelfDeliveryOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Сборка заказа самовывоза
     */
    public function collectSelfDeliveryOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->collectSelfDeliveryOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Получение списка заказов самовывоза
     *
     * @response SelfDeliveryOrderIndexCollection<SelfDeliveryOrderIndexResource>
     */
    public function index(GetSelfDeliveryOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $filters = $request->validated();
            $orders = $this->ordersService->getOrders($integrationId, $filters);

            $collection = new SelfDeliveryOrderIndexCollection($orders);
            return $this->successResponse($collection->additional([
                'page' => $filters['page'] ?? 1,
                'per_page' => $filters['per_page'] ?? 15,
            ]));
        });
    }

    public function prepareSelfDeliveryOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->prepareSelfDeliveryOrder($orderId);

            return $this->noContentResponse();
        });
    }
}
