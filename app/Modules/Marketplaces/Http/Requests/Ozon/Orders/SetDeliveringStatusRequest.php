<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Orders;

use Illuminate\Foundation\Http\FormRequest;

class SetDeliveringStatusRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_ids' => ['required', 'array', 'min:1'],
            'order_ids.*' => ['required', 'string', 'uuid'],
        ];
    }
}
