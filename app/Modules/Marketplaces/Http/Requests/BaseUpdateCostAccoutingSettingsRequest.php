<?php

namespace App\Modules\Marketplaces\Http\Requests;

use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use App\Modules\Marketplaces\Factories\MarketplaceDtoFactory;
use Illuminate\Foundation\Http\FormRequest;

abstract class BaseUpdateCostAccoutingSettingsRequest extends FormRequest
{
    abstract protected function getMarketplaceType(): string;

    public function toDto(string $integrationId): CostAccountingDto
    {
        return MarketplaceDtoFactory::updateCostAccountingSettings(
            $this->getMarketplaceType(),
            array_merge($this->validated(), ['integration_id' => $integrationId])
        );
    }
}
