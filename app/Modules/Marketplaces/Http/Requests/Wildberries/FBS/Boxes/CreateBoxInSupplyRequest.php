<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Boxes;

use Illuminate\Foundation\Http\FormRequest;

class CreateBoxInSupplyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'amount' => ['required', 'integer', 'min:1', 'max:100'],
        ];
    }

    public function messages(): array
    {
        return [
            'amount.required' => 'Количество коробок обязательно для заполнения',
            'amount.integer' => 'Количество коробок должно быть целым числом',
            'amount.min' => 'Минимальное количество коробок: 1',
            'amount.max' => 'Максимальное количество коробок: 100',
        ];
    }
}
