<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries;

use App\Contracts\Requests\ToDtoContract;
use App\Modules\Marketplaces\Services\Wildberries\Data\WildberriesMarketData;
use Illuminate\Foundation\Http\FormRequest;

class WildberriesStoreMarketplaceRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|string|uuid',
            'name' => 'required|string|max:255',
            'token' => 'required|string',
            'legal_entity_id' => 'required|string|uuid',
            'contractor_id' => 'required|string|uuid',
            'comission_contract_id' => 'required|uuid',
            'department_id' => 'required|string|uuid',
        ];
    }

    public function toDTO(): WildberriesMarketData
    {
        return WildberriesMarketData::fromArray($this->validated());
    }
}
