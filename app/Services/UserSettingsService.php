<?php

namespace App\Services;

use DB;
use Illuminate\Support\Carbon;

class UserSettingsService
{
    public static function storeSettings(int $id): bool
    {

        return DB::table('user_settings')
            ->insert([
                'user_id' => $id,
                'inn' => null,
                'language' => 'ru',
                'printing_documents' => 0,
                'additional_fields' => 0,
                'start_screen' => null,
                'update_reports_automatically' => 0,
                'signature_sent_emails' => 0,
                'created_at' => Carbon::now()
            ]);
    }

}
