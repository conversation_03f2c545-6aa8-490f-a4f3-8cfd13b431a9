<?php

namespace App\Services\Api\Internal\Contractors\ContractorsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;

class ContractorDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public string $title,
        public ?string $statusId,
        public string $departmentId,
        public string $employeeId,
        public ?bool $isBuyer = false,
        public array $address = [],
        public array $detail = [],
        public array $accounts = [],
        public array $contacts = [],
        public array $contractorGroups = [],
        public ?bool $sharedAccess = false,
        public ?bool $isSupplier = false,
        public ?string $resourceId = null,
        public ?string $cabinetId = null,
        public ?string $phone = null,
        public ?string $fax = null,
        public ?string $email = null,
        public ?string $description = null,
        public ?string $code = null,
        public ?string $externalCode = null,
        public ?string $discountAndPrices = null,
        public ?string $discountCardNumber = null,
        public ?array $files = [],
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'title' => $this->title,
            'status_id' => $this->statusId,
            'is_buyer' => $this->isBuyer,
            'is_supplier' => $this->isSupplier,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'email' => $this->email,
            'description' => $this->description,
            'code' => $this->code,
            'external_code' => $this->externalCode,
            'discounts_and_prices' => $this->discountAndPrices,
            'discount_card_number' => $this->discountCardNumber,
            'shared_access' => $this->sharedAccess,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'title' => $this->title,
            'status_id' => $this->statusId,
            'is_buyer' => $this->isBuyer,
            'is_supplier' => $this->isSupplier,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'email' => $this->email,
            'description' => $this->description,
            'code' => $this->code,
            'external_code' => $this->externalCode,
            'discounts_and_prices' => $this->discountAndPrices,
            'discount_card_number' => $this->discountCardNumber,
            'shared_access' => $this->sharedAccess,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
        ];
    }

    public function toInsertAdressArray(string $contractorId): array
    {
        return [
            'id' => $this->generateUuid(),
            'contractor_id' => $contractorId,
            'postcode' => $this->address['postcode'] ?? null,
            'country' => $this->address['country'] ?? null,
            'region' => $this->address['region'] ?? null,
            'city' => $this->address['city'] ?? null,
            'street' => $this->address['street'] ?? null,
            'house' => $this->address['house'] ?? null,
            'office' => $this->address['office'] ?? null,
            'other' => $this->address['other'] ?? null,
            'comment' => $this->address['comment'] ?? null
        ];
    }

    public function toInsertDetailArray(string $detailId, string $contractorId): array
    {
        return [
            'id' => $detailId,
            'contractor_id' => $contractorId,
            'type' => $this->detail['type'] ?? null,
            'vat_rate_id' => $this->detail['vat_rate_id'] ?? null,
            'inn' => $this->detail['inn'] ?? null,
            'kpp' => $this->detail['kpp'] ?? null,
            'ogrn' => $this->detail['ogrn'] ?? null,
            'okpo' => $this->detail['okpo'] ?? null,
            'full_name' => $this->detail['full_name'] ?? null,
            'firstname' => $this->detail['firstname'] ?? null,
            'patronymic' => $this->detail['patronymic'] ?? null,
            'lastname' => $this->detail['lastname'] ?? null,
            'ogrnip' => $this->detail['ogrnip'] ?? null,
            'certificate_number' => $this->detail['certificate_number'] ?? null,
            'certificate_date' => $this->detail['certificate_date'] ?? null,
            'taxation_type' => $this->detail['taxation_type'] ?? null,
        ];
    }


    public function toInsertDetailAddressArray(string $detailId): array
    {
        return [
            'id' => $this->generateUuid(),
            'contractor_detail_id' => $detailId,
            'postcode' => $this->detail['address']['postcode'] ?? null,
            'country' => $this->detail['address']['country'] ?? null,
            'region' => $this->detail['address']['region'] ?? null,
            'city' => $this->detail['address']['city'] ?? null,
            'street' => $this->detail['address']['street'] ?? null,
            'house' => $this->detail['address']['house'] ?? null,
            'office' => $this->detail['address']['office'] ?? null,
            'other' => $this->detail['address']['other'] ?? null,
            'comment' => $this->detail['address']['comment'] ?? null
        ];
    }



    public static function fromArray(array $data): self
    {
        return new self(
            title: $data['title'],
            statusId: $data['status_id'] ?? null,
            departmentId: $data['department_id'],
            employeeId: $data['employee_id'],
            isBuyer: $data['is_buyer'] ?? false,
            address: $data['address'] ?? [],
            detail: $data['detail'] ?? [],
            accounts: $data['accounts'] ?? [],
            contacts: $data['contacts'] ?? [],
            contractorGroups: $data['contractor_groups'] ?? [],
            sharedAccess: $data['shared_access'] ?? false,
            isSupplier: $data['is_supplier'] ?? false,
            resourceId: $data['id'] ?? null,
            cabinetId: $data['cabinet_id'] ?? null,
            phone: $data['phone'] ?? null,
            fax: $data['fax'] ?? null,
            email: $data['email'] ?? null,
            description: $data['description'] ?? null,
            code: $data['code'] ?? null,
            externalCode: $data['external_code'] ?? null,
            discountAndPrices: $data['discounts_and_prices'] ?? null,
            discountCardNumber: $data['discount_card_number'] ?? null,
            files: $data['files'] ?? [],
        );
    }
}
