<?php

namespace App\Services\Api\Internal\Workspace\BookmarksService\Handlers;

use App\Contracts\Repositories\BookmarkRepositoryContract;
use App\Traits\HasOrderedUuid;

class BookmarkDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly BookmarkRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
