<?php

namespace App\Services\Api\Internal\Workspace\BookmarksService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\BookmarkRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Services\Api\Internal\Workspace\BookmarksService\DTO\BookmarkDto;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\Auth;
use InvalidArgumentException;
use RuntimeException;

class BookmarkCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly BookmarkRepositoryContract $repository,
        private readonly EmployeeRepositoryContract $employeeRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof BookmarkDto) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }

        $employee = $this->employeeRepository->getByUserIdAndCabinet(Auth::id(), $dto->cabinetId);
        if (!$employee) {
            throw new RuntimeException('Employee not found');
        }

        $this->repository->insert($dto->toInsertArray($this->resourceId, $employee->id));

        return $this->resourceId;
    }
}
