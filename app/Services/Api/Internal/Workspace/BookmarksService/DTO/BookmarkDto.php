<?php

namespace App\Services\Api\Internal\Workspace\BookmarksService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class BookmarkDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public string $name,
        public ?string $cabinetId,
        public ?string $entity,
        public ?string $resourceId,
        public string $filters = '{}',
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'employee_id' => $employeeId,
            'name' => $this->name,
            'entity' => $this->entity,
            'filters' => $this->filters
        ];
    }
    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            name: $data['name'],
            cabinetId: $data['cabinet_id'] ?? null,
            entity: $data['entity'] ?? null,
            resourceId: $data['resource_id'] ?? null,
            filters: $data['filters'] ?? '{}'
        );
    }
}
