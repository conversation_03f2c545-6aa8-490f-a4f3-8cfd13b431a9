<?php

namespace App\Services\Api\Internal\Workspace\DepartmentsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Traits\HasOrderedUuid;

class DepartmentsCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly DepartmentsRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
