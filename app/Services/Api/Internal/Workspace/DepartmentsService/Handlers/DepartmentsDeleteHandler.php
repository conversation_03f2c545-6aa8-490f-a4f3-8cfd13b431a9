<?php

namespace App\Services\Api\Internal\Workspace\DepartmentsService\Handlers;

use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Traits\HasOrderedUuid;

class DepartmentsDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly DepartmentsRepositoryContract $repository
    ) {
    }
    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
