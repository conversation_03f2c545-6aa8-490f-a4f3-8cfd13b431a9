<?php

namespace App\Services\Api\Internal\Workspace\DepartmentsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class DepartmentDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $name,
        public ?string $resourceId,
        public string $sorting,
        public ?string $salesChannelId = null
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'name' => $this->name,
            'sorting' => $this->sorting,
            'sales_channel_id' => $this->salesChannelId
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'sorting' => $this->sorting,
            'sales_channel_id' => $this->salesChannelId
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            name: $data['name'],
            resourceId: $data['resource_id'] ?? null,
            sorting: $data['sorting'],
            salesChannelId: $data['sales_channel_id'] ?? null
        );
    }
}
