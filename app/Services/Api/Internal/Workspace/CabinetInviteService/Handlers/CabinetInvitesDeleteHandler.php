<?php

namespace App\Services\Api\Internal\Workspace\CabinetInviteService\Handlers;

use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;

class CabinetInvitesDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CabinetInvitesRepositoryContract $repository
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->repository->delete($resourceId);
    }
}
