<?php

namespace App\Services\Api\Internal\Workspace\CabinetInviteService\Handlers;

use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;
use App\Exceptions\AccessDeniedException;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;

class CabinetInvitesDeclineHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CabinetInvitesRepositoryContract $repository
    ) {
    }

    /**
     * @throws NotFoundException
     * @throws AccessDeniedException
     */
    public function run(string $resourceId): void
    {
        $data = $this->repository->find($resourceId);

        if (!$data) {
            throw new NotFoundException('Invite not found');
        }

        if ($data->status !== CabinetInviteStatusEnum::WAITING->value) {
            throw new AccessDeniedException('Only waiting invites can be declined.');
        }

        $this->repository->update(
            $resourceId,
            ['status' => CabinetInviteStatusEnum::DECLINED]
        );
    }
}
