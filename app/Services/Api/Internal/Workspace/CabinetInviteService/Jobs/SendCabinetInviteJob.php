<?php

namespace App\Services\Api\Internal\Workspace\CabinetInviteService\Jobs;

use App\Mail\CabinetInviteMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendCabinetInviteJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    /**
     * Create a new job instance.
     */
    public function __construct(private readonly array $data)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = DB::table('users')
            ->where('email', $this->data['email'])
            ->exists();

        $appUrl = config()->get('app.frontend_url');
        if (!$user) {
            $url = $appUrl . '/auth?mode=register&email=' . $this->data['email'] . '&invite=' . $this->data['token'];
        } else {
            $url = $appUrl . '/profile/invites?accept=' . $this->data['token'];
        }

        Mail::to($this->data['email'])->send(new CabinetInviteMail($url));
    }
}
