<?php

namespace App\Services\Api\Internal\Workspace\StatusesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\StatusesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Workspace\StatusesService\Handlers\StatusesCreateHandler;
use App\Services\Api\Internal\Workspace\StatusesService\Handlers\StatusesDeleteHandler;
use App\Services\Api\Internal\Workspace\StatusesService\Handlers\StatusesGetHandler;
use App\Services\Api\Internal\Workspace\StatusesService\Handlers\StatusesShowHandler;
use App\Services\Api\Internal\Workspace\StatusesService\Handlers\StatusesUpdateHandler;
use Illuminate\Support\Collection;

readonly class StatusesService implements StatusesServiceContract
{
    public function __construct(
        private StatusesCreateHandler $createHandler,
        private StatusesGetHandler $getHandler,
        private StatusesUpdateHandler $updateHandler,
        private StatusesDeleteHandler $deleteHandler,
        private StatusesShowHandler $showHandler
    ) {
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function show(string $id): object
    {
        return $this->showHandler->run($id);
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }
}
