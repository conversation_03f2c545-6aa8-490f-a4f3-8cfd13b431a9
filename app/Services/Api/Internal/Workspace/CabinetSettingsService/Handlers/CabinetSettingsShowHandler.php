<?php

namespace App\Services\Api\Internal\Workspace\CabinetSettingsService\Handlers;

use App\Contracts\Repositories\CabinetSettingRepositoryContract;

readonly class CabinetSettingsShowHandler
{
    public function __construct(
        private CabinetSettingRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $result = $this->repository->show($resourceId);
        if ($result->logo_image) {
            $result->logo_image = json_decode($result->logo_image);
        }
        return $result;
    }
}
