<?php

namespace App\Services\Api\Internal\Workspace\EmployeesService\Handlers;

use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class EmployeesGetHandler
{
    public function __construct(
        private EmployeeRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        $result = $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage,
        );

        $result['data'] = array_map(static function ($item) {
            $item->work_schedule = $item->work_schedule ? json_decode(
                $item->work_schedule,
                false,
                512,
                JSON_THROW_ON_ERROR
            ) : null;
            return $item;
        }, $result['data']);

        return $result;
    }
}
