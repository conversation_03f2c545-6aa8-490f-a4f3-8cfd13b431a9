<?php

namespace App\Services\Api\Internal\Workspace\CabinetsService\Handlers;

use App\Contracts\Repositories\CabinetsRepositoryContract;
use App\Traits\HasOrderedUuid;

class CabinetShowHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CabinetsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
