<?php

namespace App\Services\Api\Internal\Workspace\CabinetsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\CabinetsRepositoryContract;
use App\Services\Api\Internal\Workspace\CabinetsService\DTO\CabinetDTO;
use InvalidArgumentException;

readonly class CabinetUpdateHandler
{
    public function __construct(
        private CabinetsRepositoryContract $repository,
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof CabinetDto) {
            throw new InvalidArgumentException();
        }

        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
