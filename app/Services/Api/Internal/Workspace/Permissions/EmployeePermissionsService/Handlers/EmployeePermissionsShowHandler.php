<?php

namespace App\Services\Api\Internal\Workspace\Permissions\EmployeePermissionsService\Handlers;

use App\Contracts\Repositories\EmployeePermissionsRepositoryContract;
use Illuminate\Support\Collection;

readonly class EmployeePermissionsShowHandler
{
    public function __construct(
        private EmployeePermissionsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): Collection
    {
        $result =  $this->repository->show($resourceId);
        if ($result) {
            foreach ($result as $item) {
                $item->permissions = json_decode($item->permissions, true);
            }
        }
        return $result;
    }
}
