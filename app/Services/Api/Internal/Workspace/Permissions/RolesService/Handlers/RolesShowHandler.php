<?php

namespace App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers;

use App\Contracts\Repositories\RolesRepositoryContract;
use App\Exceptions\NotFoundException;

readonly class RolesShowHandler
{
    public function __construct(
        private RolesRepositoryContract $repository
    ) {
    }

    /**
     * @throws NotFoundException
     */
    public function run(string $resourceId): ?object
    {
        $result = $this->repository->show($resourceId);

        if (!$result) {
            throw new NotFoundException('Role not found in handler.');
        }
        $result->permissions = $result->permissions ? json_decode($result->permissions) : [];

        return $result;
    }
}
