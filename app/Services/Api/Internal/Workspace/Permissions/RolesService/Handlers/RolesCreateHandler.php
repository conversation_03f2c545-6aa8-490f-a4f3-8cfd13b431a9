<?php

namespace App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\PermissionsRepositoryContract;
use App\Contracts\Repositories\RolePermissionsRepositoryContract;
use App\Contracts\Repositories\RolesRepositoryContract;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\DTO\RoleDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use InvalidArgumentException;

readonly class RolesCreateHandler
{
    use HasOrderedUuid;

    public string $resourceId;

    public function __construct(
        private RolesRepositoryContract $repository,
        private RolePermissionsRepositoryContract $rolePermissionsRepository,
        private PermissionsRepositoryContract $permissionsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if ($dto instanceof RoleDTO) {

            $insertArray = [];

            $this->repository->insert(
                $dto->toInsertArray($this->resourceId)
            );

            if ($dto->permissions) {
                foreach ($dto->permissions as $permission) {
                    $requiredScope = $this->permissionsRepository->checkRequireScope($permission['id']);

                    if ($requiredScope && !isset($permission['scope'])) {
                        throw new InvalidArgumentException('Scope is required');
                    }

                    $insertArray[] = [
                        'id' => $this->generateUuid(),
                        'role_id' => $this->resourceId,
                        'permission_id' => $permission['id'],
                        'scope' => $requiredScope ? $permission['scope'] : null,
                        'created_at' => Carbon::now(),
                    ];
                }
                $this->rolePermissionsRepository
                    ->insert($insertArray);
            }

            return $this->resourceId;

        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
