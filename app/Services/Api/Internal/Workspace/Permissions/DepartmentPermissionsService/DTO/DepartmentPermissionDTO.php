<?php

namespace App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\PermissionsRepositoryContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;

class DepartmentPermissionDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public ?array $permissions,
        public string $departmentId
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        $insertArray = [];

        $permissionsRepository = app()->make(PermissionsRepositoryContract::class);

        foreach ($this->permissions as $permission) {

            $requiredScope = $permissionsRepository->checkRequireScope($permission['id']);

            if ($requiredScope && !isset($permission['scope'])) {
                throw new InvalidArgumentException('Scope is required');
            }

            $insertArray[] = [
                'id' => $this->generateUuid(),
                'department_id' => $this->departmentId,
                'permission_id' => $permission['id'],
                'scope' => $requiredScope ? $permission['scope'] : null,
            ];
        }

        return $insertArray;
    }

    /**
     * @throws BindingResolutionException
     */
    public function toUpdateArray(): array
    {
        $upsertArray = [];

        $permissionsRepository = app()->make(PermissionsRepositoryContract::class);

        foreach ($this->permissions as $permission) {

            $requiredScope = $permissionsRepository->checkRequireScope($permission['permission_id']);

            if ($requiredScope && !isset($permission['scope'])) {
                throw new InvalidArgumentException('Scope is required');
            }

            $upsertArray[] = [
                'id' => $permission['id'] ?? $this->generateUuid(),
                'department_id' => $this->departmentId,
                'permission_id' => $permission['permission_id'],
                'scope' => $requiredScope ? $permission['scope'] : null,
            ];
        }
        return $upsertArray;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            permissions: $data['permissions'] ?? [],
            departmentId: $data['department_id']
        );
    }
}
