<?php

namespace App\Services\Api\Internal\Other\ExcelService;

use App\Traits\HasOrderedUuid;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Http\File;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExportExcelService
{
    use HasOrderedUuid;

    public function generatePdfFile(string $cabinetId, array $dataArray, string  $fileName): void
    {

        $inputFileName = storage_path('app/public/excel/template_' . $fileName . '.xlsx');
        $spreadsheet = IOFactory::load($inputFileName);

        $dataArray['sheet'][0]['data']['time'] = now()->format('d.m.Y H:i');

        $sheet = $spreadsheet->getSheet(0);

        // Вставляем колонки с динамическими данными по горизонтали
        $this->processForeachHorizon($sheet, $dataArray['sheet'][0]);

        // Сохраняем во временный файл
        $tempFilePath = tempnam(sys_get_temp_dir(), 'xlsx_');
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFilePath);

        // Загружаем временный файл и получаем активный лист для второго метода
        $spreadsheetNew = IOFactory::load($tempFilePath);

        $sheet = $spreadsheetNew->getSheet(0);

        // Вставляем основные данные в таблицу
        $this->processForeach($sheet, $dataArray['sheet'][0]);

        // Удаляем временный файл
        unlink($tempFilePath);

        // Настройки для HTML-контента и кодировки width: 100%;
        $htmlContent = '<h2>1111Excel Data</h2>';
        $htmlContent .= '<style>
                            body { font-family: DejaVu Sans; }
                            table { border-collapse: collapse;   page-break-inside: auto; }
                            tr { page-break-inside: avoid; page-break-after: auto; }
                            th, td { border: 1px solid #000; padding: 4px; word-wrap: break-word; font-size: 12px; }
                            h2, h3 { font-size: 14px; page-break-before: always; }
                        </style>';

        $htmlContent .= '<h3>Лист: ' . htmlspecialchars($sheet->getTitle()) . '</h3>';
        $htmlContent .= '<table>';

        foreach ($sheet->getRowIterator() as $row) {
            $htmlContent .= '<tr>';
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);

            foreach ($cellIterator as $cell) {
                $cellValue = $cell->getFormattedValue(); // Получаем форматированное значение ячейки
                $htmlContent .= '<td>' . htmlspecialchars($cellValue) . '</td>';
            }
            $htmlContent .= '</tr>';
        }

        $htmlContent .= '</table>';

        // Настройки Dompdf
        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $options->set('defaultFont', 'DejaVu Sans'); // Устанавливаем шрифт, поддерживающий кириллицу

        // Создаем экземпляр Dompdf
        $dompdf = new Dompdf($options);

        // Загружаем HTML в Dompdf
        $dompdf->loadHtml($htmlContent);

        // Устанавливаем размер страницы и ориентацию на альбомную (landscape, portrait)
        $dompdf->setPaper('A4', 'landscape');

        // Рендерим PDF
        $dompdf->render();

        $pdfContent = $dompdf->output();

        // // Масштабирование содержимого страницы
        // $dompdf->set_option('isPhpEnabled', true); // Если требуется, включить поддержку PHP внутри Dompdf

        // // Рендерим PDF с возможностью масштабирования содержимого
        // $canvas = $dompdf->getCanvas();
        // $canvas->scale(0.75, 0.75, 0.75, 0.75); // Масштабирование страницы до 75% (при необходимости отрегулируйте значение)

        $date = now()->format('d_m_Y_H_i_s');

        $tempFileName = 'pdf_'.$fileName.'_'. $date.'.pdf';
        $tempPath  = storage_path('app/public/excel/' . $tempFileName);

        // Сохраняем PDF файл временно на локальном диске
        file_put_contents($tempPath, $pdfContent);

        $path = Storage::disk('s3-docs')->put('pdf', new File($tempPath));
        // $store = Storage::disk('public')->put('pdf', $dompdf->output());

        if ($path) {
            DB::table('export')->insert([
                'id' => $this->generateUuid(),
                'title' => __('Экспорт ' . $fileName . ' в PDF'),
                'cabinet_id' => $cabinetId,
                'description' => __(''),
                'file' => $tempFileName,
                'path' => $path,
                'mime_type' => 'pdf',
                'log' => __(''),
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        unlink($tempPath);

    }

    public function generateExcelFile(string $cabinetId, array $dataArray, string $fileName): void
    {

        $inputFileName = storage_path('app/public/excel/template_' . $fileName . '.xlsx');

        $spreadsheet = IOFactory::load($inputFileName);

        // Проходим по страницам из dataArray['sheet']
        foreach ($dataArray['sheet'] as $index => $sheetData) {

            $sheetData['data']['time'] = now()->format('d.m.Y H:i');

            $sheet = $spreadsheet->getSheet($index);

            // Вставляем колонки с динамическими данными по горизонтали
            $this->processForeachHorizon($sheet, $sheetData);

            // // Сохраняем во временный файл
            // $tempFilePath = tempnam(sys_get_temp_dir(), 'xlsx_');
            // $writer = new Xlsx($spreadsheet);
            // $writer->save($tempFilePath);

            // // Загружаем временный файл и получаем активный лист для второго метода
            // $spreadsheetNew = IOFactory::load($tempFilePath);

            // Сохраняем во временный файл
            $tempFile_products = tmpfile();

            $writer = new Xlsx($spreadsheet);
            $writer->save($tempFile_products);

            $date = now()->format('d_m_Y_H_i_s');

            $tempFileName = 'tempFile_'.$fileName.'_'. $date.'.xlsx';

            // Сохраняем Временный файл с вставленными динамическими колонками
            Storage::disk('public')->put('excel/'.$tempFileName, $tempFile_products);

            $inputtempFile_products = storage_path('app/public/excel/'.$tempFileName);

            $spreadsheetNew = IOFactory::load($inputtempFile_products);

            $sheet = $spreadsheetNew->getSheet($index);

            // Вставляем основные данные в таблицу
            $this->processForeach($sheet, $sheetData);

            // // Обновляем текущий лист в исходной книге
            $spreadsheet->removeSheetByIndex($index);
            $spreadsheet->addExternalSheet($sheet, $index);

            // Удаляем временный файл
            unlink($inputtempFile_products);
        }

        $spreadsheet->setActiveSheetIndex(0);

        // Создаем временный путь для сохранения файла
        $date = now()->format('d_m_Y_H_i_s');
        $finalFileName = 'new_' . $fileName . '_' . $date . '.xlsx';
        $tempPath = storage_path('app/public/excel/' . $finalFileName);

        // Сохраняем файл временно на локальном диске
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempPath);

        // Загружаем файл в S3
        $s3Path = Storage::disk('s3-docs')->put($cabinetId . '/docs', new File($tempPath));

        // $s3Path = Storage::disk('s3-docs')->putFileAs('docs', new File($tempPath), $finalFileName);

        // Удаляем временный файл с локального диска
        // unlink($tempPath);

        if ($s3Path) {
            DB::table('export')->insert([
                'id' => $this->generateUuid(),
                'title' => __('Экспорт ' . $fileName . ' в Excel'),
                'cabinet_id' => $cabinetId,
                'description' => __('Товары экспортированы'),
                'file' => $finalFileName,
                'path' => $s3Path,
                'mime_type' => 'xlsx',
                'log' => __(''),
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

    }

    public function processForeachHorizon(mixed $sheet, array $data): void
    {
        $cellCollection = $sheet->getCellCollection()->getCoordinates();

        foreach ($cellCollection as $cell) {
            $cellValue = $sheet->getCell($cell)->getValue();

            if (strpos($cellValue, '{foreachHorizon') !== false) {
                $matches = [];

                if (preg_match('/\{foreachHorizon from=\$([a-zA-Z0-9_.]+) item=\$([a-zA-Z0-9_]+\(.*?\)(?:&[а-яА-Яa-zA-Z0-9_]+\(.*?\))*) var=([a-zA-Z0-9_]+)(?: transform=([0-9]+))?\}/m', $cellValue, $matches)) {
                    $dataKey = $matches[1];
                    $itemKey = $matches[2];
                    $var = $matches[3];
                    $transform = $matches[4] ?? null;

                    // Получаем массив для итерации
                    $array = $this->getValueFromArray($data, $dataKey);

                    is_array($array) ? $array : $array->toArray();

                    if (isset($array)) {
                        $numValues = count($array);
                        $startColumnIndex = Coordinate::columnIndexFromString($sheet->getCell($cell)->getColumn());
                        $row = $sheet->getCell($cell)->getRow();

                        // Найдем закрывающий тег `{/endforeachHorizon}`
                        $endCell = $this->findEndTag($sheet, $row, $startColumnIndex);
                        if (!$endCell) {
                            continue; // Пропускаем, если закрывающий тег не найден
                        }

                        $endColumnIndex = Coordinate::columnIndexFromString($sheet->getCell($endCell)->getColumn());

                        // Разделяем параметры по '&' и обрабатываем каждую часть
                        $itemKeys = explode('&', $itemKey);
                        $itemCount = count($itemKeys);

                        $columnsToAdd = $itemCount * ($numValues - ($endColumnIndex - $startColumnIndex - 1));
                        if ($columnsToAdd > 0) {
                            $sheet->insertNewColumnBefore(Coordinate::stringFromColumnIndex(($endColumnIndex)), $columnsToAdd);
                        }
                        // dd($startColumnIndex, $endCell, $endColumnIndex, $columnsToAdd);

                        foreach ($array as $index => $value) {
                            foreach ($itemKeys as $i => $iValue) {
                                $startColumnIndex++;
                                $currentColumn = Coordinate::stringFromColumnIndex($startColumnIndex);

                                // Разбираем текущий параметр на ключ и название столбца
                                if (preg_match('/([a-zA-Z0-9_]+)\(([^)]+)\)/', $itemKeys[$i], $paramMatches)) {
                                    $parameterKey = trim($paramMatches[1]); // Ключ параметра
                                    $columnName = trim($paramMatches[2]);   // Название столбца

                                    // Устанавливаем название столбца в текущую ячейку
                                    $sheet->setCellValue($currentColumn . $row, $columnName.' ('.$value->name.')');

                                    // Установка значений
                                    $valueItem = '{$' . $var . '.' . $parameterKey . '.' . $value->id;

                                    // Если transform равно "100", добавляем "/100" перед закрывающей скобкой
                                    if ($transform === '100') {
                                        $valueItem .= '/100';
                                    }

                                    // Завершаем значение с закрывающей скобкой
                                    $valueItem .= '}';

                                    $sheet->setCellValue($currentColumn . ($row + 2), $valueItem);
                                }
                            }
                        }

                        // Удаляем колонки с началом и концом цикла
                        $removeColumnEndCell = Coordinate::stringFromColumnIndex($endColumnIndex + $numValues * $itemCount);
                        $sheet->removeColumn($sheet->getCell($removeColumnEndCell . $row)->getColumn());
                        $sheet->removeColumn($sheet->getCell($cell)->getColumn());
                        // dump($cell, $endColumnIndex, $numValues);
                        // Рекурсивный вызов для обработки других возможных циклов
                        $this->processForeachHorizon($sheet, $data);
                    }
                }

            }
        }

    }

    // Функция для поиска закрывающего тега {/endforeachHorizon} в текущей строке
    public function findEndTag(mixed $sheet, string $row, int $startColumnIndex): mixed
    {

        foreach ($sheet->getColumnIterator() as $column) {
            $columnIndex = Coordinate::columnIndexFromString($column->getColumnIndex());

            if ($columnIndex > $startColumnIndex) {
                $cellValue = $sheet->getCell($column->getColumnIndex() . $row)->getValue();
                if ($cellValue === '{/endforeachhorizon}') {
                    return $column->getColumnIndex() . $row;
                }
            }
        }
        return null;
    }

    // Обработка переменных assign
    public function processAssign(mixed $sheet, array &$variables): void
    {
        foreach ($sheet->getCellCollection()->getCoordinates() as $cell) {
            $value = $sheet->getCell($cell)->getValue();

            // Обработка переменных assign
            if (preg_match('/\{assign var="([a-zA-Z0-9_]+)" type=([a-zA-Z0-9_]+) value=([a-zA-Z0-9_+-.]+)\}/', $value, $matches)) {
                $varName = $matches[1];
                $type = $matches[2];
                $varValue = $matches[3];

                // Увеличение переменных (если используется ++, --)
                if (strpos($varValue, '++') !== false) {
                    $varValue = $variables[$varName] ?? 0;
                    $variables[$varName] = ++$varValue;
                } elseif (strpos($varValue, '--') !== false) {
                    $varValue = $variables[$varName] ?? 0;
                    $variables[$varName] = --$varValue;
                } else {
                    $variables[$varName] = $varValue;
                }
                dump($value, $varValue);
                // Замена значения в шаблоне
                $sheet->setCellValue($cell, '');
            }
            // dd($value, $varValue);
        }
    }

    // Рекурсивная функция для получения значения из многомерного массива
    public function getValueFromArray(array $dataArray, string $keyPath): mixed
    {
        $keys = explode('.', $keyPath);
        $value = $dataArray;
        foreach ($keys as $key) {
            if (isset($value[$key])) {
                $value = $value[$key];
            } else {
                return null; // Если ключ не найден
            }
        }
        return $value;
    }

    // Обработка циклов foreach в шаблоне
    public function processForeach(mixed $sheet, array $dataArray): void
    {
        $cellCollection = $sheet->getCellCollection()->getCoordinates();

        $foreachStart = null;
        $foreachEnd = null;
        $foreachContent = [];
        $afterForeach = [];

        // Переменные, которые находятся до, после и между циклами
        $variablesBeforeLoop = [];
        $variablesAfterLoop = [];

        // Стилизуем ячейку Дата и других ячеек
        $styleArray = [
            'font' => [
                'italic' => true,
                'color' => ['rgb' => '000000'], // Синий цвет текста
                'size' => 10,
                'name' => 'Arial'
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'fff', // Зеленый фон
                ],
            ],
        ];

        // Находим все ячейки с переменными и блоки foreach
        foreach ($cellCollection as $cell) {
            $cellValue = $sheet->getCell($cell)->getValue();  // Используем getValue для проверки на переменные и foreach

            // Если находим начало цикла
            if (preg_match('/\{foreach from=\$([a-zA-Z0-9_.]+) item=\$([a-zA-Z0-9_]+)\}/', $cellValue, $matches)) {
                $foreachStart = $sheet->getCell($cell)->getRow();
                $arrayPath = $matches[1]; // Путь к массиву, например, data.products
                $itemName = $matches[2];  // Имя элемента, например, product + id
                continue;
            }

            // Если находим конец цикла
            if (preg_match('/\{\/endforeach\}/', $cellValue)) {
                $foreachEnd = $sheet->getCell($cell)->getRow();
            }

            // Если находим переменные до цикла
            if ($foreachStart === null && preg_match_all('/\{\$item.([a-zA-Z0-9_.]+)\}/', $cellValue, $matches)) {
                $variablesBeforeLoop[] = $cell;
            }
            // Если находим переменные после цикла
            if ($foreachStart !== null && $foreachEnd !== null) {
                if (preg_match_all('/\{\$item.([a-zA-Z0-9_.]+)\}/', $cellValue, $matches)) {
                    $variablesAfterLoop[] = $cell;
                }

                if (strpos($cellValue, '{$sum}') !== false) {
                    $variablesAfterLoop[] = $cell;
                }
            }

            // Если находимся между {foreach} и {/foreach}, запоминаем содержимое
            if ($foreachStart !== null && $foreachEnd === null) {
                $foreachContent[] = $cell;
            }
        }

        if (isset($arrayPath)) {

            $array = $this->getValueFromArray($dataArray, $arrayPath); // Получаем массив для итерации
            // dd($array);
            //  Смещение переменных после цикла
            $rowsInserted = ($array !== null) ? count($array) : 0; // Количество вставленных строк
            if (count($variablesAfterLoop) > 1) {
                $first_element  = array_shift($variablesAfterLoop); // Убираем первый элемент массива  {/endforeach}
            }
            $variablesAfterLoop = array_reverse($variablesAfterLoop); // Переворачиваем массив для перезаписи

            // Выполняем сдвиг переменных после цикла
            $shiftedCells = []; // Массив для хранения информации о переменных после сдвига

            foreach ($variablesAfterLoop as $cell) {
                $originalRow = $sheet->getCell($cell)->getRow(); // Оригинальная строка переменной
                $newRow = $originalRow + $rowsInserted; // Новая строка после смещения

                $currentCol = $sheet->getCell($cell)->getColumn(); // Столбец переменной
                $cellValue = $sheet->getCell($cell)->getValue(); // Значение переменной

                // Сохраняем информацию о сдвинутых ячейках
                $shiftedCells[] = [
                    'column' => $currentCol,
                    'newRow' => $newRow,
                    'value' => $cellValue,
                ];

                // Очищаем старую ячейку
                $sheet->setCellValue($currentCol . $originalRow, '');
            }


            // Обрабатываем переменные до цикла
            foreach ($variablesBeforeLoop as $cell) {
                $cellValue = $sheet->getCell($cell)->getValue();
                $newValue = $this->replaceVariables($cellValue, $dataArray); // Функция для замены переменных
                $sheet->getCell($cell)->setValue($newValue);
            }

            if (!empty($array)) {
                // Начальная строка для вставки данных
                $startRow = $foreachEnd + 1;
                $rowNumber = 1; // Начальный номер строки

                foreach ($array as $element) {
                    foreach ($foreachContent as $cell) {
                        // Получаем оригинальное значение ячейки, содержащее переменные
                        $originalValue = $sheet->getCell($cell)->getFormattedValue();
                        // Создаем копию этого значения для замены переменных
                        $newValue = $originalValue;

                        // Заменяем переменные на значения из текущего элемента массива
                        if (isset($itemName)) {
                            foreach ($element as $key => $value) {

                                if (preg_match('/\{\$' . $itemName . '\.' . $key . '\/100\}/', $newValue)) {

                                    if (is_numeric($value)) {
                                        $value = (int)$value / 100;
                                    }

                                    $newValue = str_replace('{$' . $itemName . '.' . $key . '/100}', (string) $value, $newValue);
                                } elseif (strpos($newValue, '{$'.$itemName.'.image}') !== false && $key == 'image') {
                                    $imageColumn = $sheet->getCell($cell)->getColumn();
                                    if ($value !== null) {
                                        $this->setImage($sheet, $imageColumn . $startRow, $value);
                                        $sheet->getRowDimension($startRow)->setRowHeight(50);
                                    }
                                } else {
                                    $newValue = str_replace('{$'.$itemName.'.'.$key.'}', $value, $newValue);
                                }

                            }
                        }
                        // Добавляем номер строки
                        $newValue = str_replace('{$rowNumber}', (string)$rowNumber, $newValue);

                        // Вставляем новые значения в соответствующие ячейки
                        $currentRow = $startRow;
                        $currentCol = $sheet->getCell($cell)->getColumn();

                        $sheet->setCellValue($currentCol . $currentRow, $newValue);
                    }
                    // dd(1);
                    // Переход к следующей строке для следующего элемента массива
                    $startRow++;
                    $rowNumber++;
                }

                // Удаляем строки {foreach} и {/foreach}
                $sheet->removeRow($foreachEnd);
                $sheet->removeRow($foreachStart + 1);
                $sheet->removeRow($foreachStart);

                // Подставляем данные в сдвинутые ячейки после основного цикла
                foreach ($shiftedCells as $shiftedCell) {
                    $sheet->setCellValue($shiftedCell['column'] . $shiftedCell['newRow'], $this->replaceVariables($shiftedCell['value'], $dataArray));
                    $sheet->getStyle($shiftedCell['column'] . $shiftedCell['newRow'])->applyFromArray($styleArray);
                }

                // Удаляем незаполненные переменные
                if (isset($itemName)) {
                    $this->processRemowForeach($sheet, $itemName);
                }
            }
        }

        $this->processSum($sheet, $foreachStart);
    }


    public function processSum(mixed $sheet, string $foreachStart): void
    {
        $cellCollection = $sheet->getCellCollection()->getCoordinates();

        foreach ($cellCollection as $cell) {
            $cellValue = $sheet->getCell($cell)->getValue();
            // dump($cellValue);
            // Проверка наличия параметра {$sum} в ячейке
            if (strpos($cellValue, '{$sum}') !== false) {
                $column = $sheet->getCell($cell)->getColumn();
                $rowStart = $foreachStart; // Начальная строка для суммирования
                $rowEnd = $sheet->getHighestRow(); // Конечная строка для суммирования
                // dd($rowEn);
                $sum = 0; // Переменная для хранения суммы

                // Суммируем все числовые значения в указанном столбце
                for ($row = $rowStart; $row <= $rowEnd; $row++) {
                    $cellToSum = $sheet->getCell($column . $row);
                    $value = $cellToSum->getValue();

                    // Проверка, является ли значение числом, и добавление его к сумме
                    if (is_numeric($value)) {
                        $sum += $value;
                    }
                }

                // Подставляем вычисленную сумму в ячейку с параметром {$sum}
                $sheet->setCellValue($cell, $sum);
            }
        }
    }

    public function setImage(mixed $sheet, string $cell, string $path): void
    {
        if ($path !== null) {

            // Создание объекта для изображения
            $drawing = new Drawing();
            $drawing->setName('Sample Image');
            $drawing->setDescription('Sample Image');
            // Укажите путь к изображению
            $finalFile = storage_path('app/public/' . $path);
            // $finalFile = Storage::disk('s3-images')->temporaryUrl($path, now()->addMinutes(5));

            $drawing->setPath($finalFile); // Локальный путь к изображению
            // Установите координаты (ячейка)
            $drawing->setCoordinates($cell);
            // Установите смещение, чтобы не пересекать текст
            $drawing->setOffsetX(3);
            $drawing->setOffsetY(3);
            // Устанавливаем высоту и ширину изображения (опционально)
            $drawing->setWidth(50);
            $drawing->setHeight(50);
            // Присоединяем изображение к листу
            $drawing->setWorksheet($sheet);
        }
    }

    // Удаляем незаполненные переменные
    public function processRemowForeach(mixed $sheet, string $itemName): void
    {
        foreach ($sheet->getCellCollection()->getCoordinates() as $cell) {

            $cellValue = $sheet->getCell($cell)->getValue();

            if (strpos($cellValue, '{$'.$itemName) !== false) {
                $sheet->setCellValue($cell, '');
            }
        }
    }

    // Вспомогательная функция для замены переменных
    public function replaceVariables(string $text, array $dataArray): mixed
    {
        return preg_replace_callback('/\{\$item.([a-zA-Z0-9_.]+)\}/', function ($matches) use ($dataArray) {
            return $this->getValueFromArray($dataArray, $matches[1]);
        }, $text);
    }

    // удаление
    public function destroyFile(string $cabinetId, string $id): void
    {
        $file = DB::table('export')->where('cabinet_id', $cabinetId)
        ->where('id', $id)->first();

        if ($file) {

            $del = DB::table('export')->find($id);

            Storage::disk('s3-docs')->delete($del->path);

            DB::table('export')->where('id', $id)->delete();

        }

    }
}
