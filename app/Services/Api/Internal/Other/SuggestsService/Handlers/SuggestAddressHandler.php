<?php

namespace App\Services\Api\Internal\Other\SuggestsService\Handlers;

use App\Contracts\DtoContract;
use App\Contracts\Services\Internal\DadataClientInterface;
use App\Services\Api\Internal\Other\SuggestsService\DTO\SuggestAddressDTO;
use InvalidArgumentException;

readonly class SuggestAddressHandler
{
    public function __construct(
        private DadataClientInterface $dadataClient
    ) {
    }

    public function run(DtoContract $dto): array|null|string
    {
        if (!$dto instanceof SuggestAddressDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }
        $kwargs = [
            'language' => $dto->language,
            'division' => $dto->division,
            'locations' => $dto->locations,
            'locations_boost' => $dto->locationsBoost,
            'locations_geo' => $dto->locationsGeo,
            'from_bound' => $dto->fromBound,
            'to_bound' => $dto->toBound,
        ];

        return $this->dadataClient->suggest(
            "address",
            $dto->query,
            10,
            $kwargs
        );
    }
}
