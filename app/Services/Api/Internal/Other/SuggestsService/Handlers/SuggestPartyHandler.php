<?php

namespace App\Services\Api\Internal\Other\SuggestsService\Handlers;

use App\Contracts\DtoContract;
use App\Contracts\Services\Internal\DadataClientInterface;
use App\Enums\Api\Internal\SuggestTypeEnum;
use App\Services\Api\Internal\Other\SuggestsService\DTO\SuggestPartyDTO;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

readonly class SuggestPartyHandler
{
    public function __construct(
        private DadataClientInterface $dadataClient
    ) {
    }

    public function run(DtoContract $dto): array|string|null
    {
        if (!$dto instanceof SuggestPartyDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        return Cache::remember("suggestParty|{$dto->query}", 86400, function () use ($dto) {
            $content = DB::table('suggestions')
                ->where('type', SuggestTypeEnum::PARTY)
                ->where('query', $dto->query)
                ->first();

            if (!$content) {
                $kwargs = [
                    'kpp' => $dto->kpp,
                    'branch_type' => $dto->branchType,
                    'type' => $dto->type,
                    'status' => $dto->status
                ];

                $content = $this->dadataClient->suggest("party", $dto->query, 10, $kwargs);

                DB::table('suggestions')->insert([
                    'type' => SuggestTypeEnum::PARTY,
                    'query' => $dto->query,
                    'content' => json_encode($content),
                    'created_at' => Carbon::now(),
                ]);
                return $content;
            }
            return json_decode($content->content, true, 512, JSON_THROW_ON_ERROR);
        });
    }
}
