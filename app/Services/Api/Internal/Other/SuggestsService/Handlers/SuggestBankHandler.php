<?php

namespace App\Services\Api\Internal\Other\SuggestsService\Handlers;

use App\Contracts\DtoContract;
use App\Contracts\Services\Internal\DadataClientInterface;
use App\Enums\Api\Internal\SuggestTypeEnum;
use App\Services\Api\Internal\Other\SuggestsService\DTO\SuggestBankDTO;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

readonly class SuggestBankHandler
{
    public function __construct(
        private DadataClientInterface $dadataClient
    ) {
    }

    public function run(DtoContract $dto): array|null|string
    {
        if (!$dto instanceof SuggestBankDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        return Cache::remember("suggestBank|{$dto->query}", 86400, function () use ($dto) {
            $content = DB::table('suggestions')
                ->where('type', SuggestTypeEnum::BANK)
                ->where('query', $dto->query)
                ->first();

            if (!$content) {
                $kwargs = [
                    'status' => $dto->status,
                    'type' => $dto->type,
                    'locations' => $dto->locations,
                    'locations_boost' => $dto->locationsBoost
                ];

                $content = $this->dadataClient->suggest("bank", $dto->query, 10, $kwargs);

                DB::table('suggestions')->insert([
                    'type' => SuggestTypeEnum::BANK,
                    'query' => $dto->query,
                    'content' => json_encode($content),
                    'created_at' => Carbon::now(),
                ]);
                return $content;
            }
            return json_decode($content->content, true);
        });
    }
}
