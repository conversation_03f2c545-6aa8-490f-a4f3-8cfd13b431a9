<?php

namespace App\Services\Api\Internal\Other\ExportService\Handlers;

use App\Enums\Api\Internal\TypeProductEnum;
use App\Traits\ArrayInsertAfterKey;
use App\Traits\ArrayToXmlTraits;
use App\Traits\SetAttributesTraits;
use Illuminate\Support\Facades\DB;

class GetLegalEntitiesHandler
{
    use ArrayInsertAfterKey;
    use SetAttributesTraits;
    use ArrayToXmlTraits;

    protected array $temp;
    protected int $i;
    protected $rules;

    // public function __construct($rules)
    // {
    //     $this->rules = $rules;
    // }

    // Организации Нпп="0"
    public function getLegalEntities(string $cabinetId, $rules): array
    {

        $this->rules = $rules;

        $data = DB::table('legal_entities as le')
        ->select('le.id', 'le.short_name', 'ld.inn', 'ld.kpp', 'ld.ogrn', 'ld.okpo', 'ld.full_name', 'ld.type', 'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code')
        ->leftJoin('legal_accounts as la', function ($join) {
            $join->on('le.id', '=', 'la.legal_entity_id')
                ->where('la.is_main', '=', true);
        })
        ->leftjoin('legal_details as ld', 'le.id', '=', 'ld.legal_entity_id')
        ->leftJoin('cabinet_currencies as cc', function ($join) {
            $join->on('le.cabinet_id', '=', 'cc.cabinet_id')
                ->where('cc.is_active', '=', true);
        })
        ->leftjoin('global_currencies as gc', 'cc.currency_id', '=', 'gc.id')
        ->groupBy(['le.id', 'le.short_name', 'ld.inn', 'ld.kpp', 'ld.ogrn', 'ld.okpo', 'ld.full_name', 'ld.type', 'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code'])
        ->where('le.cabinet_id', $cabinetId)->get();
        // dd($this->rules);
        $resultLegalEntities = '';

        $this->i = 0;
        $i = $this->i;

        foreach ($data as $item) {

            $resultLegalEntities = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'Организации',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Организации'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('Наименование', 'Строка', $item->short_name),
                        $this->setAttributes('ИНН', 'Строка', $item->inn),
                        $this->setAttributes('КПП', 'Строка', $item->kpp),
                        $this->setAttributes('КодПоОКПО', 'Строка', $item->okpo),
                        $this->setAttributes('НаименованиеПолное', 'Строка', $item->full_name),
                        $this->setAttributes('ЭтоГруппа', 'Булево', 'false'), // false???
                    ]
                ],
                'Свойство' => [
                    $this->setAttributes('ЮрФизЛицо', 'ПеречислениеСсылка.ЮрФизЛицо', $item->type),
                    $this->setAttributes('ОГРН', 'Строка', $item->ogrn),
                    $this->setAttributes('ПометкаУдаления', 'Булево', 'false'), // false???
                ],
            ];

            $this->temp[] = [
                    'id' => $item->id,
                    '@attributes' => [
                        'Имя' => 'Организации',
                        'Тип' => 'СправочникСсылка.Организации'
                    ],
                    'Ссылка' => [
                        '@attributes' => [
                            'Нпп' => $i
                        ],
                        'Свойство' => [
                            $this->setAttributes('Наименование', 'Строка', $item->short_name),
                            $this->setAttributes('ИНН', 'Строка', $item->inn),
                            $this->setAttributes('КПП', 'Строка', $item->kpp),
                            $this->setAttributes('КодПоОКПО', 'Строка', $item->okpo),
                            $this->setAttributes('НаименованиеПолное', 'Строка', $item->full_name),
                            $this->setAttributes('ЭтоГруппа', 'Булево', 'false'), // false???
                        ]
                    ]
            ];

            // $i++;

            // Организации
            $LegalEntities = $this->rules->addChild('Объект');
            $this->arrayToXml($resultLegalEntities, $LegalEntities, 'Свойство');

            $this->i = ++$i; // временно

            // РеализацияТоваровУслуг
            $getShipments = $this->getShipments($cabinetId);

            return $resultLegalEntities; // временно

            // dd($result, $this->temp);

        }

        $this->i = $i;

        return $resultLegalEntities;
    }



    public function getBankAccounts(string $legal_entity_id): array
    {

        $data = DB::table('legal_entities as le')
        ->select('le.id', 'le.short_name', 'la.is_main', 'ld.inn', 'ld.kpp', 'ld.okpo', 'ld.full_name', 'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code')
        ->leftjoin('legal_accounts as la', 'le.id', '=', 'la.legal_entity_id')
        ->leftjoin('legal_details as ld', 'le.id', '=', 'ld.legal_entity_id')
        ->leftJoin('cabinet_currencies as cc', function ($join) {
            $join->on('le.cabinet_id', '=', 'cc.cabinet_id')
                ->where('cc.is_active', '=', true);
        })
        ->leftjoin('global_currencies as gc', 'cc.currency_id', '=', 'gc.id')
        ->groupBy(['le.id', 'le.short_name','la.is_main', 'ld.inn', 'ld.kpp', 'ld.okpo', 'ld.full_name',  'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code'])
        ->where('le.id', $legal_entity_id)->get();
        // dd($legal_entity_id, $data);
        $result = [];

        $i = $this->i;
        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'Банки',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Банки'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('Код', 'Строка', $item->bik),
                    ]
                ],
                'Свойство' => [
                    $this->setAttributes('Наименование', 'Строка', $item->bank),
                    $this->setAttributes('КоррСчет', 'Строка', $item->correspondent_account),
                    $this->setAttributes('Адрес', 'Строка', $item->address),
                ],
            ];

            $this->temp[] = [
                'id' => $item->id,
                '@attributes' => [
                    'Имя' => 'Банки',
                    'Тип' => 'СправочникСсылка.Банки'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('Код', 'Строка', $item->bik),
                    ]
                ]

            ];

            $i++;

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'БанковскиеСчета',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.БанковскиеСчета'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('НомерСчета', 'Строка', $item->payment_account),
                    ]
                ],
                'Свойство' => [
                    $this->setAttributes('Наименование', 'Строка', $item->bank),

                    [
                        '@attributes' => [
                            'Имя' => 'Банк',
                            'Тип' => 'СправочникСсылка.Банки'
                        ],
                        'Ссылка' => [
                            '@attributes' => [
                                'Нпп' => $i
                            ],
                            'Свойство' => [
                                $this->setAttributes('Код', 'Строка', $item->bik),
                            ]
                        ],
                    ],
                    $this->setAttributes('ВидСчета', 'Строка', 'Расчетный????'),

                    [
                        '@attributes' => [
                            'Имя' => 'ВалютаДенежныхСредств',
                            'Тип' => 'СправочникСсылка.Валюты'
                        ],
                        'Ссылка' => [
                            '@attributes' => [
                                'Нпп' => $i
                            ],
                            'Свойство' => [
                                [
                                    '@attributes' => [
                                        'Имя' => 'Код',
                                        'Тип' => 'Строка'
                                    ],
                                    'Значение' => $item->num_code
                                ]
                            ]
                        ],
                    ]
                ],
            ];


            $this->temp[] = [
                'id' => $item->id,
                '@attributes' => [
                    'Имя' => (!$item->is_main) ? 'БанковскиеСчета' : 'ОсновнойБанковскийСчет',
                    'Тип' => 'СправочникСсылка.БанковскиеСчета'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('НомерСчета', 'Строка', $item->payment_account),
                    ]
                ]

            ];

            $i++;
        }

        $this->i = $i;
        // dd($result);
        return $result;
    }


    // РеализацияТоваровУслуг
    public function getShipments(string $cabinetId): array
    {

        $data = DB::table('shipments as s')
        ->select('*')
        ->where('s.cabinet_id', $cabinetId)->get();
        // dd($data);


        // id :                 идентификатор
        // created_at :         создан_ат
        // updated_at :         обновлен_ат
        // cabinet_id :         кабинета_и_дателя
        // employee_id :        сотрудника
        // number :             номер
        // date_from :          дата_от_наличия
        // status_id :          статуса
        // held :               держал
        // legal_entity_id :    юридический
        // contractor_id :      подрядчика
        // warehouse_id :       хранилища
        // sales_channel_id :   канал продаж
        // currency_id :        валюты
        // consignee_id :       получателя
        // transporter_id :     перевозчика
        // cargo_name :         имя груза
        // shipper_instructions : инструкции отправителя
        // venicle :             транспортное средство
        // venicle_number :      транспортный номер
        // total_seats :         общее количество мест
        // goverment_contract_id : правительства_контракта
        // comment :            комментарий
        // price_includes_vat : цена_включения_vat
        // overhead_cost :      сверхнормативная стоимость
        // total_cost :         общая стоимость
        // profit :             прибыль
        // total_price :        общая цена


        $resultShipments = '';

        // $i = $this->i;

        foreach ($data as $item) {

            // Валюта
            $getCurrencies = $this->getCurrencies($item->currency_id);
            // dd($getCurrencies, $this->temp);
            $rulesCurrencies = $this->rules->addChild('Объект');
            $this->arrayToXml($getCurrencies, $rulesCurrencies, 'Свойство');

            $tempSI = $this->getShipmentItem($item->id, $cabinetId);

            $i = $this->i;
            // $this->i = $i;


            $resultShipments = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'РеализацияТоваровУслуг',
                    'Нпп' =>  $i,
                    'Тип' => 'ДокументСсылка.РеализацияТоваровУслуг'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('Номер', 'Строка', $item->number, ['currency_id' => $item->currency_id]),
                        $this->setAttributes('Дата', 'Дата', $item->date_from)
                    ]
                ],
                'ТабличнаяЧасть' => $tempSI,
                'Свойство' => [
                    $this->setAttributes('ВалютаДокумента', 'СправочникСсылка.Валюты', '', ['currency_id' => $item->currency_id]),
                    $this->setAttributes('Комментарий', 'Строка', $item->comment),
                    $this->setAttributes('Контрагент', 'СправочникСсылка.Контрагенты', '', ['contractor_id' => $item->contractor_id]),
                    $this->setAttributes('ДоговорКонтрагента', 'СправочникСсылка.ДоговорыКонтрагентов', '', ['contractor_id' => $item->contractor_id]),
                    $this->setAttributes('КратностьВзаиморасчетов', 'Число', '', ['currency_id' => $item->currency_id]),
                    $this->setAttributes('КурсВзаиморасчетов', 'Число', '', ['currency_id' => $item->currency_id]),
                    // $this->setAttributes('НДСВключенВСтоимость', 'Булево', ($item->price_includes_vat) ? 'true' : 'false'),
                    $this->setAttributes('Организация', 'СправочникСсылка.Организации', '', ['legal_entity_id' => $item->legal_entity_id]),
                    // $this->setAttributes('РегистрироватьЦеныПоставщика', 'Булево', ''),
                    $this->setAttributes('Склад', 'СправочникСсылка.Склады', '', ['warehouse_id' => $item->warehouse_id]),
                    $this->setAttributes('СуммаВключаетНДС', 'Булево', ($item->price_includes_vat) ? 'true' : 'false'),
                    $this->setAttributes('СуммаДокумента', 'Число', round((int)$item->total_price / 100, 2)),
                    $this->setAttributes('УчитыватьНДС', 'Булево', ($item->price_includes_vat) ? 'true' : 'false'), // true Перепроверить!
                    $this->setAttributes('ВидОперации', 'ПеречислениеСсылка.ВидыОперацийПоступлениеТоваровУслуг', 'ПокупкаКомиссия'),
                    $this->setAttributes('ПометкаУдаления', 'Булево', 'false'), // false Доделать
                    $this->setAttributes('СпособЗачетаАвансов', 'ПеречислениеСсылка.СпособыЗачетаАвансов', 'НеЗачитывать'),
                ]
            ];

            $i++;
            $this->i = $i;



            // $link = $this->rules->xpath('//Объект[@Нпп="1"]');
            // dd($link);

            // $link['Нпп'] = 2;

            // // Находим элемент <Значение> внутри <Свойство Имя="Номер"> и изменяем его текст
            // $propertyNumber = $link->xpath('Свойство[@Нпп="Номер"]')[0];
            // $propertyNumber->Значение = '12345';


            // dd($this->rules);
            // Склады
            $getWarehouses = $this->getWarehouses($item->warehouse_id);

            $rulesWarehouses = $this->rules->addChild('Объект');
            $this->arrayToXml($getWarehouses, $rulesWarehouses, 'Свойство');


            // Банки и счета
            $getBankAccounts = $this->getBankAccounts($item->legal_entity_id);

            // dd($getBankAccounts, $this->temp);

            foreach ($getBankAccounts as $objectBank) {

                foreach ($this->temp as $temp) {

                    $newTemp = $temp;
                    // dump($object['id'], $temp['id']);
                    if ($newTemp['id'] == $objectBank['id'] && $newTemp['@attributes']['Имя'] == 'Организации' && $objectBank['@attributes']['ИмяПравила'] == 'БанковскиеСчета') {
                        unset($newTemp['id']);
                        $newTemp['@attributes']['Имя'] = 'Владелец';
                        array_unshift($objectBank['Свойство'], $newTemp);
                    }
                }
                unset($objectBank['id']);

                $rulesBank = $this->rules->addChild('Объект');
                $this->arrayToXml($objectBank, $rulesBank, 'Свойство');
            }


            $getСontractors = $this->getСontractors($item->contractor_id);

            // dd($getСontractors);

            foreach ($getСontractors as $objectСontractors) {

                foreach ($this->temp as $temp) {

                    $newTemp = $temp;

                    if ($newTemp['id'] == $objectСontractors['id'] && $newTemp['Ссылка']['@attributes']['Нпп'] == $objectСontractors['@attributes']['Нпп']) {

                        $newTemp['@attributes']['Имя'] = 'ГоловнойКонтрагент';
                        unset($newTemp['id']);
                        array_push($objectСontractors['Свойство'], $newTemp);

                    }

                    $newTemp1 = $temp;

                    if ($newTemp1['id'] == $objectСontractors['id'] && $newTemp1['@attributes']['Имя'] == 'Контрагенты' && $objectСontractors['@attributes']['ИмяПравила'] == 'КонтактнаяИнформация') {

                        $newTemp1['@attributes']['Имя'] = 'Объект';
                        unset($newTemp1['id']);
                        array_unshift($objectСontractors['Свойство'], $newTemp1);

                    }
                }
                unset($objectСontractors['id']);

                $rulesСontractors = $this->rules->addChild('Объект');
                $this->arrayToXml($objectСontractors, $rulesСontractors, 'Свойство');
            }


            foreach ($this->temp as $k => $temp) {
                foreach ($this->temp as $NewTemp) {

                    $newTemp = $NewTemp;

                    if (isset($newTemp['id']) && $temp['id'] == $newTemp['id'] && $temp['@attributes']['Имя'] == 'ДоговорКонтрагента' &&  $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты') {
                        $newTemp['@attributes']['Имя'] = 'Владелец';
                        unset($newTemp['id']);
                        array_push($this->temp[$k]['Ссылка']['Свойство'], $newTemp);
                    }

                    if ($temp['@attributes']['Имя'] == 'ДоговорКонтрагента' && $newTemp['@attributes']['Имя'] == 'true' && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Валюты') {
                        $newTemp['@attributes']['Имя'] = 'ВалютаВзаиморасчетов';
                        unset($newTemp['id']);
                        unset($newTemp['value']);
                        array_push($this->temp[$k]['Ссылка']['Свойство'], $newTemp);
                    }

                    if ($temp['@attributes']['Имя'] == 'ДоговорКонтрагента' && $newTemp['@attributes']['Имя'] == 'Организации' && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Организации') {
                        $newTemp['@attributes']['Имя'] = 'Организация';
                        unset($newTemp['id']);
                        array_push($this->temp[$k]['Ссылка']['Свойство'], $newTemp);
                    }
                }
            }
            foreach ($resultShipments['Свойство'] as $k => $object) {
                // dd($object);
                foreach ($this->temp as $temp) {

                    $newTemp = $temp;

                    if (isset($object['currency_id']) && $object['currency_id'] == $temp['id']) {
                        // dd($object, $temp);
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        // unset($object['currency_id']);
                        unset($newTemp['currency_id']);
                        // $object['Ссылка'] = $newTemp['Ссылка'];
                        $resultShipments['Свойство'][$k]['Ссылка'] = $newTemp['Ссылка'];
                        // dd($obj, $newTemp);
                    }

                    if (isset($object['contractor_id']) && $object['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты' && $object['@attributes']['Имя'] == 'Контрагент') {
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        unset($newTemp['currency_id']);
                        $resultShipments['Свойство'][$k]['Ссылка'] = $newTemp['Ссылка'];
                        // dd($object['Свойство'][$j]['Ссылка'], $obj, $newTemp);
                    }

                    if (isset($object['contractor_id']) &&  $object['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Имя'] == $object['@attributes']['Имя']) {
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        unset($newTemp['currency_id']);
                        $resultShipments['Свойство'][$k]['Ссылка'] = $newTemp['Ссылка'];
                        // dd($object['Свойство'][$j], $obj, $newTemp);
                    }

                    if (isset($object['currency_id']) && $object['currency_id'] == $temp['id'] && $object['@attributes']['Имя'] == 'КратностьВзаиморасчетов') {
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        unset($resultShipments['Свойство'][$k]['Ссылка']);
                        // unset($object['Ссылка']);
                        unset($newTemp['currency_id']);
                        unset($newTemp['Ссылка']);
                        $resultShipments['Свойство'][$k]['Значение'] = $newTemp['value'];
                        // dd($object['Свойство'][$j], $obj, $newTemp);
                    }

                    if (isset($object['currency_id']) && $object['currency_id'] == $temp['id'] && $object['@attributes']['Имя'] == 'КурсВзаиморасчетов') {
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        unset($resultShipments['Свойство'][$k]['Ссылка']);
                        unset($newTemp['currency_id']);
                        unset($newTemp['Ссылка']);
                        $resultShipments['Свойство'][$k]['Значение'] = $newTemp['value'];
                        // dd($object['Свойство'][$j], $obj, $newTemp);
                    }

                    if (isset($object['legal_entity_id']) && $object['legal_entity_id'] == $temp['id'] && $object['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        unset($newTemp['id']);
                        $resultShipments['Свойство'][$k]['Ссылка'] = $newTemp['Ссылка'];
                        // dd($object['Свойство'][$j], $obj, $newTemp);
                    }

                    if (isset($object['warehouse_id']) && $object['warehouse_id'] == $temp['id'] && $object['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                        unset($resultShipments['Свойство'][$k]['Пусто']);
                        unset($newTemp['id']);
                        $resultShipments['Свойство'][$k]['Ссылка'] = $newTemp['Ссылка'];
                        // dd($object['Свойство'][$j], $obj, $newTemp);
                    }

                }


                if (isset($object['Значение']) && $object['Значение'] !== null) {
                    // dd($object['Значение']);
                    unset($resultShipments['Свойство'][$k]['Пусто']);
                }

                unset($resultShipments['Свойство'][$k]['currency_id']);
                unset($resultShipments['Свойство'][$k]['contractor_id']);
                unset($resultShipments['Свойство'][$k]['legal_entity_id']);
                unset($resultShipments['Свойство'][$k]['warehouse_id']);

            }
            unset($resultShipments['id']);
            // РеализацияТоваровУслуг
            $rulesShipments = $this->rules->addChild('Объект');
            $this->arrayToXml($resultShipments, $rulesShipments, 'Свойство');
        }

        // $this->i = $i;
        // dd($result);
        return $resultShipments;

    }


    public function getShipmentItem(string $shipmentItemId, string $cabinetId): array
    {

        $data = DB::table('shipment_items as shi')
        ->select('shi.*', 'shi.product_id', 'vr.rate', 'vr.description as vr_desc', 'p.type') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('vat_rates as vr', 'vr.id', '=', 'shi.vat_rate_id')
        ->leftjoin('products as p', 'p.id', '=', 'shi.product_id')
        ->groupBy('shi.id', 'vr.id', 'p.type') //, 'vr.rate', 'vr.description'
        ->where('shi.shipment_id', $shipmentItemId)->get();
        // dd($data->pluck('product_id'));

        // id :            идентификатор :
        // created_at :    создан_ат :
        // updated_at :    обновлен_ат :
        // shipment_id :   идентификатор_отправки :
        // product_id :    идентификатор_продукта :
        // quantity :      количество :
        // price :         цена :
        // cost :          стоимость :
        // total_cost :    общая стоимость :
        // total_price :   общая цена :
        // profit :        прибыль :
        // vat_rate_id :   ставка НДС :
        // discount :      скидка :
        // recidual :      остаточный :
        // rate :          ставка :
        // vr_desc :       виртуальный адрес :


        $products = $this->getProductsItem($data->pluck('product_id'));

        $resItem = [];

        foreach ($data as $item) {

            if ($item->rate != 0) {
                $vat_rate = 'НДС'.$item->rate;

                $sum_rate =  $this->setAttributes('СуммаНДС', 'Число', round(((int)$item->total_price * $item->rate / (100 + $item->rate)) / 100, 2));

            } else {
                $vat_rate = 'БезНДС';

                $sum_rate = [];
            }

            $resItem[] = [
                'product_id' => $item->product_id,
                // 'type' => $item->type,
                'Свойство' => [
                    $this->setAttributes('Количество', 'Число', number_format((int)$item->quantity, 1, '.', '')),
                    $this->setAttributes('НомерГТД', 'СправочникСсылка.НомераГТД', ''),
                    $this->setAttributes('СтавкаНДС', 'ПеречислениеСсылка.СтавкиНДС', $vat_rate),
                    $sum_rate,
                    $this->setAttributes('Сумма', 'Число', round((int)$item->total_price / 100, 2)),
                    $this->setAttributes('Цена', 'Число', round((int)$item->price / 100, 2)),
                    $this->setAttributes('Комиссия', 'Булево', 'false'), // false???
                ]
            ];

        }

        foreach ($resItem as $k => $object) {

            foreach ($this->temp as $temp) {

                $newTemp = $temp;
                if ($newTemp['id'] == $object['product_id']) {
                    unset($newTemp['id']);
                    // array_unshift($resItem[$k]['Свойство'], $newTemp);

                    $resItem[$k]['type'] = $newTemp['type'];
                    unset($newTemp['type']);
                    // Вставим новый массив в нужное место
                    $this->array_insert_after_key($resItem[$k]['Свойство'], 0, [$newTemp]);
                }
            }
            unset($resItem[$k]['product_id']);
        }

        $resItems = [
            [
                '@attributes' => [
                    'Имя' => 'Товары'
                ]
            ],
            [
                '@attributes' => [
                    'Имя' => 'Услуги'
                ]
            ]
        ];
        // dd($resItem);
        foreach ($resItem as $k => $object) {

            if ($object['type'] == TypeProductEnum::PRODUCT->value) {
                unset($object['type']);
                $resItems[0]['Запись'][] = $object;
            } elseif ($object['type'] == TypeProductEnum::SERVICE->value) {
                unset($object['type']);
                $resItems[1]['Запись'][] = $object;
            }
        }

        // dd($resItems);
        return $resItems;
    }


    public function getProductsItem($array = []): array
    {

        $result = '';

        $i = $this->i++;

        foreach ($array as $product_id) {

            $item = DB::table('products as p')
            ->select('p.id', 'p.title', 'p.code', 'p.type', 'p.article', 'p.tax', 'p.description', 'p.measurement_unit_id', 'mu.name', 'mu.code as code_m', 'p.deleted_at')
            ->leftjoin('measurement_units as mu', 'mu.id', '=', 'p.measurement_unit_id')
            ->groupBy(['p.id', 'p.title', 'p.code', 'p.article', 'p.tax', 'p.description', 'mu.name', 'mu.code', 'p.deleted_at'])
            ->where('p.id', $product_id)->first();
            // dd($item);

            $result = [
                '@attributes' => [
                    'ИмяПравила' => 'Номенклатура',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Номенклатура'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('Наименование', 'Строка', $item->title),
                        $this->setAttributes('ЭтоГруппа', 'Булево', 'false'),
                        $this->setAttributes('Код', 'Строка', $item->code),
                    ]
                ],
                'Свойство' => [
                    $this->setAttributes('Код', 'Строка', $item->code),
                    $this->setAttributes('Артикул', 'Строка', $item->article),
                    $this->setAttributes('ПометкаУдаления', 'Булево', $item->deleted_at),
                    $this->setAttributes('ЭтоГруппа', 'Булево', 'false'),
                    $this->setAttributes('НаименованиеПолное', 'Строка', $item->title),
                    $this->setAttributes('БазоваяЕдиницаИзмерения', 'СправочникСсылка.КлассификаторЕдиницИзмерения', '', ['measurement_unit_id' => $item->measurement_unit_id]),
                    $this->setAttributes('СтавкаНДС', 'ПеречислениеСсылка.СтавкиНДС', $item->tax),
                    $this->setAttributes('Комментарий', 'Строка', $item->description),
                    $this->setAttributes('Услуга', 'Булево', 'false'),
                ],

            ];


            $this->temp[] = [
                'id' => $item->id,
                'type' => $item->type,
                '@attributes' => [
                    'Имя' => 'Номенклатура',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Номенклатура'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('Наименование', 'Строка', $item->title),
                        $this->setAttributes('ЭтоГруппа', 'Булево', 'false'),
                        $this->setAttributes('Код', 'Строка', $item->code),
                    ]
                ]
            ];


            $i++;


            $rulesProductsItem = $this->rules->addChild('Объект');
            $this->arrayToXml($result, $rulesProductsItem, 'Свойство');

        }

        $this->i = $i;
        // dd($this->temp);
        return $result;

    }

    // Валюты Нпп="1"
    public function getCurrencies(string $id): array
    {

        $item = DB::table('global_currencies as gc')
        // ->select('ccu.id', 'ccu.currency_id', 'gc.num_code', 'gc.char_code', 'ccu.is_active', 'gc.external_id', 'gc.value')
        // ->leftjoin('global_currencies as gc', 'gc.id', '=', 'ccu.currency_id')
        // ->groupBy(['ccu.id', 'gc.num_code', 'gc.char_code', 'gc.external_id', 'gc.value'])
        ->where('gc.id', $id)->first();
        // dd($item);

        // $data = DB::table('cabinet_currencies as ccu')
        // ->select('ccu.id', 'ccu.currency_id', 'gc.num_code', 'gc.char_code', 'ccu.is_active', 'gc.external_id', 'gc.value')
        // ->leftjoin('global_currencies as gc', 'gc.id', '=', 'ccu.currency_id')
        // ->groupBy(['ccu.id', 'gc.num_code', 'gc.char_code', 'gc.external_id', 'gc.value'])
        // ->where('ccu.cabinet_id', $cabinetId)->get();
        // dd($data);

        // Преобразование данных в нужный формат
        // $result = [];

        $i = $this->i;

        // foreach ($data as $item) {
        $result = [
            'id' => $item->id,
            '@attributes' => [
                'ИмяПравила' => 'Валюты',
                'Нпп' =>  $i,
                'Тип' => 'СправочникСсылка.Валюты'
            ],
            'Ссылка' => [
                '@attributes' => [
                    'Нпп' =>  $i
                ],
                'Свойство' => $this->setAttributes('Код', 'Строка', $item->num_code),
            ],
            'Свойство' => [
                $this->setAttributes('ПометкаУдаления', 'Булево', 'false'), // ($item->is_active) ? 'false' : 'true' Нужно ли??????
                $this->setAttributes('Наименование', 'Строка', $item->char_code),
                $this->setAttributes('НаименованиеПолное', 'Строка', $item->char_code),
            ]
        ];


        $this->temp[] = [
            'id' => $item->id,
            'value' => $item->value,
            '@attributes' => [
                'Имя' => 'Валюты',
                'Тип' => 'СправочникСсылка.Валюты'
            ],
            'Ссылка' => [
                '@attributes' => [
                    'Нпп' =>  $i
                ],
                'Свойство' => $this->setAttributes('Код', 'Строка', $item->num_code),
            ]
        ];

        $i++;
        // }

        // $rules15 = $this->rules->addChild('Объект');
        // $this->arrayToXml($result, $rules15, 'Свойство');


        $this->i = $i;
        // dd($result);
        return $result;

    }


    public function getWarehouses(string $warehouse_id): array
    {

        // нужна ли группа
        // добавить софтделит

        $item = DB::table('warehouses as w')
        ->select('w.id', 'w.name', 'wa.comment')
        ->leftjoin('warehouse_addresses as wa', 'wa.id', '=', 'w.address_id')
        ->groupBy(['w.id', 'w.name', 'wa.comment'])
        ->where('w.id', $warehouse_id)->first();
        // dd($data);
        // $result = [];

        $i = $this->i;
        // foreach ($data as $item) {

        $result = [
            '@attributes' => [
                'ИмяПравила' => 'Склады',
                'Нпп' => $i,
                'Тип' => 'СправочникСсылка.Склады'
            ],
            'Ссылка' => [
                '@attributes' => [
                    'Нпп' => $i
                ],
                'Свойство' => [
                    $this->setAttributes('Наименование', 'Строка', $item->name),
                    $this->setAttributes('ЭтоГруппа', 'Булево', 'false'), // false Нужна ли группа?
                ]
            ],
            'Свойство' => [
                $this->setAttributes('ПометкаУдаления', 'Булево', 'false'), // false добавить софтделит
                $this->setAttributes('Комментарий', 'Строка', $item->comment),
                $this->setAttributes('ВидСклада', 'ПеречислениеСсылка.ВидыСкладов', 'Будет ли у нас ВидСклада???'),
            ],
        ];

        $this->temp[] = [
            'id' => $item->id,
            '@attributes' => [
                'Имя' => 'Склад',
                'Нпп' => $i,
                'Тип' => 'СправочникСсылка.Склады'
            ],
            'Ссылка' => [
                '@attributes' => [
                    'Нпп' => $i
                ],
                'Свойство' => [
                    $this->setAttributes('Наименование', 'Строка', $item->name),
                    $this->setAttributes('ЭтоГруппа', 'Булево', 'false'), // false Нужна ли группа?
                ]
            ]
        ];

        $i++;
        // }

        $this->i = $i;

        return $result;
    }


    // ДоговорыКонтрагентов Нпп="5"
    public function getСontracts(string $cabinetId): array
    {

        $data = DB::table('contractors as c')
        ->select('c.id', 'cf.title', 'c.deleted_at')
        ->leftjoin('contractors_files as cf', 'c.id', '=', 'cf.contractor_id')
        ->groupBy(['c.id'], 'cf.title')
        ->where('c.cabinet_id', $cabinetId)->get();
        // dd($data);

        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            if ($item->title !== null) {

                $result[] = [
                    'id' => $item->id,
                    '@attributes' => [
                        'ИмяПравила' => 'ДоговорыКонтрагентов',
                        'Нпп' =>  $i,
                        'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                    ],
                    'Ссылка' => [
                        '@attributes' => [
                            'Нпп' =>  $i
                        ],
                        'Свойство' => [
                            $this->setAttributes('Наименование', 'Строка', $item->title),
                            $this->setAttributes('ЭтоГруппа', 'Булево', 'false'), // false???
                            $this->setAttributes('ВидДоговора', 'ПеречислениеСсылка.ВидыДоговоровКонтрагентов', 'СПокупателем'),
                        ]
                    ],
                    'Свойство' => [
                        $this->setAttributes('ПометкаУдаления', 'Булево', ($item->deleted_at) ? 'false' : 'true'),
                        $this->setAttributes('Комментарий', 'Булево', 'Комментарий ???'),
                    ]
                ];


                $this->temp[] = [
                    'id' => $item->id,
                    '@attributes' => [
                        'Имя' => 'ДоговорКонтрагента',
                        'Нпп' =>  $i,
                        'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                    ],
                    'Ссылка' => [
                        '@attributes' => [
                            'Нпп' =>  $i
                        ],
                        'Свойство' => [
                            $this->setAttributes('Наименование', 'Строка', $item->title),
                            $this->setAttributes('ЭтоГруппа', 'Булево', 'false'), // false???
                            $this->setAttributes('ВидДоговора', 'ПеречислениеСсылка.ВидыДоговоровКонтрагентов', 'СПокупателем'),
                        ]
                    ]
                ];

                $i++;

            }
        }

        $this->i = $i;

        return $result;

    }


    // Контрагенты Нпп="6"
    public function getСontractors(string $contractor_id): array
    {
        $item = DB::table('contractors as c')
            ->select('c.id', 'c.title', 'c.tel', 'c.fax', 'c.email', 'cd.inn', 'cd.kpp', 'cd.ogrn', 'cd.full_name', 'cd.type', 'c.deleted_at')
            ->leftjoin('contractors_details as cd', 'c.id', '=', 'cd.contractor_id')
            ->groupBy(['c.id', 'cd.inn', 'cd.kpp', 'cd.ogrn', 'cd.full_name', 'cd.type', 'c.deleted_at'])
            ->where('c.id', $contractor_id)->first();

        // dd($data);

        $result = [];
        $temp = [];
        $i = $this->i;

        // foreach ($data as $k => $item) {
        $contractor = [
            'id' => $item->id,
            '@attributes' => [
                'ИмяПравила' => 'Контрагенты',
                'Нпп' => $i,
                'Тип' => 'СправочникСсылка.Контрагенты'
            ],
            'Ссылка' => [
                '@attributes' => [
                    'Нпп' => $i
                ],
                'Свойство' => $this->getContractorProperties($item)
            ],
            'Свойство' => [
                $this->setAttributes('ЮрФизЛицо', 'ПеречислениеСсылка.ЮрФизЛицо', $item->type),
                $this->setAttributes('ПометкаУдаления', 'Булево', 'false'),
            ]
        ];

        $result[] = $contractor;
        $tempcontractor = $contractor;
        $tempcontractor['@attributes']['Имя'] = 'Контрагенты';
        unset($tempcontractor['@attributes']['ИмяПравила']);
        $this->temp[] = $tempcontractor;

        // dd($tempcontractor);

        $i++;

        $contactInfoTypes = [
            ['Тип' => 'Телефон', 'Вид' => 'Телефон', 'Поле' => 'Поле3', 'Значение' => $item->tel],
            ['Тип' => 'Телефон', 'Вид' => 'Факс', 'Поле' => 'Поле3', 'Значение' => $item->fax],
            ['Тип' => 'АдресЭлектроннойПочты', 'Вид' => 'Email', 'Поле' => 'Поле1', 'Значение' => $item->email]
        ];

        foreach ($contactInfoTypes as $contactInfoType) {
            $contactInfo = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'КонтактнаяИнформация',
                    'Нпп' => $i,
                    'Тип' => 'РегистрСведенийЗапись.КонтактнаяИнформация'
                ],
                'Свойство' => [
                    $this->setAttributes('Тип', 'ПеречислениеСсылка.ТипыКонтактнойИнформации', $contactInfoType['Тип']),
                    $this->setAttributes('Вид', 'Строка', $contactInfoType['Вид']),
                    $this->setAttributes($contactInfoType['Поле'], 'Строка', $contactInfoType['Значение']),
                    $this->setAttributes('Представление', 'Строка', $contactInfoType['Значение']),
                ]
            ];

            $result[] = $contactInfo;
            $i++;
        }

        // }

        $this->i = $i;

        // dd($result);
        return $result;
    }

    private function getContractorProperties($item): array
    {
        return [
            $this->setAttributes('Наименование', 'Строка', $item->title),
            $this->setAttributes('ИНН', 'Строка', $item->inn),
            $this->setAttributes('КПП', 'Строка', $item->kpp),
            $this->setAttributes('КодПоОКПО', 'Строка', $item->ogrn),
            $this->setAttributes('НаименованиеПолное', 'Строка', $item->full_name),
            $this->setAttributes('ЭтоГруппа', 'Булево', 'false'),
        ];
    }


    // КлассификаторЕдиницИзмерения Нпп="14"
    public function getMeasurementProducts(string $cabinetId): array
    {

        $shipments = DB::table('shipments as s')
        ->select('s.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('shipment_items as shi', 's.id', '=', 'shi.shipment_id')
        ->leftjoin('products as p', 'p.id', '=', 'shi.product_id')
        ->leftjoin('measurement_units as mu', 'mu.id', '=', 'p.measurement_unit_id')
        ->groupBy('s.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') //, 'vr.rate', 'vr.description'
        ->where('s.cabinet_id', $cabinetId)
        ->distinct('measurement_unit_id')->get();

        $acceptances = DB::table('acceptances as ac')
        ->select('ac.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('acceptance_items as aci', 'ac.id', '=', 'aci.acceptance_id')
        ->leftjoin('products as p', 'p.id', '=', 'aci.product_id')
        ->leftjoin('measurement_units as mu', 'mu.id', '=', 'p.measurement_unit_id')
        ->groupBy('ac.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') //, 'vr.rate', 'vr.description'
        ->where('ac.cabinet_id', $cabinetId)
        ->distinct('measurement_unit_id')->get();

        $mergeArray = [];
        $uniqueArray = [];
        $uniqueIds = [];

        foreach (array_merge($shipments->toArray(), $acceptances->toArray()) as $object) {
            $mergeArray[] = (array) $object;
        }

        foreach ($mergeArray as $item) {
            if (!in_array($item['measurement_unit_id'], $uniqueIds)) {
                $uniqueArray[] = $item;
                $uniqueIds[] = $item['measurement_unit_id'];
            }
        }

        $result = [];

        $i = $this->i;

        foreach ($uniqueArray as $item) {

            $result[] = [
                '@attributes' => [
                    'ИмяПравила' => 'КлассификаторЕдиницИзмерения',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.КлассификаторЕдиницИзмерения'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('{УникальныйИдентификатор}', 'Строка', $item['measurement_unit_id']),
                        $this->setAttributes('Код', 'Строка', $item['code']),
                    ]
                ],
                'Свойство' => [
                    $this->setAttributes('ПометкаУдаления', 'Булево', 'false Вставить Softdelete'),
                    $this->setAttributes('Наименование', 'Строка', $item['short_name']),
                    $this->setAttributes('НаименованиеПолное', 'Строка', $item['name']),
                ]
            ];

            $this->temp[] = [
                'id' => $item['measurement_unit_id'],
                '@attributes' => [
                    'Имя' => 'БазоваяЕдиницаИзмерения',
                    'Тип' => 'СправочникСсылка.КлассификаторЕдиницИзмерения'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        $this->setAttributes('{УникальныйИдентификатор}', 'Строка', $item['measurement_unit_id']),
                        $this->setAttributes('Код', 'Строка', $item['code']),
                    ]
                ]
            ];

            $i++;

        }

        $this->i = $i;
        // dd($result);
        return $result;
    }
}
