<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use App\Contracts\Repositories\OzonOrdersRepositoryContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class OzonOrderNumberChunksHandler
{
    use HasOrderedUuid;

    public function __construct(
        private OzonOrdersRepositoryContract $ozonOrdersRepositoryContract,
    ) {
    }

    public function run(Collection $soldAmount, string $cabinetId, string $departmentId, string $employeeId): void
    {
        $orderNumberChunks = $soldAmount->chunk(1000);

        $orderNumberChunks->each(fn ($chunk) => $this->processChunk($chunk, $cabinetId, $departmentId, $employeeId));
    }

    private function processChunk(Collection $chunk, string $cabinetId, string $departmentId, string $employeeId): void
    {
        $orders = $chunk->map(fn ($item) => $this->formatOrderData($item, $cabinetId, $departmentId, $employeeId))->toArray();
        $this->saveOrders($orders);
    }

    private function formatOrderData(array $item, string $cabinetId, string $departmentId, string $employeeId): array
    {
        return [
            'id' => $item['id'] ?? $this->generateUuid(),
            'cabinet_id' => $cabinetId,
            'department_id' => $departmentId,
            'employee_id' => $employeeId,
            'order_number' => $item['order_number'],
            'inner_order_number' => $this->generateInnerOrderNumber($item),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    private function generateInnerOrderNumber(array $item): string
    {
        return $item['order_number'] . '--' . $item['posting_number'] . '___' . $item['sku'];
    }

    private function saveOrders(array $orders): void
    {
        $this->ozonOrdersRepositoryContract->upsert($orders);
    }

}
