<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use App\Contracts\Repositories\OzonOrderItemsRepositoryContract;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class OzonOrderItemsChunksHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        private OzonOrderItemsRepositoryContract $ozonOrderItemsRepositoryContract,
    ) {
    }

    public function run(Collection $soldAmount, Collection $orderIds): void
    {
        // Второй этап: Заполняем таблицу `ozon_order_items` с привязкой к `ozon_order_id`
        $soldAmount->chunk(1000)->each(function ($chunk) use ($orderIds) {
            $orderItems = $chunk->map(function ($item) use ($orderIds) {

                $inner_posting_number           = $item['order_number'].'--'.$item['posting_number'].'___'.$item['sku'];

                $item['id']                     = $this->generateUuid();
                $item['ozon_order_id']          = $orderIds[$inner_posting_number];
                $item['inner_posting_number']   = $inner_posting_number;
                $item['amount']                 = $this->rublesInKopeck((int)$item['amount']);
                $item['products_price']         = $this->rublesInKopeck((int)$item['products_price']);
                $item['cost_bayer']             = $this->rublesInKopeck((int)$item['cost_bayer']);
                $item['quantity']               = (int)$item['quantity'];
                $item['in_process_at']          = Carbon::parse($item['in_process_at'])->format('Y-m-d H:i:s');
                $item['shipment_date']          = Carbon::parse($item['shipment_date'])->format('Y-m-d H:i:s');
                $item['delivering_date']        = Carbon::parse($item['delivering_date'])->format('Y-m-d H:i:s');
                $item['delivery_date_end']      = Carbon::parse($item['delivery_date_end'])->format('Y-m-d H:i:s');
                $item['delivery_price']         = $this->rublesInKopeck((int)$item['delivery_price']);
                $item['related_postings']       = json_encode($item['related_postings'], JSON_UNESCAPED_UNICODE);
                $item['old_price']              = $this->rublesInKopeck((int)$item['old_price']);
                $item['total_discount_percent'] = (float)rtrim($item['total_discount_percent'], '%');
                $item['total_discount_value']   = $this->rublesInKopeck((int)$item['total_discount_value']);
                $item['actions']                = json_encode($item['actions'], JSON_UNESCAPED_UNICODE);
                $item['created_at']             = now();
                $item['updated_at']             = now();

                unset($item['order_number']);

                return $item;

            })->toArray();

            $this->ozonOrderItemsRepositoryContract->upsert($orderItems);
        });

    }

}
