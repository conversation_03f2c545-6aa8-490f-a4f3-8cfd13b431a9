<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use App\Services\Api\Internal\Other\ExcelImportService\DTO\ExcelImportDTO;
use App\Traits\HasOrderedUuid;
use Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ExcelImportHandler
{
    use HasOrderedUuid;

    private string $cabinetId;
    private string $departmentId;
    private string $employeeId;

    public function __construct(
        protected OzonOrderHandler $ozonOrderHandler,
        protected ProductsAndPricesHandler $productsAndPricesHandler,
        protected EconomyProductsHandler $economyProductsHandler,
        protected ImplementationReportHandler $implementationReportHandler,
        protected DetailedReportHandler $detailedReportHandler,
        protected CompensationReportHandler $compensationReportHandler,
    ) {
    }

    public function run(ExcelImportDTO $dto): void
    {
        $file = $dto->file;

        $this->cabinetId = $dto->cabinetId;
        $this->departmentId = $dto->departmentId;
        $this->employeeId = $dto->employeeId;
        $filePath = $file->getRealPath();

        $extension = strtolower($file->getClientOriginalExtension());


        switch ($extension) {
            case 'xlsx':

                $spreadsheet = IOFactory::load($filePath);


                $sheetNames = [
                    'Товары и цены',
                    // 'Эконом товары',
                    'Отчет о реализации',
                    'Детальный отчёт',
                    'Отчет о компенсациях',
                ];

                foreach ($spreadsheet->getSheetNames() as $sheetName) {
                    if (in_array($sheetName, $sheetNames)) {
                        switch ($sheetName) {
                            case 'Товары и цены':
                                $this->productsAndPricesHandler->run($spreadsheet->getSheetByName($sheetName), $this->cabinetId, $this->departmentId, $this->employeeId);
                                break;
                            case 'Эконом товары':
                                $this->economyProductsHandler->run($spreadsheet->getSheetByName($sheetName));
                                break;
                            case 'Отчет о реализации':
                                $this->implementationReportHandler->run($spreadsheet->getSheetByName($sheetName));
                                break;
                            case 'Детальный отчёт':
                                $this->detailedReportHandler->run($spreadsheet->getSheetByName($sheetName), $this->cabinetId, $this->departmentId, $this->employeeId);
                                break;
                            case 'Отчет о компенсациях':
                                $this->compensationReportHandler->run($spreadsheet->getSheetByName($sheetName));
                                break;
                        }

                    }
                }
                break;

            case 'csv':
                $this->ozonOrderHandler->run($file, $this->cabinetId, $this->departmentId, $this->employeeId);
                break;

            default:
                throw new Exception("Unsupported file format: $extension");

        }

    }

}
