<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ChoosingParsingProcessHandler
{
    public function __construct(
        protected ProductsAndPricesHandler $productsAndPricesHandler,
        protected EconomyProductsHandler $economyProductsHandler,
        protected ImplementationReportHandler $implementationReportHandler,
        protected DetailedReportHandler $detailedReportHandler,
        protected CompensationReportHandler $compensationReportHandler,
    ) {
    }


    public function run(string $sheetName, Worksheet $sheet, string $cabinetId, string $departmentId, string $employeeId): void
    {
        switch ($sheetName) {
            case 'Товары и цены':
                $this->productsAndPricesHandler->run($sheet, $cabinetId, $departmentId, $employeeId);
                break;
            case 'Эконом товары':
                $this->economyProductsHandler->run($sheet);
                break;
            case 'Отчет о реализации':
                $this->implementationReportHandler->run($sheet);
                break;
            case 'Детальный отчёт':
                $this->detailedReportHandler->run($sheet, $cabinetId, $departmentId, $employeeId);
                break;
            case 'Отчет о компенсациях':
                $this->compensationReportHandler->run($sheet);
                break;
        }
    }

}
