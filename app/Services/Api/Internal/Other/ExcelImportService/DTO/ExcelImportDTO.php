<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\DTO;

use App\Contracts\DtoContract;
use Illuminate\Http\UploadedFile;

class ExcelImportDTO implements DtoContract
{
    public function __construct(
        public string $cabinetId,
        public string $departmentId,
        public string $employeeId,
        public UploadedFile $file,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'],
            departmentId: $data['department_id'],
            employeeId: $data['employee_id'],
            file: $data['file'],
        );
    }
}
