<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class ReservationValidationHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function validateReservation(string $warehouseId, array $items): array
    {
        $errors = [];
        $warnings = [];

        // Проверяем активна ли ордерная схема
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, now()->toDateString());

        if (!$isOrderSchemeActive) {
            // Для обычной схемы простая проверка остатков
            return $this->validateBasicReservation($warehouseId, $items);
        }

        // Валидация для ордерной схемы
        foreach ($items as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];

            // Получаем доступное количество (не зарезервированное)
            $availableQuantity = DB::selectOne("
                SELECT COALESCE(SUM(wi.available_quantity), 0) as available
                FROM warehouse_items wi
                WHERE wi.warehouse_id = ?
                  AND wi.product_id = ?
                  AND wi.status != 'out_of_stock'
                  AND wi.quality_status = 'good'
            ", [$warehouseId, $productId])->available ?? 0;

            if ($availableQuantity < $quantity) {
                $errors[] = [
                    'product_id' => $productId,
                    'type' => 'insufficient_available_stock',
                    'message' => "Недостаточно доступного товара для резервирования. Требуется: {$quantity}, доступно: {$availableQuantity}",
                    'required' => $quantity,
                    'available' => $availableQuantity
                ];
                continue;
            }

            // Проверяем приоритеты существующих резервов
            $higherPriorityReserved = DB::table('warehouse_reservations as wr')
                ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
                ->where('wi.warehouse_id', $warehouseId)
                ->where('wi.product_id', $productId)
                ->where('wr.status', 'reserved')
                ->where('wr.priority', '<', $item['priority'] ?? 5)
                ->sum('wr.reserved_quantity');

            if ($higherPriorityReserved > 0) {
                $warnings[] = [
                    'product_id' => $productId,
                    'type' => 'higher_priority_exists',
                    'message' => "Существуют резервы с более высоким приоритетом: {$higherPriorityReserved} шт.",
                    'quantity' => $higherPriorityReserved
                ];
            }

            // Проверяем сроки годности доступных товаров
            $nearExpiryQuantity = DB::table('warehouse_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->where('status', '!=', 'out_of_stock')
                ->where('quality_status', 'good')
                ->where('expiry_date', '<', now()->addDays(30))
                ->whereNotNull('expiry_date')
                ->sum('available_quantity');

            if ($nearExpiryQuantity > 0 && $nearExpiryQuantity >= $quantity) {
                $warnings[] = [
                    'product_id' => $productId,
                    'type' => 'near_expiry_stock',
                    'message' => "Резервируемый товар истекает в течение 30 дней: {$nearExpiryQuantity} шт.",
                    'quantity' => $nearExpiryQuantity
                ];
            }

            // Проверяем максимальный срок резервирования
            $maxReserveDays = 90; // Максимальный срок резерва
            $requestedDays = $item['reserve_days'] ?? 30;

            if ($requestedDays > $maxReserveDays) {
                $errors[] = [
                    'product_id' => $productId,
                    'type' => 'reserve_period_too_long',
                    'message' => "Срок резервирования превышает максимальный: {$requestedDays} дней (макс. {$maxReserveDays})",
                    'requested_days' => $requestedDays,
                    'max_days' => $maxReserveDays
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    private function validateBasicReservation(string $warehouseId, array $items): array
    {
        $errors = [];
        $warnings = [];

        foreach ($items as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];

            // Простая проверка общих остатков
            $totalQuantity = DB::table('warehouse_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->where('status', '!=', 'out_of_stock')
                ->sum('quantity');

            if ($totalQuantity < $quantity) {
                $errors[] = [
                    'product_id' => $productId,
                    'type' => 'insufficient_stock',
                    'message' => "Недостаточно товара на складе. Требуется: {$quantity}, доступно: {$totalQuantity}",
                    'required' => $quantity,
                    'available' => $totalQuantity
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }
}
