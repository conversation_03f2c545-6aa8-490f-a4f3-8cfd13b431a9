<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class StockValidationHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function validateAvailability(string $warehouseId, string $productId, int $quantity, string $date): array
    {
        $errors = [];
        $warnings = [];

        // Проверяем активна ли ордерная схема
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, $date);

        if ($isOrderSchemeActive) {
            // Детальная проверка для ордерной схемы
            $stockInfo = $this->getDetailedStockInfo($warehouseId, $productId, $date);
            
            if ($stockInfo['available'] < $quantity) {
                $errors[] = [
                    'type' => 'insufficient_available_stock',
                    'message' => "Недостаточно доступного товара",
                    'required' => $quantity,
                    'available' => $stockInfo['available'],
                    'total' => $stockInfo['total'],
                    'reserved' => $stockInfo['reserved']
                ];
            }

            // Предупреждения о качестве товаров
            if ($stockInfo['defective'] > 0) {
                $warnings[] = [
                    'type' => 'defective_stock_exists',
                    'message' => "На складе есть бракованный товар: {$stockInfo['defective']} шт.",
                    'quantity' => $stockInfo['defective']
                ];
            }

            if ($stockInfo['quarantine'] > 0) {
                $warnings[] = [
                    'type' => 'quarantine_stock_exists',
                    'message' => "На складе есть товар на карантине: {$stockInfo['quarantine']} шт.",
                    'quantity' => $stockInfo['quarantine']
                ];
            }
        } else {
            // Простая проверка для обычной схемы
            $totalQuantity = DB::table('warehouse_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->where('received_at', '<=', $date)
                ->where('status', '!=', 'out_of_stock')
                ->sum('quantity');

            if ($totalQuantity < $quantity) {
                $errors[] = [
                    'type' => 'insufficient_stock',
                    'message' => "Недостаточно товара на складе",
                    'required' => $quantity,
                    'available' => $totalQuantity
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    public function validateExpiryDates(string $warehouseId, array $items): array
    {
        $errors = [];
        $warnings = [];

        foreach ($items as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];

            // Проверяем товары с истекшим сроком годности
            $expiredQuantity = DB::table('warehouse_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->where('expiry_date', '<', now())
                ->whereNotNull('expiry_date')
                ->where('status', '!=', 'out_of_stock')
                ->sum('quantity');

            if ($expiredQuantity > 0) {
                $errors[] = [
                    'product_id' => $productId,
                    'type' => 'expired_stock',
                    'message' => "На складе есть товар с истекшим сроком годности: {$expiredQuantity} шт.",
                    'quantity' => $expiredQuantity
                ];
            }

            // Предупреждения о товарах, которые скоро истекут
            $nearExpiryQuantity = DB::table('warehouse_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->whereBetween('expiry_date', [now(), now()->addDays(7)])
                ->where('status', '!=', 'out_of_stock')
                ->sum('quantity');

            if ($nearExpiryQuantity > 0) {
                $warnings[] = [
                    'product_id' => $productId,
                    'type' => 'near_expiry_stock',
                    'message' => "Товар истекает в течение 7 дней: {$nearExpiryQuantity} шт.",
                    'quantity' => $nearExpiryQuantity
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    private function getDetailedStockInfo(string $warehouseId, string $productId, string $date): array
    {
        $result = DB::selectOne("
            SELECT 
                COALESCE(SUM(wi.quantity), 0) as total,
                COALESCE(SUM(wi.available_quantity), 0) as available,
                COALESCE(SUM(wi.reserved_quantity), 0) as reserved,
                COALESCE(SUM(CASE WHEN wi.quality_status = 'defective' THEN wi.quantity ELSE 0 END), 0) as defective,
                COALESCE(SUM(CASE WHEN wi.quality_status = 'quarantine' THEN wi.quantity ELSE 0 END), 0) as quarantine,
                COALESCE(SUM(CASE WHEN wi.quality_status = 'expired' THEN wi.quantity ELSE 0 END), 0) as expired
            FROM warehouse_items wi
            WHERE wi.warehouse_id = ?
              AND wi.product_id = ?
              AND wi.received_at <= ?
              AND wi.status != 'out_of_stock'
        ", [$warehouseId, $productId, $date]);

        return [
            'total' => $result->total ?? 0,
            'available' => $result->available ?? 0,
            'reserved' => $result->reserved ?? 0,
            'defective' => $result->defective ?? 0,
            'quarantine' => $result->quarantine ?? 0,
            'expired' => $result->expired ?? 0
        ];
    }
}
