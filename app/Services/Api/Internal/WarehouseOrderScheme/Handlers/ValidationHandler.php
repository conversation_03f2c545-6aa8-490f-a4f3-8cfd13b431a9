<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class ValidationHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function validateShipment(string $warehouseId, array $items): array
    {
        $errors = [];
        $warnings = [];

        // Проверяем активна ли ордерная схема для отгрузок
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, now()->toDateString());

        if (!$isOrderSchemeActive) {
            // Для обычной схемы применяем базовую валидацию
            return $this->validateBasicShipment($warehouseId, $items);
        }

        // Валидация для ордерной схемы
        foreach ($items as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];

            // Проверяем наличие резервов для товара
            $reservedQuantity = DB::table('warehouse_reservations as wr')
                ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
                ->where('wi.warehouse_id', $warehouseId)
                ->where('wi.product_id', $productId)
                ->where('wr.status', 'reserved')
                ->sum('wr.reserved_quantity');

            if ($reservedQuantity < $quantity) {
                $errors[] = [
                    'product_id' => $productId,
                    'type' => 'insufficient_reservation',
                    'message' => "Недостаточно зарезервированного товара. Требуется: {$quantity}, зарезервировано: {$reservedQuantity}",
                    'required' => $quantity,
                    'available' => $reservedQuantity
                ];
            }

            // Проверяем сроки годности зарезервированных товаров
            $expiredReservations = DB::table('warehouse_reservations as wr')
                ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
                ->where('wi.warehouse_id', $warehouseId)
                ->where('wi.product_id', $productId)
                ->where('wr.status', 'reserved')
                ->where('wi.expiry_date', '<', now()->addDays(7))
                ->whereNotNull('wi.expiry_date')
                ->sum('wr.reserved_quantity');

            if ($expiredReservations > 0) {
                $warnings[] = [
                    'product_id' => $productId,
                    'type' => 'expiry_warning',
                    'message' => "Часть зарезервированного товара истекает в течение 7 дней: {$expiredReservations} шт.",
                    'quantity' => $expiredReservations
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    public function validateTransfer(string $sourceWarehouseId, string $targetWarehouseId, array $items): array
    {
        $errors = [];
        $warnings = [];

        // Проверяем ордерную схему для склада-отправителя
        $isSourceOrderScheme = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($sourceWarehouseId, now()->toDateString());

        if ($isSourceOrderScheme) {
            // Для ордерной схемы проверяем что товары не зарезервированы под заказы
            foreach ($items as $item) {
                $productId = $item['product_id'];
                $quantity = $item['quantity'];

                $reservedForOrders = DB::table('warehouse_reservations as wr')
                    ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
                    ->where('wi.warehouse_id', $sourceWarehouseId)
                    ->where('wi.product_id', $productId)
                    ->where('wr.status', 'reserved')
                    ->where('wr.reservation_type', 'order')
                    ->sum('wr.reserved_quantity');

                if ($reservedForOrders > 0) {
                    $errors[] = [
                        'product_id' => $productId,
                        'type' => 'reserved_for_orders',
                        'message' => "Товар зарезервирован под заказы клиентов: {$reservedForOrders} шт.",
                        'reserved_quantity' => $reservedForOrders
                    ];
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    private function validateBasicShipment(string $warehouseId, array $items): array
    {
        $errors = [];
        $warnings = [];

        foreach ($items as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];

            // Базовая проверка наличия товаров
            $availableQuantity = DB::table('warehouse_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->where('status', '!=', 'out_of_stock')
                ->sum('quantity');

            if ($availableQuantity < $quantity) {
                $errors[] = [
                    'product_id' => $productId,
                    'type' => 'insufficient_stock',
                    'message' => "Недостаточно товара на складе. Требуется: {$quantity}, доступно: {$availableQuantity}",
                    'required' => $quantity,
                    'available' => $availableQuantity
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }
}
