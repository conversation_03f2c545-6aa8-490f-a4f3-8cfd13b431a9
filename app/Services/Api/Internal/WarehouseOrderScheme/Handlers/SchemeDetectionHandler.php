<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SchemeDetectionHandler
{
    public function isActiveForReceipts(string $warehouseId, string $date): bool
    {
        $settings = $this->getSettings($warehouseId);
        
        if (!$settings || !$settings->on_coming_from) {
            return false;
        }

        return Carbon::parse($date)->greaterThanOrEqualTo(Carbon::parse($settings->on_coming_from));
    }

    public function isActiveForShipments(string $warehouseId, string $date): bool
    {
        $settings = $this->getSettings($warehouseId);
        
        if (!$settings || !$settings->on_shipment_from) {
            return false;
        }

        return Carbon::parse($date)->greaterThanOrEqualTo(Carbon::parse($settings->on_shipment_from));
    }

    public function getSchemeMode(string $warehouseId): array
    {
        $settings = $this->getSettings($warehouseId);
        
        if (!$settings) {
            return [
                'receipts' => false,
                'shipments' => false,
                'full' => false
            ];
        }

        $today = Carbon::now()->toDateString();
        $receiptsActive = $settings->on_coming_from && Carbon::parse($today)->greaterThanOrEqualTo(Carbon::parse($settings->on_coming_from));
        $shipmentsActive = $settings->on_shipment_from && Carbon::parse($today)->greaterThanOrEqualTo(Carbon::parse($settings->on_shipment_from));

        return [
            'receipts' => $receiptsActive,
            'shipments' => $shipmentsActive,
            'full' => $receiptsActive && $shipmentsActive
        ];
    }

    public function getSettings(string $warehouseId): ?object
    {
        return DB::table('warehouse_order_schemes')
            ->where('warehouse_id', $warehouseId)
            ->first();
    }

    public function isOperationalBalanceControlEnabled(string $warehouseId): bool
    {
        $settings = $this->getSettings($warehouseId);
        
        return $settings ? (bool) $settings->control_operational_balances : false;
    }
}
