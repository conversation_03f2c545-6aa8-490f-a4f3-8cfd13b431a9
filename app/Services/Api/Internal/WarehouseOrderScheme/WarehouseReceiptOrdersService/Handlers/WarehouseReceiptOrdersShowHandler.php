<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;

readonly class WarehouseReceiptOrdersShowHandler
{
    public function __construct(
        private WarehouseReceiptOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id): ?object
    {
        return $this->repository->show($id);
    }
}
