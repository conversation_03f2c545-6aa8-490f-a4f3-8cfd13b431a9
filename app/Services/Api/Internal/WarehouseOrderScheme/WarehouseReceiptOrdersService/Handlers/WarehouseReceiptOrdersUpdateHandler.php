<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use Exception;

readonly class WarehouseReceiptOrdersUpdateHandler
{
    public function __construct(
        private WarehouseReceiptOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id, object $dto): bool
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            throw new Exception('Приходный ордер не найден');
        }
        
        if ($order->held) {
            throw new Exception('Нельзя изменять проведенный приходный ордер');
        }
        
        $data = [];
        
        if (isset($dto->number)) {
            $data['number'] = $dto->number;
        }
        
        if (isset($dto->date_from)) {
            $data['date_from'] = $dto->date_from;
        }
        
        if (isset($dto->status_id)) {
            $data['status_id'] = $dto->status_id;
        }
        
        if (isset($dto->reason)) {
            $data['reason'] = $dto->reason;
        }
        
        if (isset($dto->comment)) {
            $data['comment'] = $dto->comment;
        }
        
        if (empty($data)) {
            return true;
        }
        
        return $this->repository->update($id, $data) > 0;
    }
}
