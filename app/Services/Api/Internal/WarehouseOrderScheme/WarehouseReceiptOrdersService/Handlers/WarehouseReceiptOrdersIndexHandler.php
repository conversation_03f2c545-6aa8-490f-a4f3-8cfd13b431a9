<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\DTO\IndexRequestDTO;

readonly class WarehouseReceiptOrdersIndexHandler
{
    public function __construct(
        private WarehouseReceiptOrdersRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): array
    {
        $warehouseId = $dto->filters['warehouse_id'] ?? '';
        $orders = $this->repository->getByWarehouse($warehouseId, $dto->filters);
        
        return [
            'data' => $orders,
            'meta' => [
                'total' => $orders->count(),
                'filters' => $dto->filters
            ]
        ];
    }
}
