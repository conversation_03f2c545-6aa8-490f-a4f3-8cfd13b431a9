<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use Exception;

readonly class WarehouseReceiptOrdersHoldHandler
{
    public function __construct(
        private WarehouseReceiptOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id): bool
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            throw new Exception('Приходный ордер не найден');
        }
        
        if ($order->held) {
            throw new Exception('Приходный ордер уже проведен');
        }
        
        return $this->repository->markAsHeld($id) > 0;
    }
}
