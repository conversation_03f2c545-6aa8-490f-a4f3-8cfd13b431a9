<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Traits\HasOrderedUuid;
use Exception;

readonly class WarehouseReceiptOrdersCreateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private WarehouseReceiptOrdersRepositoryContract $repository,
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeService
    ) {
    }

    public function run(object $dto): string
    {
        // Проверяем что склад работает в ордерной схеме
        $isOrderSchemeActive = $this->orderSchemeService
            ->isOrderSchemeActiveForReceipts($dto->warehouse_id, $dto->date_from);
            
        if (!$isOrderSchemeActive) {
            throw new Exception('Склад не работает в ордерной схеме для приемок');
        }

        $id = $this->generateUuid();
        
        $data = [
            'id' => $id,
            'cabinet_id' => $dto->cabinet_id,
            'employee_id' => $dto->employee_id,
            'department_id' => $dto->department_id,
            'warehouse_id' => $dto->warehouse_id,
            'number' => $dto->number ?? null,
            'date_from' => $dto->date_from,
            'status_id' => $dto->status_id ?? null,
            'document_basis_type' => $dto->document_basis_type ?? null,
            'document_basis_id' => $dto->document_basis_id ?? null,
            'reason' => $dto->reason ?? null,
            'total_quantity' => $dto->total_quantity,
            'total_cost' => $dto->total_cost,
            'comment' => $dto->comment ?? null,
        ];
        
        $this->repository->insert($data);
        
        return $id;
    }
}
