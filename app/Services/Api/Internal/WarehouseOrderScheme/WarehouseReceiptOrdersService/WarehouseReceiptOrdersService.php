<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService;

use App\Contracts\Services\Internal\WarehouseReceiptOrdersServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers\WarehouseReceiptOrdersIndexHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers\WarehouseReceiptOrdersShowHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers\WarehouseReceiptOrdersCreateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers\WarehouseReceiptOrdersUpdateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers\WarehouseReceiptOrdersDeleteHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrdersService\Handlers\WarehouseReceiptOrdersHoldHandler;
use App\DTO\IndexRequestDTO;

readonly class WarehouseReceiptOrdersService implements WarehouseReceiptOrdersServiceContract
{
    public function __construct(
        private WarehouseReceiptOrdersIndexHandler $indexHandler,
        private WarehouseReceiptOrdersShowHandler $showHandler,
        private WarehouseReceiptOrdersCreateHandler $createHandler,
        private WarehouseReceiptOrdersUpdateHandler $updateHandler,
        private WarehouseReceiptOrdersDeleteHandler $deleteHandler,
        private WarehouseReceiptOrdersHoldHandler $holdHandler
    ) {
    }

    public function index(IndexRequestDTO $dto): array
    {
        return $this->indexHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(object $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(string $id, object $dto): bool
    {
        return $this->updateHandler->run($id, $dto);
    }

    public function delete(string $id): bool
    {
        return $this->deleteHandler->run($id);
    }

    public function hold(string $id): bool
    {
        return $this->holdHandler->run($id);
    }
}
