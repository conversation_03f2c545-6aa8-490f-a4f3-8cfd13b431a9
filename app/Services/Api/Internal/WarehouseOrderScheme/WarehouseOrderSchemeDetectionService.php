<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeDetectionHandler;

readonly class WarehouseOrderSchemeDetectionService implements WarehouseOrderSchemeDetectionServiceContract
{
    public function __construct(
        private SchemeDetectionHandler $detectionHandler
    ) {
    }

    public function isOrderSchemeActiveForReceipts(string $warehouseId, string $date): bool
    {
        return $this->detectionHandler->isActiveForReceipts($warehouseId, $date);
    }

    public function isOrderSchemeActiveForShipments(string $warehouseId, string $date): bool
    {
        return $this->detectionHandler->isActiveForShipments($warehouseId, $date);
    }

    public function getOrderSchemeMode(string $warehouseId): array
    {
        return $this->detectionHandler->getSchemeMode($warehouseId);
    }

    public function getOrderSchemeSettings(string $warehouseId): ?object
    {
        return $this->detectionHandler->getSettings($warehouseId);
    }

    public function isOperationalBalanceControlEnabled(string $warehouseId): bool
    {
        return $this->detectionHandler->isOperationalBalanceControlEnabled($warehouseId);
    }
}
