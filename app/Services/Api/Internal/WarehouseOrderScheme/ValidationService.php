<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeValidationServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\ValidationHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\ReservationValidationHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\StockValidationHandler;

readonly class ValidationService implements WarehouseOrderSchemeValidationServiceContract
{
    public function __construct(
        private ValidationHandler $validationHandler,
        private ReservationValidationHandler $reservationValidationHandler,
        private StockValidationHandler $stockValidationHandler
    ) {
    }

    public function validateShipmentOperation(string $warehouseId, array $items): array
    {
        return $this->validationHandler->validateShipment($warehouseId, $items);
    }

    public function validateReservationOperation(string $warehouseId, array $items): array
    {
        return $this->reservationValidationHandler->validateReservation($warehouseId, $items);
    }

    public function validateStockAvailability(string $warehouseId, string $productId, int $quantity, string $date): array
    {
        return $this->stockValidationHandler->validateAvailability($warehouseId, $productId, $quantity, $date);
    }

    public function validateTransferOperation(string $sourceWarehouseId, string $targetWarehouseId, array $items): array
    {
        return $this->validationHandler->validateTransfer($sourceWarehouseId, $targetWarehouseId, $items);
    }

    public function validateExpiryDates(string $warehouseId, array $items): array
    {
        return $this->stockValidationHandler->validateExpiryDates($warehouseId, $items);
    }
}
