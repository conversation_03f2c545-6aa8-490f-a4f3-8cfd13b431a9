<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use Exception;

readonly class WarehouseReservationsCancelHandler
{
    public function __construct(
        private WarehouseReservationsRepositoryContract $repository
    ) {
    }

    public function run(string $id): bool
    {
        $reservation = $this->repository->getReservationsByOrderItem($id)->first();
        
        if (!$reservation) {
            throw new Exception('Резерв не найден');
        }
        
        if ($reservation->status !== 'reserved') {
            throw new Exception('Можно отменить только активный резерв');
        }
        
        // Отменяем резерв
        return $this->repository->update($id, [
            'status' => 'cancelled'
        ]) > 0;
    }
}
