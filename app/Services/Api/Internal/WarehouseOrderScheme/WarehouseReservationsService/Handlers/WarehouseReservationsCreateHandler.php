<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeValidationServiceContract;
use App\Traits\HasOrderedUuid;
use Exception;

readonly class WarehouseReservationsCreateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private WarehouseReservationsRepositoryContract $repository,
        private WarehouseOrderSchemeValidationServiceContract $validationService
    ) {
    }

    public function run(object $dto): string
    {
        // Валидируем возможность резервирования
        $validation = $this->validationService->validateReservationOperation(
            $dto->warehouseId,
            [(array) $dto]
        );
        
        if (!$validation['valid']) {
            throw new Exception('Ошибка валидации резервирования: ' . implode(', ', array_column($validation['errors'], 'message')));
        }
        
        $id = $this->generateUuid();
        
        $data = [
            'id' => $id,
            'warehouse_item_id' => $dto->warehouseItemId,
            'customer_order_item_id' => $dto->customerOrderItemId,
            'reserved_quantity' => $dto->reservedQuantity,
            'reserved_at' => now(),
            'status' => 'reserved',
            'reservation_type' => $dto->reservationType,
            'document_type' => $dto->documentType ?? null,
            'document_id' => $dto->documentId ?? null,
            'priority' => $dto->priority ?? 5,
            'expires_at' => $dto->expiresAt ?? null,
            'auto_release' => $dto->autoRelease ?? true,
        ];
        
        $this->repository->insert($data);
        
        return $id;
    }
}
