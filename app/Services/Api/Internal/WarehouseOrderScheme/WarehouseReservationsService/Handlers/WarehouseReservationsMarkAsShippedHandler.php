<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;

readonly class WarehouseReservationsMarkAsShippedHandler
{
    public function __construct(
        private WarehouseReservationsRepositoryContract $repository
    ) {
    }

    public function run(array $reservationIds): int
    {
        if (empty($reservationIds)) {
            return 0;
        }
        
        return $this->repository->markAsShipped($reservationIds);
    }
}
