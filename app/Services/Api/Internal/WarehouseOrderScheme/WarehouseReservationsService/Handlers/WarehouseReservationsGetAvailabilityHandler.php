<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseReservationsService\Handlers;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;

readonly class WarehouseReservationsGetAvailabilityHandler
{
    public function __construct(
        private WarehouseReservationsRepositoryContract $repository
    ) {
    }

    public function run(string $productId, string $warehouseId, string $date): array
    {
        $available = $this->repository->getAvailableQuantityForProduct($productId, $warehouseId, $date);
        $reserved = $this->repository->getTotalReservedQuantity($warehouseId . '_' . $productId);
        
        return [
            'available' => $available,
            'reserved' => $reserved,
            'total' => $available + $reserved
        ];
    }
}
