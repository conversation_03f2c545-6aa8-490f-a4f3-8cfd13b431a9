<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService;

use App\Contracts\Services\Internal\WarehouseIssueOrdersServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers\WarehouseIssueOrdersIndexHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers\WarehouseIssueOrdersShowHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers\WarehouseIssueOrdersCreateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers\WarehouseIssueOrdersUpdateHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers\WarehouseIssueOrdersDeleteHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers\WarehouseIssueOrdersHoldHandler;
use App\DTO\IndexRequestDTO;

readonly class WarehouseIssueOrdersService implements WarehouseIssueOrdersServiceContract
{
    public function __construct(
        private WarehouseIssueOrdersIndexHandler $indexHandler,
        private WarehouseIssueOrdersShowHandler $showHandler,
        private WarehouseIssueOrdersCreateHandler $createHandler,
        private WarehouseIssueOrdersUpdateHandler $updateHandler,
        private WarehouseIssueOrdersDeleteHandler $deleteHandler,
        private WarehouseIssueOrdersHoldHandler $holdHandler
    ) {
    }

    public function index(IndexRequestDTO $dto): array
    {
        return $this->indexHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(object $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(string $id, object $dto): bool
    {
        return $this->updateHandler->run($id, $dto);
    }

    public function delete(string $id): bool
    {
        return $this->deleteHandler->run($id);
    }

    public function hold(string $id): bool
    {
        return $this->holdHandler->run($id);
    }
}
