<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;

readonly class WarehouseIssueOrdersShowHandler
{
    public function __construct(
        private WarehouseIssueOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id): ?object
    {
        return $this->repository->show($id);
    }
}
