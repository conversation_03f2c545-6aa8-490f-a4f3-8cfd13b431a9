<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Traits\HasOrderedUuid;
use Exception;

readonly class WarehouseIssueOrdersCreateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private WarehouseIssueOrdersRepositoryContract $repository,
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeService
    ) {
    }

    public function run(object $dto): string
    {
        // Проверяем что склад работает в ордерной схеме
        $isOrderSchemeActive = $this->orderSchemeService
            ->isOrderSchemeActiveForShipments($dto->warehouseId, $dto->dateFrom);
            
        if (!$isOrderSchemeActive) {
            throw new Exception('Склад не работает в ордерной схеме для отгрузок');
        }

        $id = $this->generateUuid();
        
        $data = [
            'id' => $id,
            'cabinet_id' => $dto->cabinetId,
            'employee_id' => $dto->employeeId,
            'department_id' => $dto->departmentId,
            'warehouse_id' => $dto->warehouseId,
            'number' => $dto->number ?? null,
            'date_from' => $dto->dateFrom,
            'status_id' => $dto->statusId ?? null,
            'document_basis_type' => $dto->documentBasisType ?? null,
            'document_basis_id' => $dto->documentBasisId ?? null,
            'write_off_reason' => $dto->writeOffReason,
            'reason_description' => $dto->reasonDescription ?? null,
            'total_quantity' => $dto->totalQuantity,
            'total_cost' => $dto->totalCost,
            'comment' => $dto->comment ?? null,
        ];
        
        $this->repository->insert($data);
        
        return $id;
    }
}
