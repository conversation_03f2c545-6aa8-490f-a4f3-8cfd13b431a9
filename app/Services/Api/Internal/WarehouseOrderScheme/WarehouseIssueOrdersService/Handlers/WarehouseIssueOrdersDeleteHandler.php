<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use Exception;

readonly class WarehouseIssueOrdersDeleteHandler
{
    public function __construct(
        private WarehouseIssueOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id): bool
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            throw new Exception('Расходный ордер не найден');
        }
        
        if ($order->held) {
            throw new Exception('Нельзя удалять проведенный расходный ордер');
        }
        
        return $this->repository->delete($id) > 0;
    }
}
