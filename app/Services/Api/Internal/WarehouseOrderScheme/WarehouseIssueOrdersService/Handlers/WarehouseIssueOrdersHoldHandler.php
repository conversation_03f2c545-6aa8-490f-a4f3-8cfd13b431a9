<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use Exception;

readonly class WarehouseIssueOrdersHoldHandler
{
    public function __construct(
        private WarehouseIssueOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id): bool
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            throw new Exception('Расходный ордер не найден');
        }
        
        if ($order->held) {
            throw new Exception('Расходный ордер уже проведен');
        }
        
        return $this->repository->markAsHeld($id) > 0;
    }
}
