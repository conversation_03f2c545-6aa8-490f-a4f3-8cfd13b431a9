<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use Exception;

readonly class WarehouseIssueOrdersUpdateHandler
{
    public function __construct(
        private WarehouseIssueOrdersRepositoryContract $repository
    ) {
    }

    public function run(string $id, object $dto): bool
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            throw new Exception('Расходный ордер не найден');
        }
        
        if ($order->held) {
            throw new Exception('Нельзя изменять проведенный расходный ордер');
        }
        
        $data = [];
        
        if (isset($dto->number)) {
            $data['number'] = $dto->number;
        }
        
        if (isset($dto->dateFrom)) {
            $data['date_from'] = $dto->dateFrom;
        }
        
        if (isset($dto->statusId)) {
            $data['status_id'] = $dto->statusId;
        }
        
        if (isset($dto->writeOffReason)) {
            $data['write_off_reason'] = $dto->writeOffReason;
        }
        
        if (isset($dto->reasonDescription)) {
            $data['reason_description'] = $dto->reasonDescription;
        }
        
        if (isset($dto->comment)) {
            $data['comment'] = $dto->comment;
        }
        
        if (empty($data)) {
            return true;
        }
        
        return $this->repository->update($id, $data) > 0;
    }
}
