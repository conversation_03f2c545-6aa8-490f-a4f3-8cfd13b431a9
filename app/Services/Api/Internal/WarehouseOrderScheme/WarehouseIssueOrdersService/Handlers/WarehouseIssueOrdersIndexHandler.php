<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrdersService\Handlers;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\DTO\IndexRequestDTO;

readonly class WarehouseIssueOrdersIndexHandler
{
    public function __construct(
        private WarehouseIssueOrdersRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): array
    {
        $warehouseId = $dto->filters['warehouse_id'] ?? '';
        $orders = $this->repository->getByWarehouse($warehouseId, $dto->filters);
        
        return [
            'data' => $orders,
            'meta' => [
                'total' => $orders->count(),
                'filters' => $dto->filters
            ]
        ];
    }
}
