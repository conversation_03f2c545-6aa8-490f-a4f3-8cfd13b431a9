<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class StockMovementReportHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function run(array $filters): array
    {
        $warehouseId = $filters['warehouse_id'];
        $dateFrom = $filters['date_from'] ?? now()->subMonth()->toDateString();
        $dateTo = $filters['date_to'] ?? now()->toDateString();
        $productId = $filters['product_id'] ?? null;

        // Проверяем активна ли ордерная схема
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, $dateFrom);

        if ($isOrderSchemeActive) {
            return $this->getOrderSchemeMovementReport($warehouseId, $dateFrom, $dateTo, $productId);
        } else {
            return $this->getRegularMovementReport($warehouseId, $dateFrom, $dateTo, $productId);
        }
    }

    private function getOrderSchemeMovementReport(string $warehouseId, string $dateFrom, string $dateTo, ?string $productId): array
    {
        $query = DB::table('warehouse_transactions as wt')
            ->join('products as p', 'wt.product_id', '=', 'p.id')
            ->leftJoin('warehouse_items as wi', 'wt.warehouse_item_id', '=', 'wi.id')
            ->where('wt.warehouse_id', $warehouseId)
            ->whereBetween('wt.transaction_date', [$dateFrom, $dateTo])
            ->select([
                'wt.id',
                'wt.transaction_date',
                'wt.transaction_type',
                'wt.document_type',
                'wt.document_id',
                'wt.quantity',
                'wt.unit_price',
                'wt.total_price',
                'p.name as product_name',
                'p.sku as product_sku',
                'wi.batch_number',
                'wi.lot_number',
                'wi.expiry_date',
                'wi.quality_status',
                DB::raw('CASE 
                    WHEN wt.transaction_type = "in" THEN wt.quantity 
                    ELSE 0 
                END as quantity_in'),
                DB::raw('CASE 
                    WHEN wt.transaction_type = "out" THEN wt.quantity 
                    ELSE 0 
                END as quantity_out')
            ]);

        if ($productId) {
            $query->where('wt.product_id', $productId);
        }

        $movements = $query->orderBy('wt.transaction_date', 'desc')
            ->orderBy('wt.created_at', 'desc')
            ->get();

        // Группируем по товарам для сводки
        $summary = $movements->groupBy('product_id')->map(function ($productMovements) {
            $totalIn = $productMovements->sum('quantity_in');
            $totalOut = $productMovements->sum('quantity_out');
            
            return [
                'product_name' => $productMovements->first()->product_name,
                'product_sku' => $productMovements->first()->product_sku,
                'total_in' => $totalIn,
                'total_out' => $totalOut,
                'net_movement' => $totalIn - $totalOut,
                'movements_count' => $productMovements->count()
            ];
        })->values();

        return [
            'movements' => $movements,
            'summary' => $summary,
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ],
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => true
        ];
    }

    private function getRegularMovementReport(string $warehouseId, string $dateFrom, string $dateTo, ?string $productId): array
    {
        // Для обычной схемы используем упрощенную логику без детализации по партиям
        $query = DB::table('warehouse_transactions as wt')
            ->join('products as p', 'wt.product_id', '=', 'p.id')
            ->where('wt.warehouse_id', $warehouseId)
            ->whereBetween('wt.transaction_date', [$dateFrom, $dateTo])
            ->select([
                'wt.id',
                'wt.transaction_date',
                'wt.transaction_type',
                'wt.document_type',
                'wt.document_id',
                'wt.quantity',
                'wt.unit_price',
                'wt.total_price',
                'p.name as product_name',
                'p.sku as product_sku',
                DB::raw('CASE 
                    WHEN wt.transaction_type = "in" THEN wt.quantity 
                    ELSE 0 
                END as quantity_in'),
                DB::raw('CASE 
                    WHEN wt.transaction_type = "out" THEN wt.quantity 
                    ELSE 0 
                END as quantity_out')
            ]);

        if ($productId) {
            $query->where('wt.product_id', $productId);
        }

        $movements = $query->orderBy('wt.transaction_date', 'desc')->get();

        $summary = $movements->groupBy('product_id')->map(function ($productMovements) {
            $totalIn = $productMovements->sum('quantity_in');
            $totalOut = $productMovements->sum('quantity_out');
            
            return [
                'product_name' => $productMovements->first()->product_name,
                'product_sku' => $productMovements->first()->product_sku,
                'total_in' => $totalIn,
                'total_out' => $totalOut,
                'net_movement' => $totalIn - $totalOut,
                'movements_count' => $productMovements->count()
            ];
        })->values();

        return [
            'movements' => $movements,
            'summary' => $summary,
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ],
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => false
        ];
    }
}
