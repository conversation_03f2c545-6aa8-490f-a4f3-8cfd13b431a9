<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class InventoryReportHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function run(array $filters): array
    {
        $warehouseId = $filters['warehouse_id'];
        $productId = $filters['product_id'] ?? null;
        $qualityStatus = $filters['quality_status'] ?? null;
        $includeExpired = $filters['include_expired'] ?? false;

        // Проверяем активна ли ордерная схема
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, now()->toDateString());

        if ($isOrderSchemeActive) {
            return $this->getOrderSchemeInventoryReport($warehouseId, $productId, $qualityStatus, $includeExpired);
        } else {
            return $this->getRegularInventoryReport($warehouseId, $productId, $qualityStatus);
        }
    }

    private function getOrderSchemeInventoryReport(string $warehouseId, ?string $productId, ?string $qualityStatus, bool $includeExpired): array
    {
        $query = DB::table('warehouse_items as wi')
            ->join('products as p', 'wi.product_id', '=', 'p.id')
            ->leftJoin('warehouse_reservations as wr', function ($join) {
                $join->on('wi.id', '=', 'wr.warehouse_item_id')
                     ->where('wr.status', '=', 'reserved');
            })
            ->where('wi.warehouse_id', $warehouseId)
            ->where('wi.status', '!=', 'out_of_stock')
            ->select([
                'wi.id as warehouse_item_id',
                'wi.product_id',
                'p.name as product_name',
                'p.sku as product_sku',
                'wi.quantity',
                'wi.available_quantity',
                'wi.reserved_quantity',
                'wi.batch_number',
                'wi.lot_number',
                'wi.expiry_date',
                'wi.quality_status',
                'wi.received_at',
                'wi.unit_price',
                DB::raw('wi.quantity * CAST(wi.unit_price as DECIMAL(15,2)) as total_value'),
                DB::raw('COALESCE(SUM(wr.reserved_quantity), 0) as active_reservations'),
                DB::raw('CASE 
                    WHEN wi.expiry_date IS NULL THEN "no_expiry"
                    WHEN wi.expiry_date < NOW() THEN "expired"
                    WHEN wi.expiry_date < DATE_ADD(NOW(), INTERVAL 7 DAY) THEN "expiring_week"
                    WHEN wi.expiry_date < DATE_ADD(NOW(), INTERVAL 30 DAY) THEN "expiring_month"
                    ELSE "good"
                END as expiry_status')
            ])
            ->groupBy([
                'wi.id', 'wi.product_id', 'p.name', 'p.sku', 'wi.quantity',
                'wi.available_quantity', 'wi.reserved_quantity', 'wi.batch_number',
                'wi.lot_number', 'wi.expiry_date', 'wi.quality_status',
                'wi.received_at', 'wi.unit_price'
            ]);

        if ($productId) {
            $query->where('wi.product_id', $productId);
        }

        if ($qualityStatus) {
            $query->where('wi.quality_status', $qualityStatus);
        }

        if (!$includeExpired) {
            $query->where(function ($q) {
                $q->whereNull('wi.expiry_date')
                  ->orWhere('wi.expiry_date', '>=', now());
            });
        }

        $inventory = $query->orderBy('p.name')
            ->orderBy('wi.received_at')
            ->get();

        // Сводка по товарам
        $productSummary = $inventory->groupBy('product_id')->map(function ($productItems) {
            $totalQuantity = $productItems->sum('quantity');
            $availableQuantity = $productItems->sum('available_quantity');
            $reservedQuantity = $productItems->sum('reserved_quantity');
            $totalValue = $productItems->sum('total_value');

            return [
                'product_name' => $productItems->first()->product_name,
                'product_sku' => $productItems->first()->product_sku,
                'total_quantity' => $totalQuantity,
                'available_quantity' => $availableQuantity,
                'reserved_quantity' => $reservedQuantity,
                'total_value' => $totalValue,
                'batches_count' => $productItems->count(),
                'quality_breakdown' => $productItems->groupBy('quality_status')->map(function ($qualityItems, $quality) {
                    return [
                        'quality' => $quality,
                        'quantity' => $qualityItems->sum('quantity'),
                        'batches' => $qualityItems->count()
                    ];
                })->values(),
                'expiry_breakdown' => $productItems->groupBy('expiry_status')->map(function ($expiryItems, $status) {
                    return [
                        'status' => $status,
                        'quantity' => $expiryItems->sum('quantity'),
                        'batches' => $expiryItems->count()
                    ];
                })->values()
            ];
        })->values();

        // Общая сводка
        $totalSummary = [
            'total_items' => $inventory->count(),
            'total_products' => $inventory->unique('product_id')->count(),
            'total_quantity' => $inventory->sum('quantity'),
            'available_quantity' => $inventory->sum('available_quantity'),
            'reserved_quantity' => $inventory->sum('reserved_quantity'),
            'total_value' => $inventory->sum('total_value'),
            'quality_summary' => $inventory->groupBy('quality_status')->map(function ($qualityItems, $quality) {
                return [
                    'quality' => $quality,
                    'quantity' => $qualityItems->sum('quantity'),
                    'value' => $qualityItems->sum('total_value'),
                    'items_count' => $qualityItems->count()
                ];
            })->values(),
            'expiry_summary' => $inventory->groupBy('expiry_status')->map(function ($expiryItems, $status) {
                return [
                    'status' => $status,
                    'quantity' => $expiryItems->sum('quantity'),
                    'value' => $expiryItems->sum('total_value'),
                    'items_count' => $expiryItems->count()
                ];
            })->values()
        ];

        return [
            'inventory' => $inventory,
            'product_summary' => $productSummary,
            'total_summary' => $totalSummary,
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => true,
            'generated_at' => now()->toISOString()
        ];
    }

    private function getRegularInventoryReport(string $warehouseId, ?string $productId, ?string $qualityStatus): array
    {
        $query = DB::table('warehouse_items as wi')
            ->join('products as p', 'wi.product_id', '=', 'p.id')
            ->where('wi.warehouse_id', $warehouseId)
            ->where('wi.status', '!=', 'out_of_stock')
            ->select([
                'wi.id as warehouse_item_id',
                'wi.product_id',
                'p.name as product_name',
                'p.sku as product_sku',
                'wi.quantity',
                'wi.unit_price',
                DB::raw('wi.quantity * CAST(wi.unit_price as DECIMAL(15,2)) as total_value'),
                'wi.received_at'
            ]);

        if ($productId) {
            $query->where('wi.product_id', $productId);
        }

        $inventory = $query->orderBy('p.name')->get();

        // Простая сводка для обычной схемы
        $productSummary = $inventory->groupBy('product_id')->map(function ($productItems) {
            return [
                'product_name' => $productItems->first()->product_name,
                'product_sku' => $productItems->first()->product_sku,
                'total_quantity' => $productItems->sum('quantity'),
                'total_value' => $productItems->sum('total_value'),
                'items_count' => $productItems->count()
            ];
        })->values();

        $totalSummary = [
            'total_items' => $inventory->count(),
            'total_products' => $inventory->unique('product_id')->count(),
            'total_quantity' => $inventory->sum('quantity'),
            'total_value' => $inventory->sum('total_value')
        ];

        return [
            'inventory' => $inventory,
            'product_summary' => $productSummary,
            'total_summary' => $totalSummary,
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => false,
            'generated_at' => now()->toISOString()
        ];
    }
}
