<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class OrderSchemeAnalyticsHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function run(array $filters): array
    {
        $warehouseId = $filters['warehouse_id'];
        $dateFrom = $filters['date_from'] ?? now()->subMonth()->toDateString();
        $dateTo = $filters['date_to'] ?? now()->toDateString();

        // Проверяем активна ли ордерная схема
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, $dateFrom);

        if (!$isOrderSchemeActive) {
            return [
                'analytics' => [],
                'message' => 'Ордерная схема не активна для данного склада',
                'order_scheme_active' => false
            ];
        }

        return [
            'analytics' => [
                'document_statistics' => $this->getDocumentStatistics($warehouseId, $dateFrom, $dateTo),
                'reservation_efficiency' => $this->getReservationEfficiency($warehouseId, $dateFrom, $dateTo),
                'inventory_turnover' => $this->getInventoryTurnover($warehouseId, $dateFrom, $dateTo),
                'quality_control' => $this->getQualityControlStats($warehouseId, $dateFrom, $dateTo),
                'expiry_management' => $this->getExpiryManagementStats($warehouseId, $dateFrom, $dateTo)
            ],
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ],
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => true
        ];
    }

    private function getDocumentStatistics(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        // Статистика по приходным ордерам
        $receiptOrders = DB::table('warehouse_receipt_orders')
            ->where('warehouse_id', $warehouseId)
            ->whereBetween('date_from', [$dateFrom, $dateTo])
            ->selectRaw('
                COUNT(*) as total_count,
                COUNT(CASE WHEN held = true THEN 1 END) as held_count,
                SUM(total_quantity) as total_quantity,
                SUM(CAST(total_cost as DECIMAL(15,2))) as total_cost
            ')
            ->first();

        // Статистика по расходным ордерам
        $issueOrders = DB::table('warehouse_issue_orders')
            ->where('warehouse_id', $warehouseId)
            ->whereBetween('date_from', [$dateFrom, $dateTo])
            ->selectRaw('
                COUNT(*) as total_count,
                COUNT(CASE WHEN held = true THEN 1 END) as held_count,
                SUM(total_quantity) as total_quantity,
                SUM(CAST(total_cost as DECIMAL(15,2))) as total_cost
            ')
            ->first();

        // Статистика по причинам списания
        $writeOffReasons = DB::table('warehouse_issue_orders')
            ->where('warehouse_id', $warehouseId)
            ->whereBetween('date_from', [$dateFrom, $dateTo])
            ->where('held', true)
            ->groupBy('write_off_reason')
            ->selectRaw('
                write_off_reason,
                COUNT(*) as count,
                SUM(total_quantity) as quantity,
                SUM(CAST(total_cost as DECIMAL(15,2))) as cost
            ')
            ->get();

        return [
            'receipt_orders' => [
                'total' => $receiptOrders->total_count ?? 0,
                'held' => $receiptOrders->held_count ?? 0,
                'quantity' => $receiptOrders->total_quantity ?? 0,
                'cost' => $receiptOrders->total_cost ?? '0.00'
            ],
            'issue_orders' => [
                'total' => $issueOrders->total_count ?? 0,
                'held' => $issueOrders->held_count ?? 0,
                'quantity' => $issueOrders->total_quantity ?? 0,
                'cost' => $issueOrders->total_cost ?? '0.00'
            ],
            'write_off_reasons' => $writeOffReasons
        ];
    }

    private function getReservationEfficiency(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        $reservationStats = DB::table('warehouse_reservations as wr')
            ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
            ->where('wi.warehouse_id', $warehouseId)
            ->whereBetween('wr.reserved_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->selectRaw('
                COUNT(*) as total_reservations,
                COUNT(CASE WHEN wr.status = "reserved" THEN 1 END) as active_reservations,
                COUNT(CASE WHEN wr.status = "shipped" THEN 1 END) as shipped_reservations,
                COUNT(CASE WHEN wr.status = "cancelled" THEN 1 END) as cancelled_reservations,
                COUNT(CASE WHEN wr.expires_at < NOW() AND wr.status = "reserved" THEN 1 END) as expired_reservations,
                SUM(wr.reserved_quantity) as total_reserved_quantity,
                AVG(TIMESTAMPDIFF(HOUR, wr.reserved_at, COALESCE(wr.updated_at, NOW()))) as avg_reservation_duration_hours
            ')
            ->first();

        $fulfillmentRate = $reservationStats->total_reservations > 0 
            ? ($reservationStats->shipped_reservations / $reservationStats->total_reservations) * 100 
            : 0;

        $cancellationRate = $reservationStats->total_reservations > 0 
            ? ($reservationStats->cancelled_reservations / $reservationStats->total_reservations) * 100 
            : 0;

        return [
            'total_reservations' => $reservationStats->total_reservations ?? 0,
            'active_reservations' => $reservationStats->active_reservations ?? 0,
            'shipped_reservations' => $reservationStats->shipped_reservations ?? 0,
            'cancelled_reservations' => $reservationStats->cancelled_reservations ?? 0,
            'expired_reservations' => $reservationStats->expired_reservations ?? 0,
            'total_reserved_quantity' => $reservationStats->total_reserved_quantity ?? 0,
            'avg_reservation_duration_hours' => round($reservationStats->avg_reservation_duration_hours ?? 0, 2),
            'fulfillment_rate' => round($fulfillmentRate, 2),
            'cancellation_rate' => round($cancellationRate, 2)
        ];
    }

    private function getInventoryTurnover(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        // Оборачиваемость по товарам
        $turnover = DB::table('warehouse_transactions as wt')
            ->join('products as p', 'wt.product_id', '=', 'p.id')
            ->where('wt.warehouse_id', $warehouseId)
            ->whereBetween('wt.transaction_date', [$dateFrom, $dateTo])
            ->groupBy('wt.product_id', 'p.name', 'p.sku')
            ->selectRaw('
                wt.product_id,
                p.name as product_name,
                p.sku as product_sku,
                SUM(CASE WHEN wt.transaction_type = "in" THEN wt.quantity ELSE 0 END) as total_in,
                SUM(CASE WHEN wt.transaction_type = "out" THEN wt.quantity ELSE 0 END) as total_out,
                COUNT(*) as transaction_count
            ')
            ->having('total_in', '>', 0)
            ->orderBy('total_out', 'desc')
            ->limit(20)
            ->get();

        return [
            'top_products' => $turnover,
            'period_days' => now()->parse($dateTo)->diffInDays(now()->parse($dateFrom)) + 1
        ];
    }

    private function getQualityControlStats(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        $qualityStats = DB::table('warehouse_items')
            ->where('warehouse_id', $warehouseId)
            ->whereBetween('received_at', [$dateFrom, $dateTo])
            ->groupBy('quality_status')
            ->selectRaw('
                quality_status,
                COUNT(*) as items_count,
                SUM(quantity) as total_quantity
            ')
            ->get();

        return $qualityStats->keyBy('quality_status')->toArray();
    }

    private function getExpiryManagementStats(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        $expiryStats = DB::table('warehouse_items')
            ->where('warehouse_id', $warehouseId)
            ->whereNotNull('expiry_date')
            ->selectRaw('
                COUNT(CASE WHEN expiry_date < NOW() THEN 1 END) as expired_items,
                COUNT(CASE WHEN expiry_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 1 END) as expiring_week,
                COUNT(CASE WHEN expiry_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 1 END) as expiring_month,
                SUM(CASE WHEN expiry_date < NOW() THEN quantity ELSE 0 END) as expired_quantity,
                SUM(CASE WHEN expiry_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) THEN quantity ELSE 0 END) as expiring_week_quantity,
                SUM(CASE WHEN expiry_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) THEN quantity ELSE 0 END) as expiring_month_quantity
            ')
            ->first();

        return [
            'expired_items' => $expiryStats->expired_items ?? 0,
            'expiring_week' => $expiryStats->expiring_week ?? 0,
            'expiring_month' => $expiryStats->expiring_month ?? 0,
            'expired_quantity' => $expiryStats->expired_quantity ?? 0,
            'expiring_week_quantity' => $expiryStats->expiring_week_quantity ?? 0,
            'expiring_month_quantity' => $expiryStats->expiring_month_quantity ?? 0
        ];
    }
}
