<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\Facades\DB;

readonly class ReservationReportHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService
    ) {
    }

    public function run(array $filters): array
    {
        $warehouseId = $filters['warehouse_id'];
        $dateFrom = $filters['date_from'] ?? now()->subMonth()->toDateString();
        $dateTo = $filters['date_to'] ?? now()->toDateString();
        $reservationType = $filters['reservation_type'] ?? null;
        $status = $filters['status'] ?? null;

        // Проверяем активна ли ордерная схема
        $isOrderSchemeActive = $this->orderSchemeDetectionService
            ->isOrderSchemeActiveForShipments($warehouseId, $dateFrom);

        if (!$isOrderSchemeActive) {
            return [
                'reservations' => [],
                'summary' => [],
                'message' => 'Ордерная схема не активна для данного склада',
                'order_scheme_active' => false
            ];
        }

        return $this->getReservationReport($warehouseId, $dateFrom, $dateTo, $reservationType, $status);
    }

    private function getReservationReport(string $warehouseId, string $dateFrom, string $dateTo, ?string $reservationType, ?string $status): array
    {
        $query = DB::table('warehouse_reservations as wr')
            ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
            ->join('products as p', 'wi.product_id', '=', 'p.id')
            ->leftJoin('customer_order_items as coi', 'wr.customer_order_item_id', '=', 'coi.id')
            ->leftJoin('customer_orders as co', 'coi.order_id', '=', 'co.id')
            ->where('wi.warehouse_id', $warehouseId)
            ->whereBetween('wr.reserved_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->select([
                'wr.id',
                'wr.reserved_at',
                'wr.status',
                'wr.reservation_type',
                'wr.reserved_quantity',
                'wr.priority',
                'wr.expires_at',
                'wr.document_type',
                'wr.document_id',
                'p.name as product_name',
                'p.sku as product_sku',
                'wi.batch_number',
                'wi.lot_number',
                'wi.expiry_date',
                'wi.quality_status',
                'co.number as order_number',
                DB::raw('CASE 
                    WHEN wr.expires_at < NOW() AND wr.status = "reserved" THEN "expired"
                    ELSE wr.status 
                END as actual_status')
            ]);

        if ($reservationType) {
            $query->where('wr.reservation_type', $reservationType);
        }

        if ($status) {
            if ($status === 'expired') {
                $query->where('wr.expires_at', '<', now())
                     ->where('wr.status', 'reserved');
            } else {
                $query->where('wr.status', $status);
            }
        }

        $reservations = $query->orderBy('wr.reserved_at', 'desc')->get();

        // Сводка по типам резервирования
        $summaryByType = $reservations->groupBy('reservation_type')->map(function ($typeReservations, $type) {
            return [
                'type' => $type,
                'total_reservations' => $typeReservations->count(),
                'total_quantity' => $typeReservations->sum('reserved_quantity'),
                'active_reservations' => $typeReservations->where('status', 'reserved')->count(),
                'shipped_reservations' => $typeReservations->where('status', 'shipped')->count(),
                'cancelled_reservations' => $typeReservations->where('status', 'cancelled')->count(),
                'expired_reservations' => $typeReservations->where('actual_status', 'expired')->count()
            ];
        })->values();

        // Сводка по статусам
        $summaryByStatus = $reservations->groupBy('actual_status')->map(function ($statusReservations, $status) {
            return [
                'status' => $status,
                'count' => $statusReservations->count(),
                'total_quantity' => $statusReservations->sum('reserved_quantity')
            ];
        })->values();

        // Сводка по товарам
        $summaryByProduct = $reservations->groupBy('product_sku')->map(function ($productReservations) {
            return [
                'product_name' => $productReservations->first()->product_name,
                'product_sku' => $productReservations->first()->product_sku,
                'total_reserved' => $productReservations->sum('reserved_quantity'),
                'reservations_count' => $productReservations->count(),
                'active_quantity' => $productReservations->where('status', 'reserved')->sum('reserved_quantity')
            ];
        })->values();

        return [
            'reservations' => $reservations,
            'summary' => [
                'by_type' => $summaryByType,
                'by_status' => $summaryByStatus,
                'by_product' => $summaryByProduct,
                'totals' => [
                    'total_reservations' => $reservations->count(),
                    'total_quantity' => $reservations->sum('reserved_quantity'),
                    'active_quantity' => $reservations->where('status', 'reserved')->sum('reserved_quantity')
                ]
            ],
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ],
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => true
        ];
    }
}
