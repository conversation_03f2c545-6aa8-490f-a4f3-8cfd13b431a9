<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\ReportsService;

use App\Contracts\Services\Internal\WarehouseOrderSchemeReportsServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers\StockMovementReportHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers\ReservationReportHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers\OrderSchemeAnalyticsHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\ReportsService\Handlers\InventoryReportHandler;

readonly class WarehouseOrderSchemeReportsService implements WarehouseOrderSchemeReportsServiceContract
{
    public function __construct(
        private StockMovementReportHandler $stockMovementHandler,
        private ReservationReportHandler $reservationHandler,
        private OrderSchemeAnalyticsHandler $analyticsHandler,
        private InventoryReportHandler $inventoryHandler
    ) {
    }

    public function getStockMovementReport(array $filters): array
    {
        return $this->stockMovementHandler->run($filters);
    }

    public function getReservationReport(array $filters): array
    {
        return $this->reservationHandler->run($filters);
    }

    public function getOrderSchemeAnalytics(array $filters): array
    {
        return $this->analyticsHandler->run($filters);
    }

    public function getInventoryReport(array $filters): array
    {
        return $this->inventoryHandler->run($filters);
    }
}
