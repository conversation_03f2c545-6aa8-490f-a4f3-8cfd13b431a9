<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService;

use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Traits\PrecisionCalculator;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для расчета НДС в позициях заказов поставщика
 */
class VendorOrderItemVatCalculatorService
{
    use PrecisionCalculator;

    public function __construct(
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
    }

    /**
     * Рассчитывает детали НДС для позиции заказа поставщика
     *
     * @param string $orderId ID заказа поставщика
     * @param string $priceInCurrency Цена в валюте
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string|null $vatRateId ID ставки НДС
     * @param string $cabinetId ID кабинета
     * @return array
     */
    public function calculateItemVatDetails(
        string $orderId,
        string $priceInCurrency,
        string $quantity,
        string $discount,
        ?string $vatRateId,
        string $cabinetId
    ): array {
        $order = DB::table('vendor_orders')
            ->where('id', $orderId)
            ->select(['has_vat', 'price_includes_vat'])
            ->first();

        if (!$order) {
            throw new \InvalidArgumentException('Vendor order not found');
        }

        // Если НДС отключен для заказа и не указана ставка НДС
        if (!$order->has_vat && !$vatRateId) {
            $vatRateId = $this->getAutoVatRateId($cabinetId);
        }

        $vatRate = '0';
        if ($vatRateId) {
            $vatRateRecord = $this->vatRatesRepository->show($vatRateId);
            $vatRate = $vatRateRecord?->rate ?? '0';
        }

        return $this->calculateVatDetails(
            $priceInCurrency,
            $quantity,
            $discount,
            $vatRate,
            $order->price_includes_vat,
            $order->has_vat
        );
    }

    /**
     * Рассчитывает итоговую сумму позиции с НДС
     *
     * @param string $orderId ID заказа поставщика
     * @param string $priceInCurrency Цена в валюте
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string|null $vatRateId ID ставки НДС
     * @param string $cabinetId ID кабинета
     * @return string
     */
    public function calculateItemTotal(
        string $orderId,
        string $priceInCurrency,
        string $quantity,
        string $discount,
        ?string $vatRateId,
        string $cabinetId
    ): string {
        $details = $this->calculateItemVatDetails(
            $orderId,
            $priceInCurrency,
            $quantity,
            $discount,
            $vatRateId,
            $cabinetId
        );

        return $details['total_with_vat'];
    }

    /**
     * Получает ID ставки НДС "Без НДС" для автоматического назначения
     *
     * @param string $cabinetId ID кабинета
     * @return string|null
     */
    public function getAutoVatRateId(string $cabinetId): ?string
    {
        $vatRate = $this->vatRatesRepository->getByRateAndCabinet('0', $cabinetId);
        return $vatRate?->id;
    }

    /**
     * Внутренний метод для расчета деталей НДС
     *
     * @param string $price Цена
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string $vatRate Ставка НДС
     * @param bool $priceIncludesVat Цена включает НДС
     * @param bool $hasVat Документ с НДС
     * @return array
     */
    private function calculateVatDetails(
        string $price,
        string $quantity,
        string $discount,
        string $vatRate,
        bool $priceIncludesVat,
        bool $hasVat
    ): array {
        // Рассчитываем сумму без скидки
        $subtotal = $this->multiply($price, $quantity);

        // Применяем скидку
        $discountAmount = '0';
        if ($this->compare($discount, '0') > 0) {
            $discountPercent = $this->divide($discount, '100');
            $discountAmount = $this->multiply($subtotal, $discountPercent);
            $subtotal = $this->subtract($subtotal, $discountAmount);
        }

        // Если документ без НДС, возвращаем сумму без НДС
        if (!$hasVat) {
            return [
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'vat_amount' => '0',
                'total_with_vat' => $subtotal,
                'vat_rate' => $vatRate,
                'price_includes_vat' => $priceIncludesVat,
                'has_vat' => $hasVat
            ];
        }

        // Если ставка НДС равна 0, возвращаем сумму без НДС
        if ($this->compare($vatRate, '0') === 0) {
            return [
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'vat_amount' => '0',
                'total_with_vat' => $subtotal,
                'vat_rate' => $vatRate,
                'price_includes_vat' => $priceIncludesVat,
                'has_vat' => $hasVat
            ];
        }

        $vatDecimal = $this->divide($vatRate, '100');
        $vatAmount = '0';
        $totalWithVat = $subtotal;

        if ($priceIncludesVat) {
            // Цена включает НДС - извлекаем НДС из цены
            $vatMultiplier = $this->add('1', $vatDecimal);
            $baseAmount = $this->divide($subtotal, $vatMultiplier);
            $vatAmount = $this->subtract($subtotal, $baseAmount);
            $totalWithVat = $subtotal;
        } else {
            // Цена без НДС - добавляем НДС к цене
            $vatAmount = $this->multiply($subtotal, $vatDecimal);
            $totalWithVat = $this->add($subtotal, $vatAmount);
        }

        return [
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'vat_amount' => $vatAmount,
            'total_with_vat' => $totalWithVat,
            'vat_rate' => $vatRate,
            'price_includes_vat' => $priceIncludesVat,
            'has_vat' => $hasVat
        ];
    }
} 