<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use Illuminate\Support\Carbon;

class AcceptanceDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $legalEntityId,
        public string $contractorId,
        public string $warehouseId,
        public string $departmentId,
        public string $employeeId,
        public int $userId,
        public string $currencyId,
        public string $currencyValue,
        public ?string $number = null,
        public ?string $dateFrom = null,
        public ?string $statusId = null,
        public ?bool $held = true,
        public ?string $incomingNumber = null,
        public ?string $incomingDate = null,
        public ?string $comment = null,
        public ?bool $priceIncludesVat = true,
        public ?bool $hasVat = true,
        public string $overheadCost = '0',
        public array $files = [],
        public ?string $resourceId = null,
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'employee_id' => $this->employeeId,
            'number' => $this->number,
            'date_from' => $this->dateFrom,
            'status_id' => $this->statusId,
            'held' => $this->held,
            'legal_entity_id' => $this->legalEntityId,
            'contractor_id' => $this->contractorId,
            'warehouse_id' => $this->warehouseId,
            'currency_id' => $this->currencyId,
            'incoming_number' => $this->incomingNumber,
            'incoming_date' => $this->incomingDate,
            'comment' => $this->comment,
            'price_includes_vat' => $this->priceIncludesVat,
            'has_vat' => $this->hasVat,
            'overhead_cost' => $this->overheadCost,
            'department_id' => $this->departmentId,
            'currency_value' => $this->currencyValue
        ];
    }
    public function toUpdateArray(): array
    {
        return [
            'date_from' => $this->dateFrom,
            'status_id' => $this->statusId,
            'held' => $this->held,
            'legal_entity_id' => $this->legalEntityId,
            'contractor_id' => $this->contractorId,
            'warehouse_id' => $this->warehouseId,
            'currency_id' => $this->currencyId,
            'incoming_number' => $this->incomingNumber,
            'incoming_date' => $this->incomingDate,
            'comment' => $this->comment,
            'price_includes_vat' => $this->priceIncludesVat,
            'has_vat' => $this->hasVat,
            'overhead_cost' => $this->overheadCost,
            'department_id' => $this->departmentId,
            'employee_id' => $this->employeeId,
            'currency_value' => $this->currencyValue
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            legalEntityId: $data['legal_entity_id'],
            contractorId: $data['contractor_id'],
            warehouseId: $data['warehouse_id'],
            departmentId: $data['department_id'],
            employeeId: $data['employee_id'],
            userId: auth()->user()->id,
            currencyId: $data['currency_id'],
            currencyValue: (string)($data['currency_value'] ?? '1'),
            number: $data['number'] ?? null,
            dateFrom: $data['date_from'] ?? Carbon::now(),
            statusId: $data['status_id'] ?? null,
            held: $data['held'] ?? true,
            incomingNumber: $data['incoming_number'] ?? null,
            incomingDate: $data['incoming_date'] ?? null,
            comment: $data['comment'] ?? null,
            priceIncludesVat: $data['price_includes_vat'] ?? true,
            hasVat: $data['has_vat'] ?? true,
            overheadCost: (string)($data['overhead_cost'] ?? '0'),
            files: $data['files'] ?? [],
            resourceId: $data['id'] ?? null
        );
    }
}
