<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Purchases\AcceptancesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceBulkCopyHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceBulkDeleteHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceBulkHeldHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceBulkUnheldHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceCreateHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceDeleteHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceGetHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceShowHandler;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers\AcceptanceUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class AcceptancesService implements AcceptancesServiceContract
{
    public function __construct(
        private AcceptanceCreateHandler $createHandler,
        private AcceptanceGetHandler $getHandler,
        private AcceptanceShowHandler $showHandler,
        private AcceptanceUpdateHandler $updateHandler,
        private AcceptanceDeleteHandler $deleteHandler,
        private AcceptanceBulkDeleteHandler $bulkDeleteHandler,
        private AcceptanceBulkHeldHandler $bulkHeldHandler,
        private AcceptanceBulkUnheldHandler $bulkUnheldHandler,
        private AcceptanceBulkCopyHandler $bulkCopyHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws BindingResolutionException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    /**
     * @throws BindingResolutionException
     */
    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }

    /**
     * @throws BindingResolutionException
     */
    public function bulkHeld(array $ids): void
    {
        $this->bulkHeldHandler->run($ids);
    }

    /**
     * @throws BindingResolutionException
     */
    public function bulkUnheld(array $ids): void
    {
        $this->bulkUnheldHandler->run($ids);
    }

    public function bulkCopy(array $ids): void
    {
        $this->bulkCopyHandler->run($ids);
    }
}
