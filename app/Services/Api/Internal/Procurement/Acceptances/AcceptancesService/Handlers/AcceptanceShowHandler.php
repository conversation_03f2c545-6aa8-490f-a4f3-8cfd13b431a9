<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers;

use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;

class AcceptanceShowHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public function __construct(
        private readonly AcceptanceRepositoryContract $acceptanceRepository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $result = $this->acceptanceRepository->show($resourceId);

        $result->files = json_decode($result->files, true);
        $result->status = json_decode($result->status, true);
        $result->warehouse = json_decode($result->warehouse, true);
        $result->contractor = json_decode($result->contractor, true);

        if ($result->files) {
            $this->generateUrls($result->files);
        }

        return $result;
    }
}
