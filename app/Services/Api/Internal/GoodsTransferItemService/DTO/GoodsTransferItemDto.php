<?php

namespace App\Services\Api\Internal\GoodsTransferItemService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class GoodsTransferItemDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public int $totalPrice;

    public function __construct(
        public int $quantity,
        public ?string $goodsTransferId = null,
        public ?string $productId = null,
        public int $price = 0,
        public ?int $recidualFrom = 0,
        public ?int $recidualTo = 0,
        public ?string $resourceId = null
    ) {
        $this->totalPrice = $this->price * $this->quantity;
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'goods_transfer_id' => $this->goodsTransferId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'recidual_from' => $this->recidualFrom,
            'recidual_to' => $this->recidualTo,
            'total_price' => $this->totalPrice
            ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'recidual_from' => $this->recidualFrom,
            'recidual_to' => $this->recidualTo,
            'total_price' => $this->totalPrice
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            quantity: $data['quantity'],
            goodsTransferId: $data['goods_transfer_id'] ?? null,
            productId: $data['product_id'] ?? null,
            price: $data['price'] ?? 0,
            recidualFrom: $data['recidual_from'] ?? 0,
            recidualTo: $data['recidual_to'] ?? 0,
            resourceId: $data['id'] ?? null,
        );
    }
}
