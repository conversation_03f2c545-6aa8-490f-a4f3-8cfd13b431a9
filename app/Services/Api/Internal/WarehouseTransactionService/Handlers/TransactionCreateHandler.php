<?php

namespace App\Services\Api\Internal\WarehouseTransactionService\Handlers;

use App\Contracts\Repositories\WarehouseTransactionsRepositoryContract;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

readonly class TransactionCreateHandler
{
    public function __construct(
        private WarehouseTransactionsRepositoryContract $transactionsRepository
    ) {
    }

    public function run(array $data): bool
    {
        $transactionData = [
            'id' => Str::uuid()->toString(),
            'warehouse_id' => $data['warehouse_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
            'document_type' => $data['document_type'] ?? null,
            'document_id' => $data['document_id'] ?? null,
            'operation_type' => $data['operation_type'],
            'batch_number' => $data['batch_number'] ?? null,
            'lot_number' => $data['lot_number'] ?? null,
            'expiry_date' => $data['expiry_date'] ?? null,
            'quality_status' => $data['quality_status'] ?? 'good',
            'cost_per_unit' => $data['cost_per_unit'] ?? null,
            'reservation_id' => $data['reservation_id'] ?? null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        return $this->transactionsRepository->insert($transactionData);
    }
}
