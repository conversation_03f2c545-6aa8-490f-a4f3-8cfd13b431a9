<?php

namespace App\Services\Api\Internal\WarehouseTransactionService\Handlers;

use App\Contracts\Repositories\WarehouseTransactionsRepositoryContract;

readonly class TransactionReportHandler
{
    public function __construct(
        private WarehouseTransactionsRepositoryContract $transactionsRepository
    ) {
    }

    public function getByDocument(string $documentType, string $documentId): array
    {
        $transactions = $this->transactionsRepository->getByDocument($documentType, $documentId);
        
        return $transactions->toArray();
    }

    public function getByWarehouse(string $warehouseId, array $filters = []): array
    {
        $transactions = $this->transactionsRepository->getByWarehouse($warehouseId, $filters);
        
        return $transactions->toArray();
    }

    public function getByProduct(string $productId, array $filters = []): array
    {
        $transactions = $this->transactionsRepository->getByProduct($productId, $filters);
        
        return $transactions->toArray();
    }

    public function getMovementReport(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        $transactions = $this->transactionsRepository->getMovementReport($warehouseId, $dateFrom, $dateTo);
        
        // Группировка по товарам и типам операций
        $report = [];
        
        foreach ($transactions as $transaction) {
            $productId = $transaction->product_id;
            $operationType = $transaction->operation_type;
            
            if (!isset($report[$productId])) {
                $report[$productId] = [
                    'product_id' => $productId,
                    'product_name' => $transaction->product_name ?? '',
                    'operations' => [],
                    'total_incoming' => 0,
                    'total_outgoing' => 0,
                ];
            }
            
            if (!isset($report[$productId]['operations'][$operationType])) {
                $report[$productId]['operations'][$operationType] = [
                    'operation_type' => $operationType,
                    'total_quantity' => 0,
                    'transactions_count' => 0,
                ];
            }
            
            $report[$productId]['operations'][$operationType]['total_quantity'] += $transaction->quantity;
            $report[$productId]['operations'][$operationType]['transactions_count']++;
            
            // Подсчет общих входящих и исходящих операций
            if (in_array($operationType, ['receipt', 'transfer_in', 'adjustment_plus'])) {
                $report[$productId]['total_incoming'] += $transaction->quantity;
            } elseif (in_array($operationType, ['issue', 'transfer_out', 'adjustment_minus'])) {
                $report[$productId]['total_outgoing'] += $transaction->quantity;
            }
        }
        
        return array_values($report);
    }
}
