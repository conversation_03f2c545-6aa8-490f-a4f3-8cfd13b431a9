<?php

namespace App\Services\Api\Internal\WarehouseTransactionService\Handlers;

use App\Contracts\Repositories\WarehouseTransactionsRepositoryContract;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

readonly class TransactionBulkCreateHandler
{
    public function __construct(
        private WarehouseTransactionsRepositoryContract $transactionsRepository
    ) {
    }

    public function run(array $transactions): bool
    {
        $transactionData = [];
        $now = Carbon::now();

        foreach ($transactions as $transaction) {
            $transactionData[] = [
                'id' => Str::uuid()->toString(),
                'warehouse_id' => $transaction['warehouse_id'],
                'product_id' => $transaction['product_id'],
                'quantity' => $transaction['quantity'],
                'document_type' => $transaction['document_type'] ?? null,
                'document_id' => $transaction['document_id'] ?? null,
                'operation_type' => $transaction['operation_type'],
                'batch_number' => $transaction['batch_number'] ?? null,
                'lot_number' => $transaction['lot_number'] ?? null,
                'expiry_date' => $transaction['expiry_date'] ?? null,
                'quality_status' => $transaction['quality_status'] ?? 'good',
                'cost_per_unit' => $transaction['cost_per_unit'] ?? null,
                'reservation_id' => $transaction['reservation_id'] ?? null,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        return $this->transactionsRepository->bulkInsert($transactionData);
    }
}
