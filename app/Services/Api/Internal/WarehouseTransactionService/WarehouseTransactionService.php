<?php

namespace App\Services\Api\Internal\WarehouseTransactionService;

use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use App\Services\Api\Internal\WarehouseTransactionService\Handlers\TransactionCreateHandler;
use App\Services\Api\Internal\WarehouseTransactionService\Handlers\TransactionBulkCreateHandler;
use App\Services\Api\Internal\WarehouseTransactionService\Handlers\TransactionReportHandler;

readonly class WarehouseTransactionService implements WarehouseTransactionServiceContract
{
    public function __construct(
        private TransactionCreateHandler $createHandler,
        private TransactionBulkCreateHandler $bulkCreateHandler,
        private TransactionReportHandler $reportHandler
    ) {
    }

    public function createTransaction(array $data): bool
    {
        return $this->createHandler->run($data);
    }

    public function createBulkTransactions(array $transactions): bool
    {
        return $this->bulkCreateHandler->run($transactions);
    }

    public function getTransactionsByDocument(string $documentType, string $documentId): array
    {
        return $this->reportHandler->getByDocument($documentType, $documentId);
    }

    public function getTransactionsByWarehouse(string $warehouseId, array $filters = []): array
    {
        return $this->reportHandler->getByWarehouse($warehouseId, $filters);
    }

    public function getTransactionsByProduct(string $productId, array $filters = []): array
    {
        return $this->reportHandler->getByProduct($productId, $filters);
    }

    public function getMovementReport(string $warehouseId, string $dateFrom, string $dateTo): array
    {
        return $this->reportHandler->getMovementReport($warehouseId, $dateFrom, $dateTo);
    }
}
