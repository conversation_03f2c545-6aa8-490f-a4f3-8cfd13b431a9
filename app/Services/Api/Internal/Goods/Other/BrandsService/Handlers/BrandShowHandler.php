<?php

namespace App\Services\Api\Internal\Goods\Other\BrandsService\Handlers;

use App\Contracts\Repositories\BrandsRepositoryContract;
use App\Traits\HasOrderedUuid;

class BrandShowHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly BrandsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
