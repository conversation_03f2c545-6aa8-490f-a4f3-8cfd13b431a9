<?php

namespace App\Services\Api\Internal\Goods\Other\BrandsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\BrandsRepositoryContract;
use App\Traits\HasOrderedUuid;

class BrandCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly BrandsRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
