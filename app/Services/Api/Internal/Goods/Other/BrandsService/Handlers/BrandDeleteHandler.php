<?php

namespace App\Services\Api\Internal\Goods\Other\BrandsService\Handlers;

use App\Contracts\Repositories\BrandsRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;

class BrandDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly BrandsRepositoryContract $repository
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->repository->delete($resourceId);
    }
}
