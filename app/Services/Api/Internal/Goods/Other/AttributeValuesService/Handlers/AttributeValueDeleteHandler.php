<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeValuesService\Handlers;

use App\Contracts\Repositories\AttributeValuesRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;

class AttributeValueDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly AttributeValuesRepositoryContract $repository
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->repository->delete($resourceId);
    }
}
