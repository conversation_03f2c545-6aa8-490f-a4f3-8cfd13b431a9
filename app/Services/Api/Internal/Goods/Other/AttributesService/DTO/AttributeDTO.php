<?php

namespace App\Services\Api\Internal\Goods\Other\AttributesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class AttributeDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $name,
        public ?string $description,
        public ?string $groupId,
        public int $sort_order,
        public bool $status,
        public ?string $resourceId
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'name' => $this->name,
            'description' => $this->description,
            'attribute_groups_id' => $this->groupId,
            'sort_order' => $this->sort_order,
            'status' => $this->status,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'attribute_groups_id' => $this->groupId,
            'sort_order' => $this->sort_order,
            'status' => $this->status
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            name: $data['name'],
            description: $data['description'] ?? null,
            groupId: $data['attribute_groups_id'] ?? null,
            sort_order: $data['sort_order'],
            status: $data['status'] ?? true,
            resourceId: $data['resource_id'] ?? null
        );
    }
}
