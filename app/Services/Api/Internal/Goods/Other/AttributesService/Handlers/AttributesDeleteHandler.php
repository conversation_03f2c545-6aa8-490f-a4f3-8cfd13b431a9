<?php

namespace App\Services\Api\Internal\Goods\Other\AttributesService\Handlers;

use App\Contracts\Repositories\AttributesRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;

class AttributesDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly AttributesRepositoryContract $repository
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->repository->delete($resourceId);
    }
}
