<?php

namespace App\Services\Api\Internal\Goods\Other\AttributesService\Handlers;

use App\Contracts\Repositories\AttributesRepositoryContract;

readonly class AttributesShowHandler
{
    public function __construct(
        private AttributesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
