<?php

namespace App\Services\Api\Internal\Goods\Other\AttributesService\Handlers;

use App\Contracts\Repositories\AttributesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class AttributesGetHandler
{
    public function __construct(
        private AttributesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            id: $dto->id,
            page: $dto->page,
            perPage: $dto->perPage,
            sortField: $dto->sortField,
            sortDirection: $dto->sortDirection,
            fields: $dto->fields,
            filters: $dto->filters
        );
    }
}
