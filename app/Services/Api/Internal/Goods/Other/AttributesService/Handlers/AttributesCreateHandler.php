<?php

namespace App\Services\Api\Internal\Goods\Other\AttributesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\AttributesRepositoryContract;
use App\Traits\HasOrderedUuid;

class AttributesCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly AttributesRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
