<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeGroupsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\AttributeGroupsRepositoryContract;
use App\Services\Api\Internal\Goods\Other\AttributeGroupsService\DTO\AttributeGroupDTO;
use App\Traits\HasOrderedUuid;
use http\Exception\InvalidArgumentException;

class AttributeGroupUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly AttributeGroupsRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof AttributeGroupDTO) {
            throw new InvalidArgumentException();
        }

        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
