<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;

readonly class BarcodeGenerateEAN13Handler
{
    use HasBarcodes;
    use HasOrderedUuid;

    public function __construct(
        private BarcodesRepositoryContract $repository
    ) {
    }

    public function run(string $cabinetId): array
    {
        $maxBarcodeEAN13 =  $this->repository->getMaxBarcodeEAN13ByCabinet($cabinetId);

        if ($maxBarcodeEAN13 == null) {

            return [
                'id' => $this->generateUuid(),
                'value' => 2000000000008
            ];

        }

        $barcodeEAN13 =  $this->repository->getMaxBarcodeEANByCabinetFirst($cabinetId, $maxBarcodeEAN13);
        $data['code'][0] = $barcodeEAN13->barcode;
        return [
            'id' => $this->generateUuid(),
            'value' => $this->codeGenerateEAN13($data)
        ];
    }
}
