<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class BarcodeCreateHandler
{
    use HasOrderedUuid;
    use HasBarcodes;

    private string $resourceId;

    public function __construct(
        private readonly BarcodesRepositoryContract $repository,
        private BarcodeForCreateOrUpdateHandler $handler
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof BarcodeDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $setIdBarcodes = $this->handler->run($dto);

        $this->repository->insert(array_merge(
            $setIdBarcodes,
            ['id' => $this->resourceId]
        ));

        return $this->resourceId;
    }

}
