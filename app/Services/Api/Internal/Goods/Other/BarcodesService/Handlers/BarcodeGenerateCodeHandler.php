<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\Repositories\BarcodesRepositoryContract;

readonly class BarcodeGenerateCodeHandler
{
    public function __construct(
        private BarcodesRepositoryContract $repository
    ) {
    }

    public function run(string $cabinetId): string
    {

        $maxInnerCode =  $this->repository->getMaxInnerCodeByCabinet($cabinetId);

        if ($maxInnerCode == null) {

            return '0001';

        } else {

            $maxCode =  $this->repository->getMaxCodeWithMaxInnerCodeForProductAndPackingsFirst($maxInnerCode);

            $code = $maxCode->code;

            $last = substr($code, -1);

            if (is_numeric($last)) {

                $string = preg_replace('/(\d+).?$/', '', $code);

                $resultInt = preg_split('/[^0-9]/', $code, -1);

                $output = (int)array_pop($resultInt) + 1;

                if (is_numeric($code)) {

                    preg_match('/^(0*)([^d]+)$/', $code, $matches);

                    $int = (int)$matches[2] + 1;

                    // вернёт формат 000007
                    return ($matches[1] . $int);

                }

                // вернёт формат qwer7
                return  $string . $output;

            }

            // вернёт формат sdfgsdf, поидее сюда невозможно попасть, на всякий случай оставлю для тестов
            return preg_replace('/(\d+).?$/', '', $code);

        }
    }
}
