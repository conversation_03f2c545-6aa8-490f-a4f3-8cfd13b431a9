<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class BarcodeBulkUpdateHandler
{
    use HasOrderedUuid;
    use HasBarcodes;

    public function __construct(
        private readonly BarcodesRepositoryContract $repository,
        private BarcodeBulkForCreateOrUpdateHandler $handler
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof BarcodeDTO) {
            throw new InvalidArgumentException();
        }

        $barcodes = collect($dto->barcodes);

        $codes = $barcodes->where('value', '!=', null)->pluck('value')->toArray();

        $setIdBarcodes = $this->handler->run($dto, $codes);

        $incomingBarcodeIds = $setIdBarcodes->pluck('id')->filter()->toArray();

        $this->repository->upsert($setIdBarcodes->toArray());

        $this->repository->deleteOldBarcodes($dto->barcodableId, $incomingBarcodeIds);

    }

}
