<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class BarcodeDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public ?string $barcodableId,
        public ?string $barcodableType,
        public array $barcodes,
        public ?string $resourceId
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            barcodableId: $data['entity_id'] ?? null,
            barcodableType: $data['entity_type'] ?? null,
            barcodes: $data['barcodes'] ?? [],
            resourceId: $data['resource_id'] ?? null
        );
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'barcodable_id' => $this->barcodableId,
            'barcodable_type' => $this->barcodableType,
            'barcodes' => $this->barcodes,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'cabinet_id' => $this->cabinetId,
            // 'barcodable_id' => $this->barcodableId,
            // 'barcodable_type' => $this->barcodableType,
            'barcodes' => $this->barcodes,
        ];
    }
}
