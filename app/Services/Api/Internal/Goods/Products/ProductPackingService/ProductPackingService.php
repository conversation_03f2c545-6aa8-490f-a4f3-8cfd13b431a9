<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPackingService;

use App\Contracts\DtoContract;
use App\Contracts\Services\Internal\Products\ProductPackingServiceContract;
use App\Services\Api\Internal\Goods\Products\ProductPackingService\Handlers\ProductPackingGetHandler;
use App\Services\Api\Internal\Goods\Products\ProductPackingService\Handlers\ProductPackingUpdateHandler;
use Illuminate\Support\Collection;

readonly class ProductPackingService implements ProductPackingServiceContract
{
    public function __construct(
        private ProductPackingGetHandler $getHandler,
        private ProductPackingUpdateHandler $updateHandler,
    ) {
    }

    public function index(array $data): Collection
    {
        return $this->getHandler->run($data['id']);
    }

    public function update(DtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }
}
