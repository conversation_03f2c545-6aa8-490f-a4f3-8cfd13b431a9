<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPackingService\DTO;

use App\Contracts\DtoContract;

class ProductPackingDto implements DtoContract
{
    public function __construct(
        public string $productId,
        public array $packings = [],
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            productId: $data['product_id'],
            packings: $data['packings'] ?? [],
        );
    }
}
