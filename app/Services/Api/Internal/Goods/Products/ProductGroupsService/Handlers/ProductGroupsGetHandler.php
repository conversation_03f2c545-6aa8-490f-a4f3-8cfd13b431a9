<?php

namespace App\Services\Api\Internal\Goods\Products\ProductGroupsService\Handlers;

use App\Contracts\Repositories\ProductGroupsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class ProductGroupsGetHandler
{
    public function __construct(
        private ProductGroupsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
