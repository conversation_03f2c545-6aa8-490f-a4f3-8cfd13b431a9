<?php

namespace App\Services\Api\Internal\Goods\Products\ProductEgaisCodeService\Handlers;

use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductEgaisCodeDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepository,
    ) {
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        if (!$this->productEgaisCodeRepository->delete($resourceId)) {
            throw new ResourceNotFoundException();
        }
    }
}
