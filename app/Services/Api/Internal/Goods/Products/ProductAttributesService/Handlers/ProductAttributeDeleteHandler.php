<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAttributesService\Handlers;

use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;

class ProductAttributeDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductAttributeRepositoryContract $productAttributeRepositoryContract,
    ) {
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function run(string $resourceId): void
    {
        if (!$this->validateUuid($resourceId)) {
            throw new InvalidUuidException();
        }

        $this->productAttributeRepositoryContract->delete($resourceId);
    }
}
