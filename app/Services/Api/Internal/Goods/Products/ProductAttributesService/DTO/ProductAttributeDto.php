<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAttributesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;

class ProductAttributeDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public ?string $productId,
        public string $attributeId,
        public string $attributeValuesId,
        public ?string $resourceId,
        public ?int $sortOrder = null,
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'product_id' => $this->productId,
            'attribute_id' => $this->attributeId,
            'attribute_values_id' => $this->attributeValuesId,
            'sort_order' => $this->sortOrder,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'product_id' => $this->productId,
            'attribute_id' => $this->attributeId,
            'attribute_values_id' => $this->attributeValuesId,
            'sort_order' => $this->sortOrder,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            productId: $data['product_id'],
            attributeId: $data['attribute_id'],
            attributeValuesId: $data['attribute_values_id'],
            resourceId: $data['resource_id'] ?? null,
            sortOrder: $data['sort_order']
        );
    }
}
