<?php

namespace App\Services\Api\Internal\Goods\Products\ProductsService\Handlers;

use App\Contracts\Repositories\ProductsRepositoryContract;
use App\DTO\IndexRequestDTO;
use App\Services\Storage\S3StorageService;
use Illuminate\Support\Collection;

readonly class ProductGetHandler
{
    public function __construct(
        private ProductsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        $storageService = app(S3StorageService::class);

        $data = $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage,
        );

        // $data['data'] = array_map(function ($item) use ($storageService) {
        //     if (!empty($item['logo'])) {
        //         $item['logo']['path'] = $storageService->getUrl($item['logo']['path'], $item['logo']['is_private']);
        //     }
        //     return $item;
        // }, $data['data']);

        return $data;

    }
}
