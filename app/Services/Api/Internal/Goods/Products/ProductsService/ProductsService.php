<?php

namespace App\Services\Api\Internal\Goods\Products\ProductsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Products\ProductsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\InvalidUuidException;
use App\Services\Api\Internal\Goods\Products\ProductsService\Handlers\ProductCreateHandler;
use App\Services\Api\Internal\Goods\Products\ProductsService\Handlers\ProductDeleteHandler;
use App\Services\Api\Internal\Goods\Products\ProductsService\Handlers\ProductGetHandler;
use App\Services\Api\Internal\Goods\Products\ProductsService\Handlers\ProductShowHandler;
use App\Services\Api\Internal\Goods\Products\ProductsService\Handlers\ProductUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class ProductsService implements ProductsServiceContract
{
    public function __construct(
        private ProductCreateHandler $createHandler,
        private ProductGetHandler $getHandler,
        private ProductShowHandler $showHandler,
        private ProductUpdateHandler $updateHandler,
        private ProductDeleteHandler $deleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }


    /**
     * @throws BindingResolutionException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws InvalidUuidException
     */
    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
