<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ProductAccountingFeaturesRepositoryContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;

class ProductAccountingFeatureCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ProductAccountingFeaturesRepositoryContract $productAccountingFeaturesRepository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {

        $this->productAccountingFeaturesRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
