<?php

namespace App\Services\Api\Internal\Goods\Products\ProductThresholdsService\Handlers;

use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductThresholdGetHandler
{
    public function __construct(
        private readonly ProductThresholdsRepositoryContract $ProductThresholdsRepository,
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->ProductThresholdsRepository->get($resourceId);
    }
}
