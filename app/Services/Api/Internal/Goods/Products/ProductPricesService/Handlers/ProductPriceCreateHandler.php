<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPricesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\ProductPricesRepositoryContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use RuntimeException;

class ProductPriceCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ProductPricesRepositoryContract $productPricesRepository,
        private readonly EmployeeRepositoryContract $employeeRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        $employee = $this->employeeRepository->getByUserIdAndCabinet($dto->userId, $dto->cabinetId);

        if (!$employee) {
            throw new RuntimeException('Employee not found');
        }

        $this->productPricesRepository->insert(
            $dto->toInsertArray($this->resourceId, $employee->id)
        );

        return $this->resourceId;
    }
}
