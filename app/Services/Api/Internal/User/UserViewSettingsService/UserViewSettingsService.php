<?php

namespace App\Services\Api\Internal\User\UserViewSettingsService;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\UserViewSettingsServiceContract;
use App\Services\Api\Internal\User\UserViewSettingsService\Handlers\UserViewSettingsDeleteHandler;
use App\Services\Api\Internal\User\UserViewSettingsService\Handlers\UserViewSettingsGetHandler;
use App\Services\Api\Internal\User\UserViewSettingsService\Handlers\UserViewSettingsUpdateHandler;
use Illuminate\Support\Collection;

readonly class UserViewSettingsService implements UserViewSettingsServiceContract
{
    public function __construct(
        private UserViewSettingsGetHandler $getHandler,
        private UserViewSettingsUpdateHandler $updateHandler,
        private UserViewSettingsDeleteHandler $deleteHandler,
    ) {
    }

    public function index(array $data): Collection
    {
        return $this->getHandler->run($data['cabinet_id'], $data['user_id'], $data['name']);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
