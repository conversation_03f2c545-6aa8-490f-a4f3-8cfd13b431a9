<?php

namespace App\Services\Api\Internal\User\UserViewSettingsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\UserViewSettingsRepositoryContract;
use App\Services\Api\Internal\User\UserViewSettingsService\DTO\UserViewSettingsDto;
use InvalidArgumentException;

readonly class UserViewSettingsUpdateHandler
{
    public function __construct(
        private UserViewSettingsRepositoryContract $repository,
        private EmployeeRepositoryContract $employeeRepository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof UserViewSettingsDto) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }

        $dto->employeeId = $this->employeeRepository->getByUserIdAndCabinet(
            $dto->userId,
            $dto->cabinetId
        )->id;

        $this->repository->upsert(
            $dto->toUpdateArray()
        );
    }
}
