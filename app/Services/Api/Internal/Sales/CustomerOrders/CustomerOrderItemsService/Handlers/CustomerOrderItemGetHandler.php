<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers;

use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\DTO\CustomerOrderItemCalculateDTO;
use Illuminate\Support\Collection;

readonly class CustomerOrderItemGetHandler
{
    public function __construct(
        private CustomerOrderItemsRepositoryContract $customerOrderItemsRepository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->customerOrderItemsRepository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции заказа покупателя
     *
     * @param CustomerOrderItemCalculateDTO $dto
     * @return object
     */
    public function calculateMetrics(CustomerOrderItemCalculateDTO $dto): object
    {
        return $this->customerOrderItemsRepository->calculateMetricsForNewItem(
            $dto->productId,
            $dto->warehouseId,
            $dto->dateFrom,
            $dto->cabinetId
        );
    }
}
