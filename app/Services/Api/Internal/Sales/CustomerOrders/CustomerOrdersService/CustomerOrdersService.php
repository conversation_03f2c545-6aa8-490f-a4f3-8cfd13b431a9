<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Sales\CustomerOrdersServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrderBulkCopyHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrderBulkDeleteHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrderBulkHeldHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrderBulkReserveHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrderBulkUnheldHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrderBulkUnreserveHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrdersCreateHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrdersDeleteHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrdersGetHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrdersShowHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers\CustomerOrdersUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class CustomerOrdersService implements CustomerOrdersServiceContract
{
    public function __construct(
        private CustomerOrdersCreateHandler $createHandler,
        private CustomerOrdersGetHandler $getHandler,
        private CustomerOrdersShowHandler $showHandler,
        private CustomerOrdersUpdateHandler $updateHandler,
        private CustomerOrdersDeleteHandler $deleteHandler,
        private CustomerOrderBulkDeleteHandler $bulkDeleteHandler,
        private CustomerOrderBulkHeldHandler $bulkHeldHandler,
        private CustomerOrderBulkUnheldHandler $bulkUnheldHandler,
        private CustomerOrderBulkCopyHandler $bulkCopyHandler,
        private CustomerOrderBulkReserveHandler $bulkReserveHandler,
        private CustomerOrderBulkUnreserveHandler $bulkUnreserveHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws BindingResolutionException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }

    public function bulkHeld(array $ids): void
    {
        $this->bulkHeldHandler->run($ids);
    }

    public function bulkUnheld(array $ids): void
    {
        $this->bulkUnheldHandler->run($ids);
    }

    public function bulkReserve(array $ids): void
    {
        $this->bulkReserveHandler->run($ids);
    }
    public function bulkUnreserve(array $ids): void
    {
        $this->bulkUnreserveHandler->run($ids);
    }

    public function bulkCopy(array $ids): void
    {
        $this->bulkCopyHandler->run($ids);
    }
}
