<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;

readonly class CustomerOrderBulkUnreserveHandler
{
    public function __construct(
        private CustomerOrdersRepositoryContract $repository,
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService,
        private WarehouseReservationsRepositoryContract $reservationsRepository
    ) {
    }

    public function run(array $ids): void
    {
        // Получаем заказы для проверки ордерной схемы
        $orders = \DB::table('customer_orders')
            ->whereIn('id', $ids)
            ->get();

        // Снимаем флаг резервирования для всех заказов
        $this->repository->updateWhereIn(
            $ids,
            ['reserve' => false]
        );

        // Обрабатываем каждый заказ в зависимости от режима склада
        foreach ($orders as $order) {
            $isOrderSchemeActive = $this->orderSchemeDetectionService
                ->isOrderSchemeActiveForShipments($order->warehouse_id, $order->date_from);

            if ($isOrderSchemeActive) {
                // Логика для ордерной схемы - снимаем детальные резервы
                $this->processWithOrderScheme($order);
            }
            // Для обычной схемы ничего дополнительного не делаем - достаточно флага reserve=false
        }
    }

    /**
     * Снятие детальных резервов для ордерной схемы
     */
    private function processWithOrderScheme(object $order): void
    {
        // Получаем все резервы для данного заказа
        $reservations = \DB::table('warehouse_reservations as wr')
            ->join('customer_order_items as coi', 'wr.customer_order_item_id', '=', 'coi.id')
            ->where('coi.order_id', $order->id)
            ->where('wr.status', 'reserved')
            ->select('wr.*')
            ->get();

        foreach ($reservations as $reservation) {
            // Возвращаем количество в доступные остатки
            \DB::table('warehouse_items')
                ->where('id', $reservation->warehouse_item_id)
                ->decrement('reserved_quantity', $reservation->reserved_quantity);

            \DB::table('warehouse_items')
                ->where('id', $reservation->warehouse_item_id)
                ->increment('available_quantity', $reservation->reserved_quantity);

            // Отменяем резерв
            \DB::table('warehouse_reservations')
                ->where('id', $reservation->id)
                ->update([
                    'status' => 'cancelled',
                    'updated_at' => now()
                ]);
        }
    }
}
