<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\CustomerOrderDeliveryRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\DTO\CustomerOrderDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class CustomerOrdersUpdateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    private HasUpdateArrayDtoContract $dto;

    public function __construct(
        private readonly CustomerOrdersRepositoryContract $customerOrdersRepository,
        private readonly CustomerOrderDeliveryRepositoryContract $deliveryRepository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof CustomerOrderDTO) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }
        $this->dto = $dto;

        $resource = $this->customerOrdersRepository->show($this->dto->resourceId);

        $update = $this->dto->toUpdateArray();
        if ($resource->legal_entity_id !== $dto->legal_entity_id) {
            $documentNumberGenerator = new DocumentNumberGenerator(
                'customer_orders',
                $dto->cabinetId,
                $dto->number,
                $dto->legal_entity_id
            );

            $update['number'] = $documentNumberGenerator->generateNumber();
        }

        $this->customerOrdersRepository->update(
            $this->dto->resourceId,
            $update,
        );

        if (!empty($dto->deliveryInfo)) {
            $dataArray = $dto->toDeliveryArray($dto->resourceId);
            $this->deliveryRepository->upsert($dataArray);
        }

        $this->setRelationToFiles($dto->resourceId, $dto->files, 'customer_orders', true);
    }
}
