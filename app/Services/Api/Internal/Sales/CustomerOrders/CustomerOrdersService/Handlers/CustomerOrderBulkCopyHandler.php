<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;

class CustomerOrderBulkCopyHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CustomerOrdersRepositoryContract $customerOrdersRepository,
        private readonly DocumentsRepositoryContract $documentsRepository,
        private readonly CustomerOrderItemsRepositoryContract $customerOrderItemsRepository
    ) {
    }

    public function run(array $ids): string
    {
        $insertOrders = [];
        $insertDocuments = [];
        $insertItems = [];
        $items = $this->customerOrderItemsRepository->getWhereOrderInIds($ids);

        $orders = $this->customerOrdersRepository->getWhereInIds($ids)->toArray();

        foreach ($orders as $order) {
            if ($insertOrders) {
                $ghostOrder = $insertOrders[array_key_last($insertOrders)];
            }
            $documentNumberGenerator = new DocumentNumberGenerator(
                'customer_orders',
                $order->cabinet_id,
                null,
                $order->legal_entity_id,
                $ghostOrder ?? []
            );
            $orderNumber = $documentNumberGenerator->generateNumber();

            $oldOrderId = $order->id;
            $order->id = $this->generateUuid();
            $insertOrders[] = [
                'id' => $order->id,
                'cabinet_id' => $order->cabinet_id,
                'employee_id' => $order->employee_id,
                'department_id' => $order->department_id,
                'number' => $orderNumber,
                'date_from' => $order->date_from,
                'payment_status' => $order->payment_status,
                'status_id' => $order->status_id,
                'held' => false,
                'reserve' => $order->reserve,
                'legal_entity_id' => $order->legal_entity_id,
                'contractor_id' => $order->contractor_id,
                'plan_date' => $order->plan_date,
                'sales_channel_id' => $order->sales_channel_id,
                'warehouse_id' => $order->warehouse_id,
                'total_price' => $order->total_price,
                'comment' => $order->comment,
                'has_vat' => $order->has_vat ?? true,
                'price_includes_vat' => $order->price_includes_vat ?? true,
                'created_at' => Carbon::now()
            ];

            $items->where('order_id', $oldOrderId)->each(function ($item) use (&$order, &$insertItems) {
                $insertItems[] = [
                    'id' => $this->generateUuid(),
                    'order_id' => $order->id,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'vat_rate_id' => $item->vat_rate_id,
                    'discount' => $item->discount,
                    'currency_id' => $item->currency_id,
                    'price_in_currency' => $item->price_in_currency,
                    'currency_rate_to_base' => $item->currency_rate_to_base,
                    'price_in_base' => $item->price_in_base,
                    'amount_in_base' => $item->amount_in_base
                ];
            });

            $insertDocuments[] = [
                'documentable_id' => $order->id,
                'documentable_type' => 'customer_orders',
                'lft' => 1,
                'rgt' => 2,
                'parent_id' => null,
                'tree_id' => $this->generateUuid(),
                'cabinet_id' => $order->cabinet_id
            ];
        }

        $this->customerOrdersRepository->insert($insertOrders);
        if ($insertItems) {
            $this->customerOrderItemsRepository->insert($insertItems);
        }
        $this->documentsRepository->insert($insertDocuments);

        return 'OK!';
    }
}
