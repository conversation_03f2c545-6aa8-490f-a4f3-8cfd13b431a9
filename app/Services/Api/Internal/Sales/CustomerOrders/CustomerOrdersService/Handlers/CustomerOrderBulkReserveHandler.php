<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;

readonly class CustomerOrderBulkReserveHandler
{
    public function __construct(
        private CustomerOrdersRepositoryContract $repository,
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService,
        private WarehouseReservationsRepositoryContract $reservationsRepository
    ) {
    }

    public function run(array $ids): void
    {
        // Получаем заказы для проверки ордерной схемы
        $orders = \DB::table('customer_orders')
            ->whereIn('id', $ids)
            ->get();

        // Устанавливаем флаг резервирования для всех заказов
        $this->repository->updateWhereIn(
            $ids,
            ['reserve' => true]
        );

        // Обрабатываем каждый заказ в зависимости от режима склада
        foreach ($orders as $order) {
            $isOrderSchemeActive = $this->orderSchemeDetectionService
                ->isOrderSchemeActiveForShipments($order->warehouse_id, $order->date_from);

            if ($isOrderSchemeActive) {
                // Логика для ордерной схемы - создаем детальные резервы
                $this->processWithOrderScheme($order);
            }
            // Для обычной схемы ничего дополнительного не делаем - достаточно флага reserve=true
        }
    }

    /**
     * Создание детальных резервов для ордерной схемы
     */
    private function processWithOrderScheme(object $order): void
    {
        // Получаем позиции заказа
        $orderItems = \DB::table('customer_order_items')
            ->where('order_id', $order->id)
            ->get();

        foreach ($orderItems as $item) {
            // Получаем доступные партии товара на складе (FIFO)
            $warehouseItems = \DB::table('warehouse_items')
                ->where('product_id', $item->product_id)
                ->where('warehouse_id', $order->warehouse_id)
                ->where('received_at', '<=', $order->date_from)
                ->where('status', '!=', 'out_of_stock')
                ->whereRaw('available_quantity > 0')
                ->orderBy('received_at', 'asc')
                ->get();

            $remainingQuantity = $item->quantity;

            foreach ($warehouseItems as $warehouseItem) {
                if ($remainingQuantity <= 0) {
                    break;
                }

                $availableQuantity = $warehouseItem->available_quantity ?? $warehouseItem->quantity;
                $reserveQuantity = min($remainingQuantity, $availableQuantity);

                if ($reserveQuantity > 0) {
                    // Создаем детальный резерв
                    $this->reservationsRepository->insert([
                        'id' => \Illuminate\Support\Str::uuid()->toString(),
                        'warehouse_item_id' => $warehouseItem->id,
                        'customer_order_item_id' => $item->id,
                        'reserved_quantity' => $reserveQuantity,
                        'reserved_at' => now(),
                        'status' => 'reserved',
                        'reservation_type' => 'order',
                        'document_type' => 'customer_order',
                        'document_id' => $order->id,
                        'priority' => 5, // Средний приоритет для обычных заказов
                        'expires_at' => now()->addDays(30), // 30 дней по умолчанию
                        'auto_release' => true,
                    ]);

                    // Обновляем доступное количество в warehouse_item
                    \DB::table('warehouse_items')
                        ->where('id', $warehouseItem->id)
                        ->increment('reserved_quantity', $reserveQuantity);

                    \DB::table('warehouse_items')
                        ->where('id', $warehouseItem->id)
                        ->decrement('available_quantity', $reserveQuantity);

                    $remainingQuantity -= $reserveQuantity;
                }
            }

            // Если не удалось зарезервировать полное количество, логируем предупреждение
            if ($remainingQuantity > 0) {
                \Log::warning("Не удалось полностью зарезервировать товар", [
                    'order_id' => $order->id,
                    'product_id' => $item->product_id,
                    'requested_quantity' => $item->quantity,
                    'unreserved_quantity' => $remainingQuantity
                ]);
            }
        }
    }
}
