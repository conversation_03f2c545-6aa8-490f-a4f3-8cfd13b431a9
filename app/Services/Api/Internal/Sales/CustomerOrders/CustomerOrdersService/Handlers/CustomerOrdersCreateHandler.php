<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\CustomerOrderDeliveryRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\DTO\CustomerOrderDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class CustomerOrdersCreateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    private string $resourceId;

    public function __construct(
        private readonly CustomerOrdersRepositoryContract $customerOrdersRepository,
        private readonly CustomerOrderDeliveryRepositoryContract $customerOrderDeliveryRepository,
        private readonly DocumentsRepositoryContract $documentsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof CustomerOrderDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $documentNumberGenerator = new DocumentNumberGenerator(
            'customer_orders',
            $dto->cabinetId,
            $dto->number,
            $dto->legal_entity_id
        );
        $dto->number = $documentNumberGenerator->generateNumber();

        $this->customerOrdersRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        if (!empty($dto->deliveryInfo)) {
            $dataArray = $dto->toDeliveryArray($this->resourceId);
            $this->customerOrderDeliveryRepository->upsert($dataArray);
        }

        $this->documentsRepository->insert(
            [
                'documentable_id' => $this->resourceId,
                'documentable_type' => 'customer_orders',
                'lft' => 1,
                'rgt' => 2,
                'parent_id' => null,
                'tree_id' => $this->generateUuid(),
                'cabinet_id' => $dto->cabinetId
            ]
        );

        $this->setRelationToFiles($this->resourceId, $dto->files, 'customer_orders');

        return $this->resourceId;
    }
}
