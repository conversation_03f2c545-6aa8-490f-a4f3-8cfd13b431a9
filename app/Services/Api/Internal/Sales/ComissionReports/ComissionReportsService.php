<?php

namespace App\Services\Api\Internal\Sales\ComissionReports;

use App\Contracts\Repositories\ComissionReportsRepositoryContract;
use App\Contracts\Services\Internal\Sales\ComissionReportsServiceContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class ComissionReportsService implements ComissionReportsServiceContract
{
    public function __construct(
        private ComissionReportsRepositoryContract $repository
    )
    {
    }

    public function index(IndexRequestDTO $DTO): Collection
    {
        return $this->repository->get(
            id: $DTO->id,
            filters: $DTO->filters,
            fields: $DTO->fields,
            sortField: $DTO->sortField,
            sortDirection: $DTO->sortDirection,
            page: $DTO->page,
            perPage: $DTO->perPage
        );
    }
}
