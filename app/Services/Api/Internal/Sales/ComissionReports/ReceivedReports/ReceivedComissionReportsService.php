<?php

namespace App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\ReceivedComissionReportsRepositoryContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\DTO\ReceivedComissionReportDTO;
use App\Traits\HasCabinetCurrency;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use RuntimeException;

readonly class ReceivedComissionReportsService implements ReceivedComissionReportServiceContract
{
    use HasOrderedUuid;
    use HasFiles;
    use HasCabinetCurrency;

    public function __construct(
        private ReceivedComissionReportsRepositoryContract $repository,
        private DocumentsRepositoryContract $documentsRepository
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->repository->get(
            id: $data->id,
            filters: $data->filters,
            fields: $data->fields,
            sortField: $data->sortField,
            sortDirection: $data->sortDirection,
            page: $data->page,
            perPage: $data->perPage
        );
    }

    /**
     * @throws \JsonException
     */
    public function show(string $id): ?object
    {
        $result = $this->repository->show($id);

        if ($result) {
            $result->files = json_decode($result->files, true, 512, JSON_THROW_ON_ERROR);
            if ($result->files) {
                $this->generateUrls($result->files);
            }
            $result->realized_items = json_decode($result->realized_items, true, 512, JSON_THROW_ON_ERROR);
            $result->return_items = json_decode($result->return_items, true, 512, JSON_THROW_ON_ERROR);
            $result->legal_entity = json_decode($result->legal_entity, true, 512, JSON_THROW_ON_ERROR);
            $result->contractor = json_decode($result->contractor, true, 512, JSON_THROW_ON_ERROR);
            $result->contract = json_decode($result->contract, true, 512, JSON_THROW_ON_ERROR);
            $result->status = json_decode($result->status, true, 512, JSON_THROW_ON_ERROR);
        }

        return $result;
    }

    /**
     * @throws NotFoundException
     */
    public function create(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ReceivedComissionReportDTO) {
            throw new InvalidArgumentException('Invalid DTO type');
        }

        $documentNumberGenerator = new DocumentNumberGenerator(
            'issued_comission_reports',
            $dto->cabinetId,
            $dto->number,
            $dto->legalEntityId
        );
        $dto->number = $documentNumberGenerator->generateNumber();

        if (!$dto->cabinetCurrencyId) {
            $currency = $this->getAccountingCurrency($dto->cabinetId);
            if (!$currency) {
                throw new RuntimeException('Cabinet currency not found');
            }

            $dto->cabinetCurrencyId = $currency->id;
        }

        $id = $this->generateUuid();
        $this->repository->insert($dto->toInsertArray($id));

        $this->documentsRepository->insert(
            [
                'documentable_id' => $id,
                'documentable_type' => 'received_comission_reports',
                'lft' => 1,
                'rgt' => 2,
                'parent_id' => null,
                'tree_id' => $this->generateUuid(),
                'cabinet_id' => $dto->cabinetId
            ]
        );

        $this->setRelationToFiles($id, $dto->files, 'received_comission_reports');

        return $id;
    }

    /**
     * @throws NotFoundException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ReceivedComissionReportDTO) {
            throw new InvalidArgumentException('Invalid DTO type');
        }

        $resource = $this->repository->findFirst($dto->id);

        if (!$resource) {
            throw new NotFoundException('Issued comission report not found in service');
        }

        $update = $dto->toUpdateArray();
        if ($resource->legal_entity_id !== $dto->legalEntityId) {
            $documentNumberGenerator = new DocumentNumberGenerator(
                'received_comission_reports',
                $dto->cabinetId,
                $dto->number,
                $dto->legalEntityId
            );
            $update['number'] = $documentNumberGenerator->generateNumber();
        }

        $this->repository->update(
            $dto->id,
            $update,
        );

        $this->setRelationToFiles($dto->id, $dto->files, 'received_comission_reports', true);
    }

    public function delete(string $id): void
    {
        $this->repository->delete($id);
    }
}
