<?php

namespace App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ReceivedComissionReportsReturnItemsRepositoryContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportReturnItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Helpers\ReceivedReportCalculatorHelper;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\DTO\ReturnItemDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use Illuminate\Support\Collection;
use InvalidArgumentException;

class ReceivedComissionReportsReturnItemsService implements ReceivedComissionReportReturnItemsServiceContract
{
    use HasOrderedUuid;
    use HasFiles;
    use PrecisionCalculator;

    public function __construct(
        private readonly ReceivedComissionReportsReturnItemsRepositoryContract $repository
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->repository->get(
            id: $data->id,
            filters: $data->filters,
            fields: $data->fields,
            sortField: $data->sortField,
            sortDirection: $data->sortDirection,
            page: $data->page,
            perPage: $data->perPage
        );
    }

    public function show(string $id): ?object
    {
        return $this->repository->show($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ReturnItemDTO) {
            throw new InvalidArgumentException('Invalid DTO type');
        }

        $dto->summaryPrice = $this->multiply(
            $dto->price,
            $dto->quantity
        );

        $id = $this->generateUuid();
        $this->repository->insert($dto->toInsertArray($id));

        ReceivedReportCalculatorHelper::recalculateSumInReport($dto->reportId);

        return $id;
    }

    /**
     * @throws NotFoundException
     */
    public function update(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ReturnItemDTO) {
            throw new InvalidArgumentException('Invalid DTO type');
        }

        $dto->summaryPrice = $this->multiply(
            $dto->price,
            $dto->quantity
        );

        $item = $this->repository->show($dto->id);

        if (!$item) {
            throw new NotFoundException('Item not found');
        }

        $this->repository->update(
            $dto->id,
            $dto->toUpdateArray()
        );

        ReceivedReportCalculatorHelper::recalculateSumInReport($item->report_id);
    }


    /**
     * @throws NotFoundException
     */
    public function delete(string $id): void
    {
        $item = $this->repository->show($id);

        if (!$item) {
            throw new NotFoundException('Item not found');
        }

        $this->repository->delete($id);

        ReceivedReportCalculatorHelper::recalculateSumInReport($item->report_id);
    }
}
