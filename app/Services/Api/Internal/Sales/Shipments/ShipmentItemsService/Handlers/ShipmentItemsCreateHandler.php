<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use InvalidArgumentException;
use RuntimeException;

class ShipmentItemsCreateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;

    private string $resourceId;

    public function __construct(
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private readonly ShipmentsRepositoryContract $shipmentsRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws Exception
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ShipmentItemDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $clone = $this->shipmentItemsRepository
            ->checkItemExists($dto->productId, $dto->shipmentId);

        if ($clone) {
            throw new RuntimeException('Item already exists');
        }

        // Получаем отгрузку для расчета НДС
        $shipment = $this->shipmentsRepository->show($dto->shipmentId);
        if (!$shipment) {
            throw new RuntimeException('Shipment not found');
        }

        // Рассчитываем total_price, total_cost, profit и total_vat_sum
        $calculatedData = $this->calculateShipmentItemTotals($dto, $shipment);

        // Устанавливаем рассчитанные значения в DTO
        $dto->totalCost = $calculatedData['total_cost'];
        $dto->profit = $calculatedData['profit'];
        $dto->totalVatSum = $calculatedData['total_vat_sum'];

        $this->shipmentItemsRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        DB::table('shipments')
            ->where('id', $dto->shipmentId)
            ->update([
                'total_price' => DB::raw("CAST(COALESCE(total_price, '0') AS NUMERIC) + " . $calculatedData['total_price'])
            ]);

        Queue::push(new HandleFifoJob($this->resourceId));

        return $this->resourceId;
    }

    private function calculateShipmentItemTotals(ShipmentItemDTO $dto, object $shipment): array
    {
        // Получаем ставку НДС
        $vatRate = '0';
        if ($dto->vat_rate_id) {
            $vatRateEntity = $this->vatRatesRepository->show($dto->vat_rate_id);
            $vatRate = $vatRateEntity ? (string)$vatRateEntity->rate : '0';
        }

        // Рассчитываем общую стоимость позиции
        $totalPrice = $this->calculateSumPrice([
            'price' => $dto->price,
            'quantity' => (string)$dto->quantity,
            'discount' => $dto->discount,
        ]);

        // Рассчитываем общую себестоимость
        $totalCost = $this->multiply($dto->cost, (string)$dto->quantity);

        // Рассчитываем прибыль
        $profit = $this->subtract($totalPrice, $totalCost);

        // Рассчитываем НДС в зависимости от настроек отгрузки
        $totalVatSum = '0';
        if ($shipment->price_includes_vat && $this->compare($vatRate, '0') > 0) {
            // НДС включен в цену
            $vatCalculation = $this->extractVat($totalPrice, $vatRate);
            $totalVatSum = $vatCalculation['vat_amount'];
        } elseif (!$shipment->price_includes_vat && $this->compare($vatRate, '0') > 0) {
            // НДС сверх цены
            $vatCalculation = $this->calculateVat($totalPrice, $vatRate);
            $totalPrice = $vatCalculation['total_amount'];
            $totalVatSum = $vatCalculation['vat_amount'];
        }

        return [
            'total_price' => $totalPrice,
            'total_cost' => $totalCost,
            'profit' => $profit,
            'total_vat_sum' => $totalVatSum
        ];
    }

    private function calculateSumPrice(array $item): string
    {
        $price = $this->toStringNumber($item['price']);
        $quantity = $this->toStringNumber($item['quantity']);
        $discount = $this->toStringNumber($item['discount'] ?? '0');

        $totalPrice = $this->multiply($price, $quantity);

        if ($this->compare($discount, '0') > 0) {
            $totalPrice = $this->applyDiscount($totalPrice, $discount);
        }

        return $totalPrice;
    }
}
