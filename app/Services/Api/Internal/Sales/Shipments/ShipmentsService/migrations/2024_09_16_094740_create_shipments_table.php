<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_common')->default(false);

            $table->string('payment_status')->default('unpaid');
            $table->string('number')->nullable();
            $table->dateTime('date_from');

            $table->foreignUuid('status_id')->nullable()->constrained();
            $table->boolean('held')->default(true); //Проведено

            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('contractor_id')->constrained();

            $table->foreignUuid('warehouse_id')->constrained();

            $table->foreignUuid('sales_channel_id')->nullable()->constrained();


            //TODO Договор + проект
            //$table->foreignUuid('agreement_id')->nullable()->constrained()->nullOnDelete();
            //$table->foreignUuid('project_id')->nullable()->constrained();

            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');
            $table->string('currency_value')->default(1);

            //Доп поля
            $table->foreignUuid('consignee_id')->nullable()->references('id')->on('contractors');
            $table->foreignUuid('transporter_id')->nullable()->references('id')->on('contractors');
            $table->string('cargo_name')->nullable();
            $table->text('shipper_instructions')->nullable();
            $table->string('venicle')->nullable();
            $table->string('venicle_number')->nullable();
            $table->integer('total_seats')->nullable();
            $table->string('goverment_contract_id')->nullable();

            $table->text('comment')->nullable();
            $table->boolean('price_includes_vat')->default(true);

            $table->string('overhead_cost')->default('0');

            $table->string('total_cost')->default('0');
            $table->string('profit')->default('0');
            $table->string('total_price')->default('0');
        });

        Schema::create('shipment_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('shipment_id')->references('id')->on('shipments')->cascadeOnDelete();
            $table->foreignUuid('product_id')->references('id')->on('products');
            $table->unsignedBigInteger('quantity');

            $table->string('price')->default('0'); //стоимость
            $table->string('cost')->default('0'); //себестоимость
            $table->string('total_cost')->default('0'); //себестоимость позиции
            $table->string('total_price')->default('0'); //сумма позиции
            $table->string('profit')->default('0'); //прибыль позиции
            $table->string('total_vat_sum')->default(0);  // сумма НДС

            $table->foreignUuid('vat_rate_id')->nullable()->references('id')->on('vat_rates');
            $table->string('discount')->default('0');


        });

        //TODO Связанные документы
        /*Schema::create('vendor_order_documents', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->foreignUuid('order_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('document_id')->constrained()->cascadeOnDelete();
        });*/

        //TODO Задачи
        /*Schema::create('vendor_order_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('order_id')->constrained()->cascadeOnDelete();
            $table->text('description')->nullable();
            $table->boolean('is_done')->default(false);
            $table->foreignUuid('employee_id')->constrained();
            $table->date('deadline')->nullable();
            $table->string('type')->nullable();

            $table->foreignUuid('contractor_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('document_id')->nullable()->constrained()->nullOnDelete();
        });

        Schema::create('vendor_order_task_comments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('vendor_order_task_id')->constrained()->cascadeOnDelete();
            $table->text('text');
            $table->foreignUuid('employee_id')->constrained();
        });*/

        Schema::create('shipment_delivery_infos', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('shipment_id')->unique()->references('id')->on('shipments')->cascadeOnDelete();
            $table->text('comment')->nullable();
            $table->string('post_code')->nullable();
            $table->string('country')->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->string('street')->nullable();
            $table->string('house')->nullable();
            $table->string('office')->nullable();
            $table->string('other')->nullable();

            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipment_items');
        Schema::dropIfExists('shipment_delivery_infos');
        Schema::dropIfExists('shipment_files');
        Schema::dropIfExists('shipments');
        Schema::dropIfExists('shipment_statuses');
    }
};
