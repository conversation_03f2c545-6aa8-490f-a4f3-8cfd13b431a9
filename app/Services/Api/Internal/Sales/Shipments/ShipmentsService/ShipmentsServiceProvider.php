<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService;

use Illuminate\Support\ServiceProvider;

class ShipmentsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $migrationsPath = __DIR__ . '/migrations';
        $this->loadMigrationsFrom($migrationsPath);
    }
}
