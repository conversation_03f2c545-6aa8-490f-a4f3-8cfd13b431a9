<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Jobs\FIFOJobs\HandleFifoJob;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Bus;

readonly class ShipmentsBulkDeleteHandler
{
    public function __construct(
        private ShipmentsRepositoryContract $shipmentsRepository,
        private ShipmentItemsRepositoryContract $shipmentItemsRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     * @throws \Throwable
     */
    public function run(array $resourceIds): void
    {
        $shipmentItems = $this->shipmentItemsRepository->getByShipmentIds($resourceIds);
        Bus::batch([new HandleFifoJob($shipmentItems, true)])
            ->finally(function () use ($resourceIds) {
                $this->shipmentsRepository->deleteWhereIn($resourceIds);
            })
            ->dispatch();
    }
}
