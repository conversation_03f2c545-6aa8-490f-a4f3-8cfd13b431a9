<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

readonly class ShipmentsBulkHeldHandler
{
    public function __construct(
        private ShipmentsRepositoryContract $shipmentsRepository,
        private ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeDetectionService,
        private WarehouseIssueOrdersRepositoryContract $issueOrdersRepository,
        private WarehouseTransactionServiceContract $transactionService
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(array $resourceIds): void
    {
        // Получаем информацию об отгрузках
        $shipments = DB::table('shipments')
            ->whereIn('id', $resourceIds)
            ->get();

        // Проводим отгрузки
        DB::table('shipments')
            ->whereIn('id', $resourceIds)
            ->update([
                'held' => true,
                'updated_at' => Carbon::now()
            ]);

        $shipmentItems = DB::table('shipment_items')
            ->join('shipments', 'shipments.id', '=', 'shipment_items.shipment_id')
            ->whereIn('shipment_id', $resourceIds)
            ->select([
                'shipment_items.id as shipment_item_id',
                'shipments.cabinet_id as cabinet_id',
                'shipments.warehouse_id as warehouse_id',
                'shipments.date_from as date_from',
                'shipment_items.product_id as product_id',
                'shipment_items.quantity as quantity',
                'shipment_items.price as price'
            ])
            ->get();

        if ($shipmentItems->isNotEmpty()) {
            // Группируем по складам для проверки ордерной схемы
            $itemsByWarehouse = $shipmentItems->groupBy('warehouse_id');

            foreach ($itemsByWarehouse as $warehouseId => $items) {
                $firstItem = $items->first();
                $isOrderSchemeActive = $this->orderSchemeDetectionService
                    ->isOrderSchemeActiveForShipments($warehouseId, $firstItem->date_from);

                if ($isOrderSchemeActive) {
                    // Логика для ордерной схемы
                    $this->processWithOrderScheme($items, $warehouseId);
                } else {
                    // Обычная логика
                    $this->processStandard($items);
                }
            }
        }
    }

    /**
     * Обработка в обычной схеме - стандартная FIFO логика
     */
    private function processStandard($items): void
    {
        // Используем BulkHandleFifoJob для оптимизированного пересчета
        Queue::push(new BulkHandleFifoJob($items));
    }

    /**
     * Обработка в ордерной схеме - создание расходного ордера
     */
    private function processWithOrderScheme($items, string $warehouseId): void
    {
        $firstItem = $items->first();

        // Создаем расходный ордер для отгрузки
        $issueOrderId = $this->generateUuid();

        $this->issueOrdersRepository->insert([
            'id' => $issueOrderId,
            'cabinet_id' => $firstItem->cabinet_id,
            'employee_id' => null, // TODO: получить из контекста пользователя
            'department_id' => null, // TODO: получить из настроек
            'warehouse_id' => $warehouseId,
            'number' => 'IO-' . date('YmdHis') . '-' . substr($issueOrderId, 0, 8),
            'date_from' => $firstItem->date_from,
            'held' => true, // Автоматически проводим
            'document_basis_type' => 'shipment',
            'document_basis_id' => $firstItem->shipment_id ?? null,
            'write_off_reason' => 'shipment',
            'reason_description' => 'Автоматическое списание при отгрузке',
            'total_quantity' => $items->sum('quantity'),
            'total_cost' => $items->sum(fn ($item) => $item->quantity * $item->price),
        ]);

        // Создаем транзакции для каждой позиции
        foreach ($items as $item) {
            $this->transactionService->createTransaction([
                'warehouse_id' => $warehouseId,
                'product_id' => $item->product_id,
                'quantity' => -$item->quantity, // Отрицательное количество для расхода
                'document_type' => 'warehouse_issue_order',
                'document_id' => $issueOrderId,
                'operation_type' => 'issue',
                'cost_per_unit' => $item->price,
            ]);
        }

        // Запускаем стандартную FIFO логику
        Queue::push(new BulkHandleFifoJob($items));
    }

    private function generateUuid(): string
    {
        return \Illuminate\Support\Str::uuid()->toString();
    }
}
