<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class ShipmentsGetHandler
{
    public function __construct(
        private ShipmentsRepositoryContract $shipmentsRepository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->shipmentsRepository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
