<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Finances\IncomingPaymentItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers\IncomingPaymentItemsCreateHandler;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers\IncomingPaymentItemsDeleteHandler;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers\IncomingPaymentItemsGetHandler;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers\IncomingPaymentItemsShowHandler;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers\IncomingPaymentItemsUpdateHandler;
use Illuminate\Support\Collection;

readonly class IncomingPaymentItemsService implements IncomingPaymentItemsServiceContract
{
    public function __construct(
        private IncomingPaymentItemsCreateHandler $createHandler,
        private IncomingPaymentItemsGetHandler $getHandler,
        private IncomingPaymentItemsShowHandler $showHandler,
        private IncomingPaymentItemsUpdateHandler $updateHandler,
        private IncomingPaymentItemsDeleteHandler $deleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
