<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers;

use App\Contracts\Repositories\IncomingPaymentItemsRepositoryContract;

readonly class IncomingPaymentItemsShowHandler
{
    public function __construct(
        private IncomingPaymentItemsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
