<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\IncomingPaymentItemsRepositoryContract;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\DTO\IncomingPaymentItemDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class IncomingPaymentItemsCreateHandler
{
    use HasOrderedUuid;

    public string $resourceId;

    public function __construct(
        private IncomingPaymentItemsRepositoryContract $repository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if ($dto instanceof IncomingPaymentItemDTO) {

            $this->repository->insert(
                $dto->toInsertArray($this->resourceId)
            );

            return $this->resourceId;

        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
