<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\DTO\IncomingPaymentDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class IncomingPaymentsCreateHandler
{
    use HasOrderedUuid;

    public string $resourceId;

    public function __construct(
        private IncomingPaymentsRepositoryContract $repository,
        private DocumentsRepositoryContract $documentsRepository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if ($dto instanceof IncomingPaymentDTO) {

            $documentNumberGenerator = new DocumentNumberGenerator(
                'incoming_payments',
                $dto->cabinet_id,
                $dto->number,
                $dto->legal_entity_id
            );
            $dto->number = $documentNumberGenerator->generateNumber();
            $this->repository->insert(
                $dto->toInsertArray($this->resourceId, $dto->employee_id)
            );

            $this->documentsRepository->insert(
                [
                    'documentable_id' => $this->resourceId,
                    'documentable_type' => 'incoming_payments',
                    'lft' => 1,
                    'rgt' => 2,
                    'parent_id' => null,
                    'tree_id' => $this->generateUuid(),
                    'cabinet_id' => $dto->cabinet_id
                ]
            );

            return $this->resourceId;

        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
