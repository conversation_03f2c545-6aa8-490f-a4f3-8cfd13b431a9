<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\Handlers;

use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;

readonly class IncomingPaymentsDeleteHandler
{
    public function __construct(
        private IncomingPaymentsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
