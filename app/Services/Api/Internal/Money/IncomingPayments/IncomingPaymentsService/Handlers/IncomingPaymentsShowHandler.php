<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\Handlers;

use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;

readonly class IncomingPaymentsShowHandler
{
    public function __construct(
        private IncomingPaymentsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
