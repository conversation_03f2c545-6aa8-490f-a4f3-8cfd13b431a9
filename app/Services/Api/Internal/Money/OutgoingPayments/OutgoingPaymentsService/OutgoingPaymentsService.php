<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Finances\OutgoingPaymentsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers\OutgoingPaymentsCreateHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers\OutgoingPaymentsDeleteHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers\OutgoingPaymentsGetHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers\OutgoingPaymentsShowHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers\OutgoingPaymentsUpdateHandler;
use Illuminate\Support\Collection;

readonly class OutgoingPaymentsService implements OutgoingPaymentsServiceContract
{
    public function __construct(
        private OutgoingPaymentsCreateHandler $createHandler,
        private OutgoingPaymentsGetHandler $getHandler,
        private OutgoingPaymentsShowHandler $showHandler,
        private OutgoingPaymentsUpdateHandler $updateHandler,
        private OutgoingPaymentsDeleteHandler $deleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
