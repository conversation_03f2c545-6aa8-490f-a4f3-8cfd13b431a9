<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers;

use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;
use Illuminate\Support\Collection;

readonly class OutgoingPaymentsGetHandler
{
    public function __construct(
        private OutgoingPaymentsRepositoryContract $repository
    ) {
    }

    public function run(array $data): Collection
    {
        $cabinetId = $data['cabinet_id'] ?? null;
        return $this->repository->get($cabinetId);
    }
}
