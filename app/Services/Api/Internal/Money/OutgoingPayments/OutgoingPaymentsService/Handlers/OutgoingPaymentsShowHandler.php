<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers;

use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;
use App\Traits\HasFiles;

readonly class OutgoingPaymentsShowHandler
{
    use HasFiles;
    public function __construct(
        private OutgoingPaymentsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $result = $this->repository->show($resourceId);

        $result->files = json_decode($result->files, true);
        if ($result->files) {
            $this->generateUrls($result->files);
        }

        return $result;
    }
}
