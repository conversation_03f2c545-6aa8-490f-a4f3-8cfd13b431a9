<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\Handlers;

use App\Contracts\Repositories\OutgoingPaymentItemsRepositoryContract;
use Illuminate\Support\Collection;

readonly class OutgoingPaymentItemsGetHandler
{
    public function __construct(
        private OutgoingPaymentItemsRepositoryContract $repository
    ) {
    }

    public function run(array $data): Collection
    {
        $cabinetId = $data['outgoing_payment_id'] ?? null;
        return $this->repository->get($cabinetId);
    }
}
