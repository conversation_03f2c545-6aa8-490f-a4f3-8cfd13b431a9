<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class OutgoingPaymentItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $outgoing_payment_id,
        public ?string $document_id,
        public string $paid_in = '0',
        public ?string $resourceId = null
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,

            'outgoing_payment_id' => $this->outgoing_payment_id,
            'document_id' => $this->document_id,
            'paid_in' => $this->paid_in,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'paid_in' => $this->paid_in,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            outgoing_payment_id: $data['outgoing_payment_id'] ?? null,
            document_id: $data['document_id'] ?? null,
            paid_in: $data['paid_in'] ?? 0,
            resourceId: $data['id'] ?? null
        );
    }
}
