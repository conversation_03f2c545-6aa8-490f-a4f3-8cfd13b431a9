<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonWarehousesService\Handlers;

use App\Contracts\Repositories\OzonWarehousesRepositoryContract;

class OzonWarehousesShowHandler
{
    public function __construct(
        private readonly OzonWarehousesRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
