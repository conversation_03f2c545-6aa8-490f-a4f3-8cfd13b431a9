<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV1ReturnsFboFbsListService\Handlers;

use App\Contracts\Repositories\OzonV1ReturnsFboFbsListRepositoryContract;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class OzonApiReturnsChunksHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        private readonly OzonV1ReturnsFboFbsListRepositoryContract $repository,
    ) {
    }

    public function run(Collection $returns, int $limit, string $cabinetId, string $departmentId, string $employeeId, string $ozonCompanyId): void
    {
        $orderNumberChunks = $returns->chunk($limit);

        $orderNumberChunks->each(function ($chunk) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId) {
            $orders = [];

            $existingReturns = $this->repository->getReturnsByReturnIds($chunk->pluck('id')->toArray())->toArray();

            $chunk->each(function ($item) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId, &$orders, &$existingReturns) {
                $orderId = $existingReturns[$item['id']] ?? $this->generateUuid();

                $orders[] = $this->prepareOrderData($item, $orderId, $cabinetId, $departmentId, $employeeId, $ozonCompanyId);
            });

            $this->saveProcessedData($orders);
        });
    }


    private function prepareOrderData($item, string $orderId, string $cabinetId, string $departmentId, string $employeeId, string $ozonCompanyId): array
    {
        return [

            'id' => $orderId,
            'cabinet_id' => $cabinetId,
            'department_id' => $departmentId,
            'employee_id' => $employeeId,
            'return_id' => $item['id'],
            'ozon_company_id' => $ozonCompanyId,
            'return_reason_name' => $item['return_reason_name'],
            'type' => $item['type'],
            'schema' => $item['schema'],
            'order_id' => $item['order_id'],
            'order_number' => $item['order_number'],
            'place_id' => $item['place']['id'],
            'place_name' => $item['place']['name'],
            'place_address' => $item['place']['address'],
            'target_place_id' => $item['target_place']['id'],
            'target_place_name' => $item['target_place']['name'],
            'target_place_address' => $item['target_place']['address'],
            'storage_sum_currency_code' => $item['storage']['sum']['currency_code'],
            'storage_sum_price' => $this->convertToKopecks($item['storage']['sum']['price'] ?? null),
            'storage_tariffication_first_date' => $this->formatDate($item['storage']['tariffication_first_date'] ?? null),
            'storage_tariffication_start_date' => $this->formatDate($item['storage']['tariffication_start_date'] ?? null),
            'storage_arrived_moment' => $this->formatDate($item['storage']['arrived_moment'] ?? null),
            'storage_days' => $item['storage']['days'],
            'storage_utilization_sum_currency_code' => $item['storage']['utilization_sum']['currency_code'],
            'storage_utilization_sum_price' => $this->convertToKopecks($item['storage']['utilization_sum']['price'] ?? null),
            'storage_utilization_forecast_date' => $this->formatDate($item['storage']['utilization_forecast_date'] ?? null),
            'product_sku' => $item['product']['sku'],
            'product_offer_id' => $item['product']['offer_id'],
            'product_name' => $item['product']['name'],
            'product_price_currency_code' => $item['product']['price']['currency_code'],
            'product_price_price' => $this->convertToKopecks($item['product']['price']['price'] ?? null),
            'product_price_without_commission_currency_code' => $item['product']['price_without_commission']['currency_code'],
            'product_price_without_commission_price' => $this->convertToKopecks($item['product']['price_without_commission']['price'] ?? null),
            'product_commission_percent' => $item['product']['commission_percent'],
            'product_commission_currency_code' => $item['product']['commission']['currency_code'],
            'product_commission_price' => $this->convertToKopecks($item['product']['commission']['price'] ?? null),
            'product_quantity' => $item['product']['quantity'],
            'logistic_technical_return_moment' => $this->formatDate($item['logistic']['technical_return_moment'] ?? null),
            'logistic_final_moment' => $this->formatDate($item['logistic']['final_moment'] ?? null),
            'logistic_cancelled_with_compensation_moment' => $this->formatDate($item['logistic']['cancelled_with_compensation_moment'] ?? null),
            'logistic_return_date' => $this->formatDate($item['logistic']['return_date'] ?? null),
            'logistic_barcode' => $item['logistic']['barcode'],
            'visual_status_id' => $item['visual']['status']['id'],
            'visual_status_display_name' => $item['visual']['status']['display_name'],
            'visual_status_sys_name' => $item['visual']['status']['sys_name'],
            'visual_change_moment' => $this->formatDate($item['visual']['change_moment'] ?? null),
            'exemplars_id' => $item['exemplars'][0]['id'],
            'additional_info_is_opened' => $item['additional_info']['is_opened'],
            'additional_info_is_super_econom' => $item['additional_info']['is_super_econom'],
            'clearing_id' => $item['clearing_id'],
            'posting_number' => $item['posting_number'],
            'return_clearing_id' => $item['return_clearing_id'],
            'source_id' => $item['source_id'],

            'created_at' => now(),
            'updated_at' => now(),

        ];
    }

    private function saveProcessedData(array $orders): void
    {
        if (!empty($orders)) {
            $this->repository->upsert($orders);
        }

    }


    private function formatDate(?string $date): ?string
    {
        return $date ? Carbon::parse($date)->format('Y-m-d H:i:s') : null;
    }

    private function convertToKopecks(?int $price): ?int
    {
        return isset($price) ? $this->rublesInKopeck($price) : null;
    }

}
