<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\Handlers;

use App\Helpers\OzonApiRequestHelper;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\DTO\OzonV3FinanceTransactionListDTO;
use Carbon\Carbon;

class OzonApiRequestHandler
{
    public function run(OzonV3FinanceTransactionListDTO $dto, string $clientId, string $apiKey, int $page, int $pageSize): array
    {

        $data = [
            'filter' => [
                'date' => [
                    'from' => Carbon::parse($dto->filter['date']['from'])->format('Y-m-d\TH:i:s.v\Z'),
                    'to' => Carbon::parse($dto->filter['date']['to'])->format('Y-m-d\TH:i:s.v\Z')
                ],
                "operation_type" => $dto->filter['operation_type'] ?? [],
                'posting_number' => $dto->filter['posting_number'] ?? '',
                'transaction_type' => $dto->filter['transaction_type'] ?? 'all',
            ],
            'page' => $page,
            'page_size' => $pageSize,
        ];

        // Отправка запроса к API Ozon
        return OzonApiRequestHelper::sendRequest(
            'POST',
            '/v3/finance/transaction/list',
            $clientId,
            $apiKey,
            $data
        );

    }
}
