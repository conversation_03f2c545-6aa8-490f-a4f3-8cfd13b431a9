<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\Handlers;

use App\Contracts\Repositories\OzonV3FinanceTransactionListRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

class OzonV3FinanceTransactionListGetHandler
{
    public function __construct(
        private readonly OzonV3FinanceTransactionListRepositoryContract $repository,
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        $data = $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage,
        );

        return $data;
    }
}
