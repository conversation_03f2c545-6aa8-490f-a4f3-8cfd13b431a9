<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseAddressService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseAddressesRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseAddressService\DTO\WarehouseAddressDTO;
use InvalidArgumentException;

readonly class WarehouseAddressUpdateHandler
{
    public function __construct(
        private WarehouseAddressesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseAddressDTO) {
            throw new InvalidArgumentException();
        }
        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
