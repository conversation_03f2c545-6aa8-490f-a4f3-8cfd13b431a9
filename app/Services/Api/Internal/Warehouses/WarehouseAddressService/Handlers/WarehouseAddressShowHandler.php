<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseAddressService\Handlers;

use App\Contracts\Repositories\WarehouseAddressesRepositoryContract;

readonly class WarehouseAddressShowHandler
{
    public function __construct(
        private WarehouseAddressesRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
