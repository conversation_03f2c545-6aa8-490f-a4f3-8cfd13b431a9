<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseAddressService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class WarehouseAddressDTO implements HasInsertArrayDtoContract, DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public string $country_id,
        public ?string $warehouse_id,
        public ?string $resourceId,
        public ?string $postcode = null,
        public ?string $region = null,
        public ?string $city = null,
        public ?string $street = null,
        public ?string $house = null,
        public ?string $office = null,
        public ?string $other = null,
        public ?string $comment = null,
    ) {
    }
    public function toUpdateArray(): array
    {
        return [
            'country_id' => $this->country_id,
            'postcode' => $this->postcode,
            'region' => $this->region,
            'city' => $this->city,
            'street' => $this->street,
            'house' => $this->house,
            'office' => $this->office,
            'other' => $this->other,
            'comment' => $this->comment
        ];
    }
    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinet_id,
            'country_id' => $this->country_id,
            'postcode' => $this->postcode,
            'region' => $this->region,
            'city' => $this->city,
            'street' => $this->street,
            'house' => $this->house,
            'office' => $this->office,
            'other' => $this->other,
            'comment' => $this->comment
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            country_id: $data['country_id'],
            warehouse_id: $data['warehouse_id'] ?? null,
            resourceId: $data['id'] ?? null,
            postcode: $data['postcode'] ?? null,
            region: $data['region'] ?? null,
            city: $data['city'] ?? null,
            street: $data['street'] ?? null,
            house: $data['house'] ?? null,
            office: $data['office'] ?? null,
            other: $data['other'] ?? null,
            comment: $data['comment'] ?? null
        );
    }
}
