<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseGroups\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class WarehouseGroupDTO implements HasInsertArrayDtoContract, DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public string $name,
        public ?string $resourceId,
        public ?string $parent_id = null
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinet_id,
            'name' => $this->name,
            'parent_id' => $this->parent_id
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'parent_id' => $this->parent_id
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            name: $data['name'],
            resourceId: $data['id'] ?? null,
            parent_id: $data['parent_id'] ?? null
        );
    }
} 