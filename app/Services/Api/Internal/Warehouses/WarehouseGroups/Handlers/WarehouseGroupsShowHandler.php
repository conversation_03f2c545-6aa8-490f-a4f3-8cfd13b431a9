<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseGroups\Handlers;

use App\Contracts\Repositories\WarehouseGroupsRepositoryContract;

readonly class WarehouseGroupsShowHandler
{
    public function __construct(
        private WarehouseGroupsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
} 