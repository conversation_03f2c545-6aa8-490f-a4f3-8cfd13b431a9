<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseCellSizesRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\DTO\WarehouseCellSizeDTO;
use InvalidArgumentException;

readonly class WarehouseCellSizesUpdateHandler
{
    public function __construct(
        private WarehouseCellSizesRepositoryContract $repository,
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseCellSizeDTO) {
            throw new InvalidArgumentException();
        }
        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
