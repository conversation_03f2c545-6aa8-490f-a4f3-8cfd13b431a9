<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers;

use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;

readonly class WarehouseCellGroupsDeleteHandler
{
    public function __construct(
        private WarehouseCellGroupsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
