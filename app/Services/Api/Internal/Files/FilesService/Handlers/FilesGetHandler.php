<?php

namespace App\Services\Api\Internal\Files\FilesService\Handlers;

use App\Contracts\Repositories\FilesRepositoryContract;
use App\DTO\IndexRequestDTO;
use App\Services\Storage\S3StorageService;
use Illuminate\Support\Collection;

readonly class FilesGetHandler
{
    public function __construct(
        private FilesRepositoryContract $repository,
        private S3StorageService $storageService
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        $result = $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );

        if (isset($result['data'][0]->path)) {
            foreach ($result['data'] as $item) {
                $item->path = $this->storageService->getUrl($item->path, $item->is_private);
            }
        }

        return $result;
    }
}
