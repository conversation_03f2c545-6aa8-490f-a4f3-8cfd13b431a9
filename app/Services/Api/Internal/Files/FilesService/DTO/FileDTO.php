<?php

namespace App\Services\Api\Internal\Files\FilesService\DTO;

use App\Contracts\DtoContract;

class FileDTO implements DtoContract
{
    public function __construct(
        public string $cabinetId,
        public mixed $file,
        public bool $isPrivate = false,
        public ?string $type = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['cabinet_id'],
            $data['file'],
            $data['is_private'] ?? false,
            $data['type'] ?? null
        );
    }
}
