<?php

namespace App\Services\Api\Internal\References\CountriesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class CountryDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public string $name,
        public string $departmentId,
        public string $employeeId,
        public bool $isCommon = false,
        public ?string $fullName = null,
        public ?string $code = null,
        public ?string $iso2 = null,
        public ?string $iso3 = null,
        public ?string $id = null
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'name' => $this->name,
            'full_name' => $this->fullName,
            'code' => $this->code,
            'iso2' => $this->iso2,
            'iso3' => $this->iso3,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'is_common' => $this->isCommon,
            'cabinet_id' => $this->cabinetId,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'full_name' => $this->fullName,
            'code' => $this->code,
            'iso2' => $this->iso2,
            'iso3' => $this->iso3,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'is_common' => $this->isCommon,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            name: $data['name'],
            departmentId: $data['department_id'],
            employeeId: $data['employee_id'],
            isCommon: $data['is_common'] ?? false,
            fullName: $data['full_name'] ?? null,
            code: $data['code'] ?? null,
            iso2: $data['iso2'] ?? null,
            iso3: $data['iso3'] ?? null,
            id: $data['id'] ?? null,
        );
    }
}
