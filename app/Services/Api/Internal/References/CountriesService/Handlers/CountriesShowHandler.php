<?php

namespace App\Services\Api\Internal\References\CountriesService\Handlers;

use App\Contracts\Repositories\CountriesRepositoryContract;
use App\Traits\HasOrderedUuid;

class CountriesShowHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly CountriesRepositoryContract $repository
    ) {
    }
    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
