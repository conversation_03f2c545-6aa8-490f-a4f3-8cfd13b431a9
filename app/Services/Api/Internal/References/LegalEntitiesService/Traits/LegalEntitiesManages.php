<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService\Traits;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

trait LegalEntitiesManages
{
    public function manageAccounts(mixed $accounts): void
    {
        $accounts = collect($accounts);

        $defaultKeys = [
            'is_main' => false,
            'balance' => 0,
            'bank' => null,
            'bik' => null,
            'correspondent_account' => null,
            'payment_account' => null,
            'legal_entity_id' => $this->resourceId,
            'address' => null,
            'updated_at' => Carbon::now(),
            'created_at' => Carbon::now(),
        ];

        $hasMain = false;
        $accounts = $accounts->map(function ($item) use ($defaultKeys, &$hasMain) {
            if (!isset($item['id'])) {
                $item['id'] = $this->generateUuid();
            }

            if (isset($item['is_main']) && $item['is_main']) {
                if ($hasMain) {
                    throw new RuntimeException('Only one main account allowed');
                }
                $hasMain = true;
            }
            return array_merge($defaultKeys, $item);
        });

        $currentIds = DB::table('legal_accounts')
            ->where('legal_entity_id', $this->resourceId)
            ->pluck('id');

        $toDelete = $currentIds->diff($accounts->pluck('id'));

        DB::table('legal_accounts')
            ->upsert(
                $accounts->toArray(),
                ['id'],
                [
                    'bik', 'correspondent_account','payment_account',
                    'balance', 'bank', 'address','is_main',
                    'updated_at'
                ]
            );

        DB::table('legal_accounts')
            ->whereIn('id', $toDelete)
            ->delete();
    }
}
