<?php

namespace App\Services\Api\Internal\References\LegalEntitiesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Directories\LegalEntitiesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\LegalEntitiesService\Handlers\LegalEntityBulkDeleteHandler;
use App\Services\Api\Internal\References\LegalEntitiesService\Handlers\LegalEntityCreateHandler;
use App\Services\Api\Internal\References\LegalEntitiesService\Handlers\LegalEntityDeleteHandler;
use App\Services\Api\Internal\References\LegalEntitiesService\Handlers\LegalEntityGetHandler;
use App\Services\Api\Internal\References\LegalEntitiesService\Handlers\LegalEntityShowHandler;
use App\Services\Api\Internal\References\LegalEntitiesService\Handlers\LegalEntityUpdateHandler;
use Illuminate\Support\Collection;

readonly class LegalEntitiesService implements LegalEntitiesServiceContract
{
    public function __construct(
        private LegalEntityGetHandler $getHandler,
        private LegalEntityCreateHandler $createHandler,
        private LegalEntityUpdateHandler $updateHandler,
        private LegalEntityDeleteHandler $deleteHandler,
        private LegalEntityShowHandler $showHandler,
        private LegalEntityBulkDeleteHandler $bulkDeleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }
}
