<?php

namespace App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Services\Api\Internal\References\CabinetCurrenciesService\DTO\CabinetCurrencyDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class CabinetCurrencyUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CabinetCurrenciesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof CabinetCurrencyDTO) {
            throw new InvalidArgumentException();
        }

        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
