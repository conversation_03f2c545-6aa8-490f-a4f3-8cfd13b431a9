<?php

namespace App\Services\Api\Internal\References\PackingsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class PackingDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinetId,
        public ?string $resourceId,
        public string $name,
        public ?string $description,
        public ?string $length,
        public ?string $width,
        public ?string $height,
        public string $measurementUnitSizeId,
        public ?string $weight,
        public string $measurementUnitWeightId,
        public ?string $volume,
        public string $measurementUnitVolumeId,
        public string $employeeId,
        public string $departmentId
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'name' => $this->name,
            'description' => $this->description,
            'length' => $this->length,
            'width' => $this->width,
            'height' => $this->height,
            'measurement_unit_size_id' => $this->measurementUnitSizeId,
            'weight' => $this->weight,
            'measurement_unit_weight_id' => $this->measurementUnitWeightId,
            'volume' => $this->volume,
            'measurement_unit_volume_id' => $this->measurementUnitVolumeId,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'length' => $this->length,
            'width' => $this->width,
            'height' => $this->height,
            'measurement_unit_size_id' => $this->measurementUnitSizeId,
            'weight' => $this->weight,
            'measurement_unit_weight_id' => $this->measurementUnitWeightId,
            'volume' => $this->volume,
            'measurement_unit_volume_id' => $this->measurementUnitVolumeId,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            resourceId: $data['resource_id'] ?? null,
            name: $data['name'],
            description: $data['description'] ?? null,
            length: $data['length'] ?? null,
            width: $data['width'] ?? null,
            height: $data['height'] ?? null,
            measurementUnitSizeId: $data['measurement_unit_size_id'],
            weight: $data['weight'] ?? null,
            measurementUnitWeightId: $data['measurement_unit_weight_id'],
            volume: $data['volume'] ?? null,
            measurementUnitVolumeId: $data['measurement_unit_volume_id'],
            employeeId: $data['employee_id'],
            departmentId: $data['department_id']
        );
    }
}
