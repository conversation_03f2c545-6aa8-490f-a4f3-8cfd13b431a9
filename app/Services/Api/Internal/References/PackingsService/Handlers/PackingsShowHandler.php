<?php

namespace App\Services\Api\Internal\References\PackingsService\Handlers;

use App\Contracts\Repositories\PackingsRepositoryContract;

readonly class PackingsShowHandler
{
    public function __construct(
        private PackingsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $packing = $this->repository->show($resourceId);

        if ($packing) {
            $packing->measurement_unit_size = json_decode($packing->measurement_unit_size);
            $packing->measurement_unit_weight = json_decode($packing->measurement_unit_weight);
            $packing->measurement_unit_volume = json_decode($packing->measurement_unit_volume);
        }
        return $packing;
    }
}
