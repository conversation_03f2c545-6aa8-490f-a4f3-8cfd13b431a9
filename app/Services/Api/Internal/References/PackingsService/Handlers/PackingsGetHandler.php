<?php

namespace App\Services\Api\Internal\References\PackingsService\Handlers;

use App\Contracts\Repositories\PackingsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class PackingsGetHandler
{
    public function __construct(
        private PackingsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            id: $dto->id,
            filters: $dto->filters,
            fields: $dto->fields,
            sortField: $dto->sortField,
            sortDirection: $dto->sortDirection,
            page: $dto->page,
            perPage: $dto->perPage
        );
    }
}
