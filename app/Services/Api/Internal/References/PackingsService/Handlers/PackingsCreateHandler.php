<?php

namespace App\Services\Api\Internal\References\PackingsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\PackingsRepositoryContract;
use App\Traits\HasOrderedUuid;

class PackingsCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly PackingsRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
