<?php

namespace App\Services\Api\Internal\References\DiscountsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class DiscountDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public string $employeeId,
        public string $departmentId,
        public ?string $cabinetId,
        public string $name,
        public ?string $resourceId,
        public bool $status,
        public ?string $cabinetPriceId,
        public string $fixedDiscount,
        public bool $productsServices,
        public bool $contractors,
        public ?string $accrualRule,
        public ?int $writeoffRule,
        public ?int $maxProcPayment,
        public ?bool $accrualWriteoff,
        public string $type,
        public array $products,
        public ?array $contractorGroups,
        public array $savings,
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'id' => $id,
            'cabinet_id' => $this->cabinetId,                 // cabinet_id
            'type' => $this->type,                             // Тип Специальная цена, Бонусная программа, Накопительная скидка, Персональная скидка, Округление копеек
            'name' => $this->name,                           // Название Скидки
            'status' => $this->status,                         // Статус
            'cabinet_price_id' => $this->cabinetPriceId,   // Использовать тип цен из карточки товара
            'fixed_discount' => $this->fixedDiscount,         // Использовать фиксированную скидку
            'products_services' => $this->productsServices,   // Все товары и услуги - 0 Или Отдельный - 1
            'contractors' => $this->contractors,               // Все контрагенты - 0 Или Контрагенты из групп - 1
            'accrual_rule' => $this->accrualRule,             // Правило начисления
            'writeoff_rule' => $this->writeoffRule,           // Правило списания
            'max_proc_payment' => $this->maxProcPayment,     // Максимальный % оплаты
            'accrual_writeoff' => $this->accrualWriteoff,     // Одновременное начисление и списание
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'type' => $this->type,                             // Тип Специальная цена, Бонусная программа, Накопительная скидка, Персональная скидка, Округление копеек
            'name' => $this->name,         // Название Скидки
            'status' => $this->status,                         // Статус
            'cabinet_price_id' => $this->cabinetPriceId,   // Использовать тип цен из карточки товара
            'fixed_discount' => $this->fixedDiscount,         // Использовать фиксированную скидку
            'products_services' => $this->productsServices,   // Все товары и услуги - 0 Или Отдельный - 1
            'contractors' => $this->contractors,               // Все контрагенты - 0 Или Контрагенты из групп - 1
            'accrual_rule' => $this->accrualRule,             // Правило начисления
            'writeoff_rule' => $this->writeoffRule,           // Правило списания
            'max_proc_payment' => $this->maxProcPayment,     // Максимальный % оплаты
            'accrual_writeoff' => $this->accrualWriteoff,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            employeeId: $data['employee_id'],
            departmentId: $data['department_id'],
            cabinetId: $data['cabinet_id'] ?? null,
            name: $data['name'],
            resourceId: $data['resource_id'] ?? null,
            status: $data['status'] ?? false,
            cabinetPriceId: $data['cabinet_price_id'] ?? null,
            fixedDiscount: $data['fixed_discount'] ?? 0,
            productsServices: $data['products_services'] ?? false,
            contractors: $data['contractors'] ?? false,
            accrualRule: $data['accrual_rule'] ?? 1, // храним в копейках!
            writeoffRule: $data['writeoff_rule'] ?? 1,
            maxProcPayment: $data['max_proc_payment'] ?? 0,
            accrualWriteoff: $data['accrual_writeoff'] ?? true,
            type: $data['type'],
            products: $data['products'] ?? [],
            contractorGroups: $data['contractor_groups'] ?? [],
            savings: $data['savings'] ?? [],
        );
    }
}
