<?php

namespace App\Services\Api\Internal\References\DiscountsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\DiscountContractorGroupRepositoryContract;
use App\Contracts\Repositories\DiscountProductsRepositoryContract;
use App\Contracts\Repositories\DiscountSavingsRepositoryContract;
use App\Contracts\Repositories\DiscountsRepositoryContract;
use App\Enums\Api\Internal\DiscountTypeEnum;
use App\Services\Api\Internal\References\DiscountsService\DTO\DiscountDTO;
use App\Services\Api\Internal\References\DiscountsService\Traits\DiscountsManages;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class DiscountsCreateHandler
{
    use DiscountsManages;
    use HasOrderedUuid;
    use ConverterForKopecks;

    private string $resourceId;

    public function __construct(
        private readonly DiscountsRepositoryContract $repository,
        DiscountContractorGroupRepositoryContract $groupRepository,
        DiscountSavingsRepositoryContract $savingsRepository,
        DiscountProductsRepositoryContract $productsRepository
    ) {
        $this->resourceId = $this->generateUuid();
        $this->initGroupRepository($groupRepository);
        $this->initSavingsRepository($savingsRepository);
        $this->initProductsRepository($productsRepository);
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof DiscountDTO) {
            throw new InvalidArgumentException();
        }

        $data = $dto->toInsertArray($this->resourceId);

        $data['accrual_rule'] = $this->rublesInKopeck($data['accrual_rule']); // храним в копейках!

        $this->repository->insert($data);

        if ($dto->type == DiscountTypeEnum::CUMULATIVE_DISCOUNT->value && $dto->savings) {

            $this->manageSavings($dto->savings);

        }

        if ($dto->type != DiscountTypeEnum::BONUS_PROGRAM->value && $dto->products) {

            $this->manageProducts($dto->products);

        }

        if ($dto->contractorGroups) {

            $this->manageGroups($dto->contractorGroups);

        }

        return $this->resourceId;
    }
}
