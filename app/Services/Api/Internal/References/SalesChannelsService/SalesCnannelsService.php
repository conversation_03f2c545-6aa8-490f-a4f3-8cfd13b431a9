<?php

namespace App\Services\Api\Internal\References\SalesChannelsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Directories\SalesChannelsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\SalesChannelsService\Handlers\SalesChannelsBulkDeleteHandler;
use App\Services\Api\Internal\References\SalesChannelsService\Handlers\SalesChannelsCreateHandler;
use App\Services\Api\Internal\References\SalesChannelsService\Handlers\SalesChannelsDeleteHandler;
use App\Services\Api\Internal\References\SalesChannelsService\Handlers\SalesChannelsGetHandler;
use App\Services\Api\Internal\References\SalesChannelsService\Handlers\SalesChannelsShowHandler;
use App\Services\Api\Internal\References\SalesChannelsService\Handlers\SalesChannelsUpdateHandler;
use Illuminate\Support\Collection;

readonly class SalesCnannelsService implements SalesChannelsServiceContract
{
    public function __construct(
        private SalesChannelsCreateHandler $createHandler,
        private SalesChannelsShowHandler $showHandler,
        private SalesChannelsGetHandler $getHandler,
        private SalesChannelsDeleteHandler $deleteHandler,
        private SalesChannelsUpdateHandler $updateHandler,
        private SalesChannelsBulkDeleteHandler $bulkDeleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }
}
