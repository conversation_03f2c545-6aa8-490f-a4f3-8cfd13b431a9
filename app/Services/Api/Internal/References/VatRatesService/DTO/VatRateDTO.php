<?php

namespace App\Services\Api\Internal\References\VatRatesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class VatRateDTO implements HasInsertArrayDtoContract, DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public ?string $id,
        public int $rate,
        public string $employee_id,
        public string $department_id,
        public ?string $description = null,
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinet_id,
            'rate' => $this->rate,
            'description' => $this->description,
            'employee_id' => $this->employee_id,
            'department_id' => $this->department_id,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            id: $data['id'] ?? null,
            rate: $data['rate'] ?? null,
            employee_id: $data['employee_id'],
            department_id: $data['department_id'],
            description: $data['description'] ?? null,
        );
    }

    public function toUpdateArray(): array
    {
        return [
            'rate' => $this->rate,
            'description' => $this->description,
            'employee_id' => $this->employee_id,
            'department_id' => $this->department_id,
        ];
    }
}
