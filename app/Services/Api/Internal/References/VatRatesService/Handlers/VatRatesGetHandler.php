<?php

namespace App\Services\Api\Internal\References\VatRatesService\Handlers;

use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class VatRatesGetHandler
{
    public function __construct(
        private VatRatesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
