<?php

namespace App\Services\Api\Internal\References\VatRatesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Services\Api\Internal\References\VatRatesService\DTO\VatRateDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class VatRatesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly VatRatesRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof VatRateDTO) {
            throw new InvalidArgumentException();
        }
        $this->repository->update(
            $dto->id,
            $dto->toUpdateArray()
        );
    }
}
