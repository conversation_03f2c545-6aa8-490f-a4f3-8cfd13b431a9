<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers;

use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Traits\CheckSystemClone;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class MeasurementUnitGroupCloneSystemHandler
{
    use HasOrderedUuid;
    use CheckSystemClone;

    public function run(object $group, string $cabinetId, string $resourceId = null): array
    {
        $this->checkSystemClone($group->id, $cabinetId);

        $groupId = $this->generateUuid();
        $date = Carbon::now();


        DB::table('measurement_unit_groups')
            ->insert([
                'id' => $groupId,
                'created_at' => $date,
                'name' => $group->name,
                'cabinet_id' => $cabinetId,
                'tech_type' => $group->tech_type
            ]);

        DB::table('cabinet_measurement_system_groups')
            ->insert([
                'id' => $this->generateUuid(),
                'group_id' => $groupId,
                'cabinet_id' => $cabinetId,
                'system_group_id' => $group->id,
                'created_at' => $date,
            ]);

        $newResourceId = '';
        DB::table('measurement_units')
            ->where('group_id', $group->id)
            ->orderBy('name')
            ->each(function ($unit) use ($groupId, $date, $resourceId, &$newResourceId, $cabinetId) {
                $insertId = $this->generateUuid();
                if ($unit->id == $resourceId) {
                    $newResourceId = $insertId;
                }
                DB::table('measurement_units')
                    ->insert([
                        'id' => $insertId,
                        'created_at' => $date,
                        'name' => $unit->name,
                        'conversion_factor' => $unit->conversion_factor,
                        'group_id' => $groupId,
                        'cabinet_id' => $cabinetId,
                        'short_name' => $unit->short_name,
                        'code' => $unit->code
                    ]);
            });

        return [
            'group_id' => $groupId,
            'new_resource_id' => $newResourceId
        ];
    }
}
