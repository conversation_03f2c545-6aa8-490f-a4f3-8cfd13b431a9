<?php

namespace App\Services\Api\Internal\References\CabinetPricesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\CabinetPricesRepositoryContract;
use App\Traits\HasOrderedUuid;

class CabinetPriceCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly CabinetPricesRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert($dto->toInsertArray($this->resourceId));

        return $this->resourceId;
    }
}
