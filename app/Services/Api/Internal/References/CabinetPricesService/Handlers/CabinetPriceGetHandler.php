<?php

namespace App\Services\Api\Internal\References\CabinetPricesService\Handlers;

use App\Contracts\Repositories\CabinetPricesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class CabinetPriceGetHandler
{
    public function __construct(
        private CabinetPricesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
