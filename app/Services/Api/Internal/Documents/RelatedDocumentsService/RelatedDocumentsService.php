<?php

namespace App\Services\Api\Internal\Documents\RelatedDocumentsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\RelatedDocumentsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\Handlers\RelatedDocumentCreateHandler;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\Handlers\RelatedDocumentsGetHandler;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\Handlers\RelatedDocumentsShowHandler;
use Illuminate\Support\Collection;

readonly class RelatedDocumentsService implements RelatedDocumentsServiceContract
{
    public function __construct(
        private RelatedDocumentCreateHandler $createHandler,
        private RelatedDocumentsGetHandler $getHandler,
        private RelatedDocumentsShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data['document_id']);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        // TODO: Implement update() method.
    }

    public function delete(string $id): void
    {
        // TODO: Implement delete() method.
    }
}
