<?php

namespace App\Services\Api\Internal\Documents\RelatedDocumentsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;

class RelatedDocumentDTO implements DtoContract, HasInsertArrayDtoContract
{
    public function __construct(
        public string $bindedDocumentId,
        public string $documentId
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            bindedDocumentId: $data['binded_document_id'],
            documentId: $data['document_id']
        );
    }
}
