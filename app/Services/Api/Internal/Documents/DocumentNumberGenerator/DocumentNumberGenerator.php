<?php

namespace App\Services\Api\Internal\Documents\DocumentNumberGenerator;

use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\NumberingType;
use Carbon\Carbon;
use http\Exception\RuntimeException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use stdClass;

class DocumentNumberGenerator
{
    public string $numberingType;
    public bool $globalNumbering;
    public object $legalEntity;
    private array $acceptedTables = [
        'acceptances',
        'shipments',
        'vendor_orders',
        'customer_orders',
        'incoming_payments',
        'outgoing_payments',
        'goods_transfers',
        'wildberries_fbs_orders',
        'wildberries_dbs_orders',
        'wildberries_self_delivery_orders',
        'issued_comission_reports',
        'received_comission_reports',
        'ozon_fbs_orders',
        'ozon_fbo_orders',
    ];

    public function __construct(
        private readonly string  $documentType,
        private readonly string  $cabinetId,
        private readonly ?string $number,
        private readonly string  $legalEntityId,
        private readonly array   $ghostData = [] //нужно на случай если нужно сгенерировать номер, по последний док еще не создан
    ) {
        if (!in_array($this->documentType, $this->acceptedTables, true)) {
            throw new RuntimeException('Invalid document type');
        }

        $cabinetSettings = DB::table('cabinet_settings')
            ->where('cabinet_id', $this->cabinetId)
            ->first();

        if (!$cabinetSettings) {
            throw new RuntimeException('Cabinet settings not found');
        }

        $this->numberingType = $cabinetSettings->numbering_type;
        $this->globalNumbering = $cabinetSettings->global_numbering;

        $this->legalEntity = DB::table('legal_entities')
            ->leftJoin('legal_details', 'legal_details.legal_entity_id', '=', 'legal_entities.id')
            ->where('legal_entities.id', $this->legalEntityId)
            ->select(
                [
                    'legal_details.type as type',
                    'legal_entities.short_name as short_name',
                    'legal_details.firstname as firstname',
                    'legal_details.lastname as lastname',
                    'legal_details.patronymic as patronymic',
                    'legal_details.prefix as prefix'
                ]
            )
            ->first();

        if (!$this->legalEntity) {
            throw new RuntimeException('Legal entity not found');
        }
    }

    public function generateNumber(): string
    {
        if ($this->number && $this->numberingType === NumberingType::OnlyNumbers->value) {
            return $this->number;
        }

        if ($this->ghostData) {
            $latestDocument = new stdClass();
            $latestDocument->number = (int)$this->ghostData['number'];
        } else {
            $latestDocument = $this->getLatestDocument();
        }
        if ($latestDocument) {
            [$prefix, $numberPart] = $this->parseCustomNumber($latestDocument->number);


            $numberPart++;

            if ($this->numberingType === NumberingType::CPNumbers->value) {
                $prefix = $this->cpTypePrefixGenerator();
            }

            if (empty($prefix)) {
                $numberPart = str_pad((string)$numberPart, 6, '0', STR_PAD_LEFT);
            }

            return $prefix . $numberPart;
        }

        if ($this->numberingType === NumberingType::CPNumbers->value) {
            return $this->cpTypePrefixGenerator() . '1';
        }

        return '000001';
    }

    private function parseCustomNumber(string $number): array
    {
        if (preg_match('/^(.*?)(\d+)$/', $number, $matches)) {
            // Если номер заканчивается числовой частью, разделяем
            return [$matches[1], (int)$matches[2]];
        }

        // Если числовой части нет, весь номер — префикс
        return [$number, 0];
    }

    public function getLatestDocument(): ?object
    {
        $latestDocumentQuery = DB::query()
            ->from($this->documentType)
            ->where('cabinet_id', $this->cabinetId)
            ->where('legal_entity_id', $this->legalEntityId);

        if (!$this->globalNumbering && $this->numberingType !== NumberingType::CPNumbers->value) {
            $latestDocumentQuery
                ->where(
                    'created_at',
                    '>=',
                    Carbon::create(
                        Carbon::now()->year
                    )?->startOfYear()
                );
        } elseif ($this->numberingType === NumberingType::CPNumbers->value) {
            $latestDocumentQuery
                ->where(
                    'created_at',
                    '>=',
                    Carbon::create(
                        Carbon::now()
                    )?->startOfDay()
                );
        }

        return $latestDocumentQuery->orderBy('created_at', 'desc')->first();
    }

    /**
     * @return string
     */
    public function cpTypePrefixGenerator(): string
    {
        $prefix = '';
        if ($this->legalEntity->type === LegalEntityType::INDIVIDUAL->value) {
            $prefix .= strtoupper(Str::take($this->legalEntity->lastname, 1));
            $prefix .= strtoupper(Str::take($this->legalEntity->firstname, 1));
            $prefix .= strtoupper(Str::take($this->legalEntity->patronymic, 1));
            $prefix .= '-';
        } elseif ($this->legalEntity->type === LegalEntityType::LEGAL->value) {
            $prefix .= strtoupper($this->legalEntity->prefix);
            $prefix .= '-';
        }

        $prefix .= Carbon::now()->format('ymd');
        $prefix .= '/';
        return $prefix;
    }
}
