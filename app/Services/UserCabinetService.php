<?php

namespace App\Services;

use App\Exceptions\InvalidUuidException;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class UserCabinetService
{
    use HasOrderedUuid;

    /**
     * @throws InvalidUuidException
     */
    public function update(int $userId, string $cabinetId): int
    {
        if (!$this->validateUuid($cabinetId)) {
            throw new InvalidUuidException();
        }

        $accessFlag = DB::table('cabinets')
            ->where('user_id', $userId)
            ->where('id', $cabinetId)
            ->exists();

        if (!$accessFlag) {
            $accessFlag = DB::table('employees')
                ->join('cabinet_employee', 'cabinet_employee.employee_id', '=', 'employees.id')
                ->where('employees.user_id', $userId)
                ->where('cabinet_employee.cabinet_id', $cabinetId)
                ->exists();
        }

        if (!$accessFlag) {
            throw new ResourceNotFoundException();
        }

        return DB::table('users')
            ->where('id', $userId)
            ->update(
                [
                    'current_cabinet_id' => $cabinetId,
                    'updated_at' => Carbon::now()
                ]
            );
    }
}
