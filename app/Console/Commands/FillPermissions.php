<?php

namespace App\Console\Commands;

use Illuminate\Support\Carbon;
use App\Traits\HasOrderedUuid;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;
use App\Enums\Api\Internal\PermissionScopeEnum;

class FillPermissions extends Command
{
    use HasOrderedUuid;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fill-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Получаем данные по умолчанию...');

        $defaultData = require base_path('app/Data/Permissions.php');

        $groupsId = [];
        $categoriesId = [];

        $this->info('Заполняем таблицу "permission_groups"...');

        foreach ($defaultData['groups'] as $group) {
            $groupId = $this->generateUuid();

            DB::table('permission_groups')->insert([
                'id' => $groupId,
                'name' => $group['name'],
                'created_at' => Carbon::now(),
            ]);

            $groupsId[$group['name']] = $groupId;
        }

        $this->info('Заполняем таблицу "permission_categories"...');

        foreach ($defaultData['categories'] as $category) {
            $groupId = $this->generateUuid();

            DB::table('permission_categories')->insert([
                'id' => $groupId,
                'name' => $category['name'],
                'created_at' => Carbon::now(),
            ]);

            $categoriesId[$category['name']] = $groupId;
        }

        $this->info('Заполняем таблицу "permissions"...');


        $permissions = [];
        foreach ($defaultData['permissions'] as $unit) {
            $groupId = $this->generateUuid();

            $permissions[$unit['guard_name']][$unit['operations']] = $groupId;
            DB::table('permissions')->insert([
                'id' => $groupId,
                'require_scope' => $unit['require_scope'],
                'guard_name' => $unit['guard_name'],
                'operation' => $unit['operations'],
                'category_id' => $categoriesId[$unit['category_name']],
                'group_id' => $groupsId[$unit['group_name']],
                'created_at' => Carbon::now(),
            ]);
        }

        $this->info('Заполняем стандартные роли...');

        $defaultRoles = require base_path('app/Data/Roles.php');
        foreach ($defaultRoles as $name => $rolePermissions) {
            $roleId = $this->generateUuid();

            DB::table('roles')->insert([
                'id' => $roleId,
                'name' => $name,
                'created_at' => Carbon::now(),
                'is_system' => true
            ]);

            if ($name == 'Администратор') {
                $adminPermissions = DB::table('permissions')
                    ->join('permission_groups', 'permissions.group_id', '=', 'permission_groups.id')
                    ->where(function (Builder $query) {
                        $query->where('permission_groups.name', '!=', 'Сотрудники')
                            ->where('permissions.operation', '!=', 'delete');
                    })
                    ->get('permissions.*');

                foreach ($adminPermissions as $permission) {
                    DB::table('role_permissions')->insert([
                        'id' => $this->generateUuid(),
                        'created_at' => Carbon::now(),
                        'role_id' => $roleId,
                        'permission_id' => $permission->id,
                        'scope' => $permission->require_scope ? PermissionScopeEnum::SCOPE_ALL->value : null
                    ]);
                }
            } else {
                // Обработка других ролей (не Администратор)
                foreach ($rolePermissions as $rolePermission) {
                    foreach ($rolePermission['operations'] as $operation) {
                        DB::table('role_permissions')->insert([
                            'id' => $this->generateUuid(),
                            'created_at' => Carbon::now(),
                            'role_id' => $roleId,
                            'permission_id' => $permissions[$rolePermission['guard_name']][$operation],
                            'scope' => $rolePermission['scope'] ?? null
                        ]);
                    }
                }
            }
        }
        $this->info('Заполнение таблицы "permissions" завершено.');
    }
}
