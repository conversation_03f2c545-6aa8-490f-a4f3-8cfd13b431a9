<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Contracts\Services\Internal\Directories\CountriesServiceContract;

class FillCountries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fill-countries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill system with countries data';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $countryService = app()->make(CountriesServiceContract::class);
            $countryService->fillSystem();

            return self::SUCCESS;
        } catch (BindingResolutionException $e) {
            $this->error('Error resolving CountriesService');
            return self::FAILURE;
        } catch (\Exception $e) {
            $this->error('Error filling countries: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
