<?php

namespace App\Repositories\Ozon;

use App\Contracts\Repositories\OzonOrderItemsRepositoryContract;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OzonOrderItemsRepository implements OzonOrderItemsRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;

    private const TABLE = 'ozon_order_items';

    public function __construct(
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);

    }

    public function upsert(array $data): int
    {

        return DB::table(self::TABLE)->upsert(
            $data,
            ['inner_posting_number'],
            [
                'ozon_order_id',
                'posting_number',
                'inner_posting_number',
                'in_process_at',
                'shipment_date',
                'status',
                'delivering_date',
                'delivery_date_end',
                'amount',
                'currency_code',
                'name',
                'sku',
                'offer_id',
                'products_price',
                'currency_code_products',
                // 'cost_bayer',
                // 'currency_code_buyer',
                'quantity',
                // 'delivery_price',
                // 'related_postings',
                'purchased',
                'old_price',
                'total_discount_percent',
                'total_discount_value',
                'actions',
                'floor',
                'upper_barcode',
                'lower_barcode',
                'cluster_from',
                'cluster_to',
                'region',
                'city',
                'delivery_type',
                'is_premium',
                'payment_type_group_name',
                'is_legal',
                'client_name',
                'client_email',
                'recipient_name',
                'recipient_tel',
                'delivery_address',
                'index',
                'warehouse',
                'tpl_provider',
                'delivery_method_name',
                'updated_at',
            ]
        );
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

}
