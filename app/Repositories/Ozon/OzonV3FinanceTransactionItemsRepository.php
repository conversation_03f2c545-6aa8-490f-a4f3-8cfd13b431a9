<?php

namespace App\Repositories\Ozon;

use App\Contracts\Repositories\OzonV3FinanceTransactionItemsRepositoryContract;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OzonV3FinanceTransactionItemsRepository implements OzonV3FinanceTransactionItemsRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;

    private const TABLE = 'ozon_transaction_items';

    public function __construct(
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->get();
    }

    public function findByOperationIdAndServiceName(?string $id = null): Collection
    {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->get();
    }

    public function getTransactionItemNamesAndSkus(string $orderId, array $item): array
    {
        return DB::table('ozon_transaction_items')
            ->where('ozon_transaction_id', $orderId)
            ->select('name', 'sku')
            ->get()
            ->map(fn ($item) => $item->name . '|' . $item->sku)
            ->toArray();
    }

    public function insert(array $data): bool
    {
        // $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);

    }

    public function upsert(array $data): int
    {

        return DB::table(self::TABLE)->upsert(
            $data,
            ['id'],
            [
                'ozon_transaction_id',
                'name',
                'sku',
            ]
        );
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

}
