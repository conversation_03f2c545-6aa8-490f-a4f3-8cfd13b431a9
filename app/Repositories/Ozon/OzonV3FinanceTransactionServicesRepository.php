<?php

namespace App\Repositories\Ozon;

use App\Contracts\Repositories\OzonV3FinanceTransactionServicesRepositoryContract;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OzonV3FinanceTransactionServicesRepository implements OzonV3FinanceTransactionServicesRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;

    private const TABLE = 'ozon_transaction_services';

    public function __construct(
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);

    }

    public function upsert(array $data): int
    {

        return DB::table(self::TABLE)->upsert(
            $data,
            ['ozon_transaction_id', 'name'],
            [
                'price',
            ]
        );
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

}
