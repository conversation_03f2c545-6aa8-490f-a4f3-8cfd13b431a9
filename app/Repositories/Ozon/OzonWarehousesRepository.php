<?php

namespace App\Repositories\Ozon;

use App\Contracts\Repositories\OzonWarehousesRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\OzonWarehousesEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OzonWarehousesRepository implements OzonWarehousesRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;

    private const TABLE = 'ozon_fbs_warehouses';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly OzonWarehousesEntity $entity
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $this->authService->init();
        [$baseFields, $relationFields] = $this->entity->parseFields($fields);

        $query = $this->getEntity();
        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::GOODS_AND_SERVICES->value, $this->entity::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($this->entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            match ($value['condition'] ?? true) {
                FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                default => $this->handleInCondition($query, $filter, $value['value']),
            };
        }


        $query->when(isset($filters['search']), function ($query) use ($filters) {
            $query->where('name', 'like', '%' . $filters['search']['value'] . '%');
        });


        $query->when(isset($filters['shared_access']), fn ($query) => $query->where('shared_access', '=', $filters['shared_access']['value']));
        $query->when(isset($filters['show_only']), function ($query) use ($filters) {
            if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                return $query->where('archived_at', '!=', null);
            }

            if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                return $query->whereNull('archived_at');
            }

            return $query;
        });

        return $query
        ->when($sortField, function ($query) use ($sortField, $sortDirection) {
            return $query->sort($sortField, $sortDirection);
        })
        ->paginate($perPage, $page)
        ->get();
    }

    public function getEntity(): OzonWarehousesEntity
    {
        return $this->entity;
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'name' => $query->whereNull('name'),
            default => null,
        };

    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'name' => $query->whereNotNull('name'),
            default => null,
        };

    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'name' => $query->where('name', 'not like', '%' . $value . '%')
            ->orWhere('name', null),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            default => null,
        };

    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'name' => $query->where('name', 'like', '%' . $value . '%'),
            'ozon_company_id' => $query->where('ozon_company_id', 'like', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            default => null,
        };

    }

    public function getWarehousesByWarehouseIds(array $operationIds): array
    {
        return DB::table(self::TABLE)
            ->whereIn('warehouse_id', $operationIds)
            ->pluck('id', 'warehouse_id') // Вернёт массив ['warehouse_id' => 'id']
            ->toArray();
    }

    public function getAllOrderIds(string $cabinetId): Collection
    {
        return DB::table(self::TABLE)
        ->where('cabinet_id', $cabinetId)
        ->pluck('id', 'inner_order_number');
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);

    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)->upsert(
            $data,
            ['warehouse_id'],
            [
                'name',
                'is_rfbs',
                'is_able_to_set_price',
                'has_entrusted_acceptance',
                'first_mile_dropoff_point_id',
                'first_mile_dropoff_timeslot_id',
                'first_mile_is_changing',
                'first_mile_type',
                'is_kgt',
                'can_print_act_in_advance',
                'min_working_days',
                'is_karantin',
                'has_postings_limit',
                'postings_limit',
                'working_days',
                'min_postings_limit',
                'is_timetable_editable',
                'status',
                'is_economy',
                'is_presorted',
                'updated_at',

            ]
        );
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)->where('id', $id)->delete();
    }

}
