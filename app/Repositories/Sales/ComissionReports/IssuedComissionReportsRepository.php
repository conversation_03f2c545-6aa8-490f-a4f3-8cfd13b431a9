<?php

namespace App\Repositories\Sales\ComissionReports;

use App\Contracts\Repositories\IssuedComissionReportsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class IssuedComissionReportsRepository implements IssuedComissionReportsRepositoryContract
{
    use HasTimestamps;

    protected const TABLE = 'issued_comission_reports';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = DB::table(self::TABLE)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter(
            $id,
            $query,
            PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            self::TABLE
        );

        return $query->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE . ' as r')
            ->leftJoin('statuses as s', 'r.status_id', '=', 's.id')
            ->leftJoin('contractors as c', 'r.contractor_id', '=', 'c.id')
            ->leftJoin('legal_entities as le', 'r.legal_entity_id', '=', 'le.id')
            ->leftJoin('file_relations as fr', 'r.id', '=', 'fr.related_id')
            ->leftJoin('files as f', 'fr.file_id', '=', 'f.id')
            ->leftJoin('contracts as co', 'r.contract_id', '=', 'co.id')
            ->leftJoin('issued_comission_report_items as rc', 'r.id', '=', 'rc.report_id')
            ->select([
                'r.*',
                DB::raw("
                        COALESCE(
                            (jsonb_object_agg(
                                'data',
                                jsonb_build_object(
                                    'id', c.id,
                                    'title', c.title
                                )
                            )) -> 'data',
                            '{}'::jsonb
                        ) AS contractor
                    "),
                DB::raw("
                        COALESCE(
                            (jsonb_object_agg(
                                'data',
                                jsonb_build_object(
                                    'id', le.id,
                                    'short_name', le.short_name
                                )
                            )) -> 'data',
                            '{}'::jsonb
                        ) AS legal_entity
                    "),
                DB::raw("
                        COALESCE(
                            (jsonb_object_agg(
                                'data',
                                jsonb_build_object(
                                    'id', co.id,
                                    'number', co.number
                                )
                            )) -> 'data',
                            '{}'::jsonb
                        ) AS contract
                    "),
                DB::raw("
                        COALESCE(
                            (jsonb_object_agg(
                                'data',
                                jsonb_build_object(
                                    'id', s.id,
                                    'name', s.name
                                )
                            ) FILTER (where s.id is not null)) -> 'data',
                            '{}'::jsonb
                        ) AS status
                    "),
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', f.id,
                                    'name', f.name,
                                    'path', f.path,
                                    'is_private', f.is_private
                                )
                            ) filter (where f.id is not null), '[]'
                        ) AS files
                    "),
                DB::raw("
                        coalesce(
                            jsonb_agg(rc) filter (where rc.id is not null), '[]'
                        ) AS items
                    "),
            ])
            ->where('r.id', $id)
            ->groupBy(['r.id', 's.name', 'c.title', 'c.id', 's.id'])
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function findFirst(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }
}
