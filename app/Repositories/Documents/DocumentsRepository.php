<?php

namespace App\Repositories\Documents;

use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class DocumentsRepository implements DocumentsRepositoryContract
{
    use HasTimestamps;
    protected const TABLE = 'documents';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function deleteWhereDocumentableId(string $id): int
    {
        DB::transaction(function () use ($id) {
            // Получаем информацию об удаляемом узле
            $node = DB::table('documents')
                ->select(['lft', 'rgt', 'tree_id'])
                ->where('documentable_id', $id)
                ->first();

            if (!$node) {
                return;
            }

            // Проверяем, что это листовой узел (разница между rgt и lft должна быть 1)
            if ($node->rgt - $node->lft !== 1) {
                throw new RuntimeException('Cannot delete node with children');
            }

            // Удаляем узел
            DB::table('documents')
                ->where('documentable_id', $id)
                ->delete();

            // Обновляем lft и rgt значения для узлов справа от удаленного
            DB::statement("
                UPDATE documents
                SET
                    lft = CASE
                        WHEN lft > ? THEN lft - 2
                        ELSE lft
                    END,
                    rgt = CASE
                        WHEN rgt > ? THEN rgt - 2
                        ELSE rgt
                    END
                WHERE tree_id = ?
                AND (lft > ? OR rgt > ?)
            ", [
                $node->lft,
                $node->rgt,
                $node->tree_id,
                $node->lft,
                $node->rgt
            ]);
        });
        return 1;
    }

    public function getType(string $id): ?string
    {
        return DB::table(self::TABLE)
            ->where('documentable_id', $id)
            ->first()
            ->documentable_type;
    }

    public function deleteWhereDocumentableIdIn(array $ids): int
    {
        DB::transaction(function () use ($ids) {
            // Получаем узлы для удаления
            $nodes = DB::table('documents')
                ->select(['lft', 'rgt', 'tree_id'])
                ->whereIn('documentable_id', $ids)
                ->get();

            if ($nodes->isEmpty()) {
                return;
            }

            // Проверяем, что узлы являются листовыми
            foreach ($nodes as $node) {
                if ($node->rgt - $node->lft !== 1) {
                    throw new RuntimeException('Cannot delete node with children');
                }
            }

            DB::table('documents')
                ->whereIn('documentable_id', $ids)
                ->delete();

            foreach ($nodes->groupBy('tree_id') as $treeId => $ranges) {
                $shift = $ranges->count() * 2;
                $minLft = $ranges->min('lft');
                $maxRgt = $ranges->max('rgt');

                DB::statement("
                    UPDATE documents
                    SET
                        lft = CASE
                            WHEN lft > ? THEN lft - ?
                            ELSE lft
                        END,
                        rgt = CASE
                            WHEN rgt > ? THEN rgt - ?
                            ELSE rgt
                        END
                    WHERE tree_id = ?
                    AND (lft > ? OR rgt > ?)
                ", [
                    $maxRgt, $shift,
                    $maxRgt, $shift,
                    $treeId,
                    $minLft,
                    $maxRgt
                ]);
            }
        });
        return 1;
    }

    public function bulkInsert(array $items): bool
    {
        return DB::table(self::TABLE)->insert($items);
    }
}
