<?php

namespace App\Repositories\Workspace;

use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EmployeeEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class EmployeesRepository implements EmployeeRepositoryContract
{
    use HasTimestamps;
    use Archivable;

    private const TABLE = 'employees';
    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function getByUserIdAndCabinet(int $userId, string $cabinetId): ?object
    {
        return DB::table(self::TABLE)
            ->join('cabinet_employee', self::TABLE . '.id', '=', 'cabinet_employee.employee_id')
            ->where(self::TABLE . '.user_id', $userId)
            ->where('cabinet_employee.cabinet_id', $cabinetId)
            ->first();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        // Сначала получаем ID сотрудников из cabinet_employee
        $employeeIds = DB::table('cabinet_employee')
            ->where('cabinet_id', $id)
            ->pluck('employee_id');

        // Затем строим основной запрос по employees
        $query->whereHas('cabinetEmployee', function ($query) use ($id) {
            $query->where('cabinet_id', $id);
        });

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::EMPLOYEES->value, $query::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['show_only']['value']),
            function ($query) use ($filters) {
                if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                    return $query->where('archived_at', '!=', null);
                }

                if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                    return $query->whereNull('archived_at');
                }

                return $query;
            }
        );

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where(function ($query) use ($filters) {
                $query->where('lastname', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('firstname', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('patronymic', 'ilike', '%' . $filters['search']['value'] . '%');
            });
        });

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function checkUserExistsInCabinet(int $userId, string $cabinetId): bool
    {
        return  DB::table(self::TABLE . ' as e')
            ->join('cabinet_employee as ce', 'ce.employee_id', '=', 'e.id')
            ->where('ce.cabinet_id', $cabinetId)
            ->where('e.user_id', $userId)
            ->exists();
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    private function getEntity(): EmployeeEntity
    {
        return new EmployeeEntity();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'inn' => $query->whereNull('inn'),
            'phone' => $query->whereNull('telephone'),
            'email' => $query->whereNull('email'),
            'position' => $query->whereNull('position'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'inn' => $query->whereNotNull('inn'),
            'phone' => $query->whereNotNull('telephone'),
            'email' => $query->whereNotNull('email'),
            'position' => $query->whereNotNull('position'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'inn' => $query->where('inn', 'not like', '%' . $value . '%'),
            'phone' => $query->where('telephone', 'not like', '%' . $value . '%'),
            'email' => $query->where('email', 'not like', '%' . $value . '%'),
            'position' => $query->where('position', 'not like', '%' . $value . '%'),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'inn' => $query->where('inn', 'ilike', '%' . $value . '%'),
            'phone' => $query->where('telephone', 'like', '%' . $value . '%'),
            'email' => $query->where('email', 'ilike', '%' . $value . '%'),
            'position' => $query->where('position', 'ilike', '%' . $value . '%'),
            default => null,
        };
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }
}
