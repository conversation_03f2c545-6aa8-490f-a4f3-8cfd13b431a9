<?php

namespace App\Repositories\Workspace;

use App\Contracts\Repositories\BookmarkRepositoryContract;
use App\Entities\BookmarkEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BookmarkRepository implements BookmarkRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'bookmarks';

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id)
            ->when(isset($filters['employee_id']), fn () => $query->where('employee_id', $filters['employee_id']))
            ->when(isset($filters['entity']), fn () => $query->where('entity', $filters['entity']))
            ->when(isset($filters['search']['value']), fn () => $query->where('name', 'ILIKE', '%'. $filters['search']['value'] . '%'));

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function getEntity(): BookmarkEntity
    {
        return new BookmarkEntity();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }
}
