<?php

namespace App\Repositories\Contractors;

use App\Contracts\Repositories\ContractRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\ContractEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractRepository implements ContractRepositoryContract
{
    use HasTimestamps;
    use Archivable;

    private const TABLE = 'contracts';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::CONTRACTORS->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['shared_access']['value']),
            fn ($query) => $query->where('shared_access', $filters['shared_access']['value'])
        );

        $query->when(
            isset($filters['is_sended']['value']),
            fn ($query) => $query->where('held', $filters['is_sended']['value'])
        );
        $query->when(
            isset($filters['is_printed']['value']),
            fn ($query) => $query->where('held', $filters['is_printed']['value'])
        );

        $query->when(
            isset($filters['search']['value']),
            function ($query) use ($filters) {
                $query->where('number', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('comment', 'ilike', '%' . $filters['search']['value'] . '%');
            }
        );

        $query->when(isset($filters['period']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('created_at', [$from, $to]);
        });


        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function getWhereInIds(array $ids): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->get();
    }

    public function getEntity(): ContractEntity
    {
        return new ContractEntity();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'legal_entity' => $query->whereNull('legal_entity_id'),
            'employee_owners' => $query->whereNull('employee_id'),
            'contractors' => $query->whereNull('contractor_id'),
            'department_owners' => $query->whereNull('department_id'),
            'contractor_groups' => $query->whereHas('contractors', function ($query) {
                $query->whereDoesntHave('group');
            }),
            'statuses' => $query->whereNull('status_id'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'legals' => $query->whereNotNull('legal_entity_id'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'contractors' => $query->whereNotNull('contractor_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            'contractor_groups' => $query->whereHas('contractors', function ($query) {
                $query->whereHas('group', function ($query) {
                    $query->whereNotNull('group_id');
                });
            }),
            'statuses' => $query->whereNotNull('status_id'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'legals' => $query->whereNotIn('legal_entity_id', $value),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'contractors' => $query->whereNotIn('contractor_id', $value),
            'contractor_groups' => $query->whereHas('contractors', function ($query) use ($value) {
                $query->whereHas('group', function ($query) use ($value) {
                    $query->whereNotIn('group_id', $value);
                });
            }),
            'statuses' => $query->where(function ($query) use ($value) {
                $query->whereNotIn('status_id', $value)
                    ->orWhere('status_id', null);
            }),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'legals' => $query->whereIn('legal_entity_id', $value),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'contractors' => $query->whereIn('contractor_id', $value),
            'contractor_groups' => $query->with('contractors', function ($query) use ($value) {
                $query->with('group', function ($query) use ($value) {
                    $query->whereIn('group_id', $value);
                });
            }),
            'statuses' => $query->whereIn('status_id', $value),
            default => null,
        };
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function deleteWhereInIds(array $ids): bool
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete() > 0;
    }

    public function getMaxNumber(?string $cabinetId): ?string
    {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->max('number');
    }
}
