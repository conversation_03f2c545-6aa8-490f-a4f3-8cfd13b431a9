<?php

namespace App\Repositories\Contractors\Contractors;

use App\Contracts\Repositories\ContractorsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\ContractorEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractorsRepository implements ContractorsRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const string TABLE = 'contractors';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::CONTRACTORS->value, $query::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['type']['value']),
            fn () => $query->whereHas('details', function ($query) use ($filters) {
                $query->where('type', $filters['type']['value']);
            })
        );

        $query->when(
            isset($filters['show_only']['value']),
            function ($query) use ($filters) {
                if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                    return $query->where('archived_at', '!=', null);
                }

                if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                    return $query->whereNull('archived_at');
                }

                return $query;
            }
        );
        $query->when(isset($filters['shared_access']['value']), fn ($query) => $query->where('shared_access', $filters['shared_access']['value']));
        $query->when(isset($filters['balance']['from']), function ($query) use ($filters) {
            $query->whereHas('accounts', function ($query) use ($filters) {
                $query->whereBetween('balance', [
                    $filters['balance']['from'],
                    $filters['balance']['to'],
                ]);
            });
        });
        $query->when(isset($filters['created_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['created_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['created_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('created_at', [$from, $to]);
        });
        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });
        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('title', 'ilike', '%' . $filters['search']['value'] . '%')
                ->orWhere('phone', 'ilike', '%' . $filters['search']['value'] . '%')
                ->orWhere('email', 'ilike', '%' . $filters['search']['value'] . '%')
                ->orWhere('code', 'ilike', '%' . $filters['search']['value'] . '%')
                ->orWhere('description', 'ilike', '%' . $filters['search']['value'] . '%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'code' => $query->whereNull('code'),
            'statuses' => $query->whereNull('status_id'),
            'title' => $query->whereNull('title'),
            'email' => $query->whereNull('email'),
            'phone' => $query->whereNull('phone'),
            'discount_card_number' => $query->whereNull('discount_card_number'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            'inn' => $query->whereHas('details', function ($query) {
                $query->whereNull('inn');
            }),
            'kpp' => $query->whereHas('details', function ($query) {
                $query->whereNull('kpp');
            }),
            'address' => function ($query) {
                $query->where(function ($query) {
                    $query->whereDoesntHave('address')
                        ->orWhereHas('address', function ($query) {
                            $query->whereRaw(
                                "postcode IS NULL AND country IS NULL AND city IS NULL AND region IS NULL AND house IS NULL
                                AND office IS NULL AND other IS NULL AND street IS NULL AND comment IS NULL"
                            );
                        });
                });
            },

            'groups' => $query->doesntHave('group'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'code' => $query->whereNotNull('code'),
            'statuses' => $query->whereNotNull('status_id'),
            'title' => $query->whereNotNull('title'),
            'email' => $query->whereNotNull('email'),
            'phone' => $query->whereNotNull('phone'),
            'discount_card_number' => $query->whereNotNull('discount_card_number'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            'inn' => $query->whereHas('details', function ($query) {
                $query->whereNotNull('inn');
            }),
            'kpp' => $query->whereHas('details', function ($query) {
                $query->whereNotNull('kpp');
            }),
            'address' => function ($query) {
                $query->whereHas('address', function ($query) {
                    $query->whereRaw(
                        "postcode IS NOT NULL OR country IS NOT NULL OR city IS NOT NULL OR region IS NOT NULL OR house IS NOT NULL
                        OR office IS NOT NULL OR other IS NOT NULL OR street IS NOT NULL OR comment IS NOT NULL"
                    );
                });
            },

            'groups' => $query->whereExists(function ($subQuery) {
                $subQuery->select(DB::raw(1))
                         ->from('contractor_group')
                         ->whereColumn('contractor_group.contractor_id', 'contractors.id');
            }),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'code' => $query->where('code', 'not like', '%' . $value . '%'),
            'statuses' => $query->whereNotIn('status_id', $value),
            'title' => $query->where('title', 'not like', '%' . $value . '%'),
            'email' => $query->where('email', 'not like', '%' . $value . '%'),
            'phone' => $query->where('phone', 'not like', '%' . $value . '%'),
            'discount_card_number' => $query->where('discount_card_number', 'not like', '%' . $value . '%'),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'inn' => $query->whereHas('details', function ($query) use ($value) {
                $query->where('inn', 'not like', '%' . $value . '%');
            }),
            'kpp' => $query->whereHas('details', function ($query) use ($value) {
                $query->where('kpp', '!=', $value);
            }),
            'address' => $query->whereHas('address', function ($query) use ($value) {
                $query->whereRaw(
                    "COALESCE(postcode, '') ||
                     ' ' ||
                     COALESCE(city, '') ||
                     ' ' ||
                     COALESCE(region, '')||
                     ' ' ||
                     COALESCE(house, '')||
                     ' ' ||
                     COALESCE(office, '')||
                     ' ' ||
                     COALESCE(other, '')||
                     ' ' ||
                     COALESCE(street, '') NOT ILIKE ?",
                    ['%' . $value . '%']
                );
            }),
            'groups' => $query->whereHas('group', function ($query) use ($value) {
                $query->whereNotIn('group_id', $value);
            }),
            default => null,
        };
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function show(string $id): ?object
    {

        $baseQuery = $this->getBaseContactQuery();
        $accountsQuery = $this->getAccountsQuery();
        $contactsQuery = $this->getContactsQuery();
        $groupsQuery = $this->getContractorGroupQuery();

        $contractor = DB::table(DB::raw("({$baseQuery->toSql()}) as c"))
                ->mergeBindings($baseQuery)
            ->leftJoin(DB::raw("({$accountsQuery->toSql()}) as ca"), 'c.id', '=', 'ca.contractor_id')
                ->mergeBindings($accountsQuery)
            ->leftJoin(DB::raw("({$contactsQuery->toSql()}) as cc"), 'c.id', '=', 'cc.contractor_id')
                ->mergeBindings($contactsQuery)
            ->leftJoin(DB::raw("({$groupsQuery->toSql()}) as ccg"), 'c.id', '=', 'ccg.contractor_id')
                ->mergeBindings($groupsQuery)
                ->select([
                    'c.*',
                    'ca.accounts',
                    'cc.contacts',
                    'ccg.contractor_groups'
                ])
                ->where('c.id', $id)
                ->first();

        return $contractor;
    }

    private function getContactsQuery(): \Illuminate\Database\Query\Builder
    {
        return DB::table('contractor_contacts as cc')
            ->select([
                'cc.contractor_id',
                DB::raw('
                    jsonb_agg(
                        jsonb_build_object(
                            \'id\', cc.id,
                            \'contractor_id\', cc.contractor_id,
                            \'full_name\', cc.full_name,
                            \'position\', cc.position,
                            \'phone\', cc.phone,
                            \'email\', cc.email,
                            \'comment\', cc.comment,
                            \'created_at\', cc.created_at,
                            \'updated_at\', cc.updated_at
                        )
                    ) as contacts
                ')
            ])
            ->groupBy('cc.contractor_id');
    }

    private function getAccountsQuery(): \Illuminate\Database\Query\Builder
    {
        return DB::table('contractor_accounts as ca')
            ->select([
                'ca.contractor_id',
                DB::raw('
                    jsonb_agg(
                        jsonb_build_object(
                            \'id\', ca.id,
                            \'is_main\', ca.is_main,
                            \'bik\', ca.bik,
                            \'correspondent_account\', ca.correspondent_account,
                            \'payment_account\', ca.payment_account,
                            \'bank\', ca.bank,
                            \'address\', ca.address,
                            \'created_at\', ca.created_at,
                            \'updated_at\', ca.updated_at
                        )
                    ) as accounts
                ')
            ])
            ->groupBy('ca.contractor_id');
    }

    private function getBaseContactQuery(): \Illuminate\Database\Query\Builder
    {
        $addressesSubquery = DB::table('contractor_detail_address AS cda')
           ->select('cda.contractor_detail_id')
           ->selectRaw('
                COALESCE(
                    (jsonb_object_agg(
                        \'data\',
                        jsonb_build_object(
                            \'postcode\', cda.postcode,
                            \'country\', cda.country,
                            \'region\', cda.region,
                            \'city\', cda.city,
                            \'street\', cda.street,
                            \'house\', cda.house,
                            \'office\', cda.office,
                            \'other\', cda.other,
                            \'comment\', cda.comment
                        )
                    ) FILTER (where cda.id is not null)) -> \'data\',
                    \'{}\'::jsonb
                ) as address
            ')
           ->groupBy('cda.contractor_detail_id');

        return DB::table('contractors as c')
           ->leftJoin('contractor_details as cd', 'cd.contractor_id', '=', 'c.id')
           ->leftJoinSub($addressesSubquery, 'cda', 'cd.id', '=', 'cda.contractor_detail_id')
           ->leftJoin('contractor_addresses as caa', 'caa.contractor_id', '=', 'c.id')
           ->leftJoin('file_relations as fr', 'c.id', '=', 'fr.related_id')
           ->leftJoin('files as f', 'f.id', '=', 'fr.file_id')
           ->select([
               'c.*',
               DB::raw('COALESCE(
                    (jsonb_object_agg(
                        \'data\',
                        jsonb_build_object(
                            \'taxation_type\', cd.taxation_type,
                            \'vat_rate_id\', cd.vat_rate_id,
                            \'type\', cd.type,
                            \'inn\', cd.inn,
                            \'kpp\', cd.kpp,
                            \'ogrn\', cd.ogrn,
                            \'okpo\', cd.okpo,
                            \'full_name\', cd.full_name,
                            \'firstname\', cd.firstname,
                            \'patronymic\', cd.patronymic,
                            \'lastname\', cd.lastname,
                            \'ogrnip\', cd.ogrnip,
                            \'certificate_number\', cd.certificate_number,
                            \'certificate_date\', cd.certificate_date,
                            \'certificate_date\', cd.certificate_date,
                            \'address\', COALESCE(cda.address, \'{}\'::jsonb)
                        )
                    ) FILTER (where cd.id is not null)) -> \'data\',
                    \'[]\'::jsonb
                ) AS detail'),
               DB::raw('COALESCE(
                    (jsonb_object_agg(
                        \'data\',
                        jsonb_build_object(
                            \'postcode\', caa.postcode,
                            \'country\', caa.country,
                            \'region\', caa.region,
                            \'city\', caa.city,
                            \'street\', caa.street,
                            \'house\', caa.house,
                            \'office\', caa.office,
                            \'other\', caa.other,
                            \'comment\', caa.comment
                        )
                    ) FILTER (where caa.id is not null)) -> \'data\',
                    \'[]\'::jsonb
                ) AS address'),
               DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', f.id,
                                    'name', f.name,
                                    'size', f.size,
                                    'path', f.path,
                                    'is_private', f.is_private
                                )
                            ) filter (where f.id is not null), '[]'
                        ) AS files
                    "),
           ])
           ->groupBy('c.id');
    }

    private function getContractorGroupQuery(): \Illuminate\Database\Query\Builder
    {

        return DB::table('contractor_group as ccg')
            ->leftJoin('contractor_groups as cg', 'ccg.group_id', '=', 'cg.id')
            ->select([
                'ccg.contractor_id',
                DB::raw('
                    COALESCE(
                        JSON_AGG(
                            JSON_BUILD_OBJECT(
                                \'group_id\', cg.id,
                                \'contractor_group_name\', cg.name
                            )
                        ),
                        \'[]\'
                    ) as contractor_groups
                ')
            ])
            ->groupBy(['ccg.contractor_id']);

    }

    public function getWhereInIds(array $ids): Collection
    {
        return DB::table(self::TABLE . ' as c')
            ->leftJoin('contractor_accounts as ca', 'ca.contractor_id', '=', 'c.id')
            ->leftJoin('contractor_addresses as caa', 'caa.contractor_id', '=', 'c.id')
            ->leftJoin('contractor_contacts as cc', 'cc.contractor_id', '=', 'c.id')
            ->leftJoin('contractor_details as cd', 'cd.contractor_id', '=', 'c.id')
            ->leftJoin('contractor_group as ccg', 'ccg.contractor_id', '=', 'c.id')
            ->leftJoin('contractor_groups as cg', 'cg.id', '=', 'ccg.group_id')
            ->whereIn('c.id', $ids)
            ->select([
                'c.*',
                DB::raw('coalesce(jsonb_agg(ca) filter (where ca.id is not null), \'[]\') AS accounts'),
                DB::raw('coalesce(jsonb_agg(caa) filter (where caa.id is not null), \'[]\') AS addresses'),
                DB::raw('coalesce(jsonb_agg(cc) filter (where cc.id is not null), \'[]\') AS contacts'),
                DB::raw('coalesce(jsonb_agg(cd) filter (where cd.id is not null), \'[]\') AS details'),
                DB::raw('coalesce(jsonb_agg(cg) filter (where cg.id is not null), \'[]\') AS contractor_groups'),
            ])
            ->groupBy('c.id')
            ->get();
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }
    private function getEntity(): ContractorEntity
    {
        return new ContractorEntity();
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'code' => $query->where('code', 'ilike', '%' . $value . '%'),
            'statuses' => $query->whereIn('status_id', $value),
            'title' => $query->where('title', 'ilike', '%' . $value . '%'),
            'email' => $query->where('email', 'ilike', '%' . $value . '%'),
            'phone' => $query->where('phone', 'like', '%' . $value . '%'),
            'discount_card_number' => $query->where('discount_card_number', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'inn' => $query->whereHas('details', function ($query) use ($value) {
                $query->where('inn', 'ilike', '%' . $value . '%');
            }),
            'kpp' => $query->whereHas('details', function ($query) use ($value) {
                $query->where('kpp', $value);
            }),
            'address' => $query->whereHas('address', function ($query) use ($value) {
                $query->whereRaw(
                    "COALESCE(postcode, '') ||
                     ' ' ||
                     COALESCE(city, '') ||
                     ' ' ||
                     COALESCE(region, '')||
                     ' ' ||
                     COALESCE(house, '')||
                     ' ' ||
                     COALESCE(office, '')||
                     ' ' ||
                     COALESCE(other, '')||
                     ' ' ||
                     COALESCE(street, '') ILIKE ?",
                    ['%' . $value . '%']
                );
            }),
            'groups' => $query->whereHas('group', function ($subQuery) use ($value) {
                $subQuery->whereIn('group_id', $value);
            }),
            default => null,
        };
    }
}
