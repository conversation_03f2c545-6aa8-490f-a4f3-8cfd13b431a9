<?php

namespace App\Repositories\User;

use App\Contracts\Repositories\UserViewSettingsRepositoryContract;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class UserViewSettingsRepository implements UserViewSettingsRepositoryContract
{
    protected const TABLE = 'user_view_settings';

    public function get(string $id, ?string $name = null): Collection
    {
        return DB::table(self::TABLE)
            ->where('employee_id', $id)
            ->when($name, fn (Builder $query) => $query->where('name', $name))
            ->get();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['name', 'employee_id', 'cabinet_id'],
                ['settings']
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }
}
