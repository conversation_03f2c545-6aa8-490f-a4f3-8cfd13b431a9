<?php

namespace App\Repositories\References\MeasurementUnits;

use App\Contracts\Repositories\MeasurementUnitGroupsRepositoryContract;
use App\Entities\MeasurementUnitGroupEntity;
use App\Traits\HasTimestamps;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MeasurementUnitGroupsRepository implements MeasurementUnitGroupsRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'measurement_unit_groups';

    public function findById(string $id, string $cabinetId): ?object
    {
        return DB::table('measurement_unit_groups')
            ->where('id', $id)
            ->where(function (Builder $query) use ($cabinetId) {
                $query->where('cabinet_id', $cabinetId)
                    ->orWhereNull('cabinet_id');
            })
            ->first();
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields);
        $query->where(function ($query) use ($id) {
            $query->where('cabinet_id', $id)
                ->where('is_system', false)
                ->orWhere(function ($subQuery) use ($id) {
                    $subQuery->where('is_system', true)
                        ->whereNotExists(function ($subSubQuery) use ($id) {
                            $subSubQuery->select(DB::raw(1))
                                ->from('cabinet_measurement_system_groups')
                                ->whereColumn('system_group_id', 'measurement_unit_groups.id')
                                ->where('cabinet_measurement_system_groups.cabinet_id', $id);
                        });
                });
        });

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('name', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)->where('id', $id)->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table('measurement_unit_groups as mug')
            ->where('mug.id', $id)
            ->first();

    }

    private function getEntity(): MeasurementUnitGroupEntity
    {
        return new MeasurementUnitGroupEntity();
    }
}
