<?php

namespace App\Repositories\References;

use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\VatRateEntity;
use App\Enums\Api\Internal\EntitiesTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class VatRatesRepository implements VatRatesRepositoryContract
{
    use SoftDeletable;
    use HasTimestamps;
    use Archivable;

    protected const TABLE = 'vat_rates';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);
        $query->select($baseFields)
            ->where('deleted_at', null);

        $query->where(function ($query) use ($filters, $id) {
            switch ($filters['type']['value'] ?? null) {
                case EntitiesTypeEnum::SYSTEM->value:
                    $query->where('cabinet_id', null);
                    break;
                case EntitiesTypeEnum::CUSTOM->value:
                    $query->where('cabinet_id', $id);
                    break;
                default:
                    $query->where(function ($q) use ($id) {
                        $q->where('cabinet_id', $id)
                          ->orWhere('cabinet_id', null);
                    });
                    break;
            }
        });

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::VAT_RATES->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['search']['value']),
            function ($query) use ($filters) {
                $query->where(function ($q) use ($filters) {
                    $q->where('rate', 'ilike', '%' . $filters['search']['value'] . '%')
                      ->orWhere('description', 'ilike', '%' . $filters['search']['value'] . '%');
                });
            }
        );

        $query->when(
            isset($filters['show_only']['value']),
            function ($query) use ($filters) {
                if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                    return $query->where('archived_at', '!=', null);
                }

                if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                    return $query->whereNull('archived_at');
                }

                return $query;
            }
        );

        $query->when(
            isset($filters['rate']['value']),
            fn ($query) => $query->where('rate', $filters['rate']['value'])
        );

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }
    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    public function getEntity(): VatRateEntity
    {
        return new VatRateEntity();
    }

    public function getByRateAndCabinet(int $rate, string $cabinetId): ?object
    {
        return DB::table(self::TABLE)
            ->where('rate', $rate)
            ->where('cabinet_id', $cabinetId)
            ->whereNull('deleted_at')
            ->whereNull('archived_at')
            ->first();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'description' => $query->whereNull('description'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'description' => $query->whereNotNull('description'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'description' => $query->where('description', 'not like', '%' . $value . '%'),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'description' => $query->where('description', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            default => null,
        };
    }
}
