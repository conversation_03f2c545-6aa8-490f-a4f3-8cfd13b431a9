<?php

namespace App\Repositories\References\SalesChannels;

use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\SalesChannelEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SalesChannelsRepository implements SalesChannelsRepositoryContract
{
    use HasTimestamps;
    use Archivable;
    use SoftDeletable;

    private const TABLE = 'sales_channels';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }
    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id)
            ->where('deleted_at', null);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::SALE_CHANNELS->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['is_common']['value']),
            fn ($query) => $query->where('is_common', $filters['is_common']['value'])
        );
        $query->when(
            isset($filters['name']['value']),
            fn ($query) => $query->where('name', 'ilike', '%' . $filters['name']['value'] . '%')
        );
        $query->when(
            isset($filters['type']['value']),
            fn ($query) => $query->where('sales_channel_type_id', $filters['type']['value'])
        );

        $query->when(
            isset($filters['show_only']['value']),
            function ($query) use ($filters) {
                if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                    return $query->where('archived_at', '!=', null);
                }

                if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                    return $query->whereNull('archived_at');
                }

                return $query;
            }
        );

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = \Illuminate\Support\Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });
        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('name', 'ilike', '%' . $filters['search']['value'] . '%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    private function getEntity(): SalesChannelEntity
    {
        return new SalesChannelEntity();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'description' => $query->whereNull('description'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'description' => $query->whereNotNull('description'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'description' => $query->where('description', 'not like', '%' . $value . '%'),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'description' => $query->where('description', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->where('department_id', $value),
            default => null,
        };
    }
}
