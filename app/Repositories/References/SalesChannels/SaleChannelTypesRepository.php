<?php

namespace App\Repositories\References\SalesChannels;

use App\Contracts\Repositories\SaleChannelTypesRepositoryContract;
use DB;
use Illuminate\Support\Collection;

class SaleChannelTypesRepository implements SaleChannelTypesRepositoryContract
{
    private const TABLE = 'sales_channel_types';

    public function get(array $fields = ['*']): Collection
    {
        return DB::table(self::TABLE)
            ->select($fields)
            ->get();
    }
}
