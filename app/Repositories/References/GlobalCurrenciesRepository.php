<?php

namespace App\Repositories\References;

use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\Entities\GlobalCurrencyEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GlobalCurrenciesRepository implements GlobalCurrenciesRepositoryContract
{
    use HasTimestamps;
    protected const TABLE = 'global_currencies';

    public function getAll(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where(
                fn ($query) => $query->where('short_name', 'ilike', '%'.$filters['search']['value'].'%')
                    ->orWhere('name', 'ilike', '%'.$filters['search']['value'].'%')
                    ->orWhere('num_code', 'ilike', '%'.$filters['search']['value'].'%')
                    ->orWhere('char_code', 'ilike', '%'.$filters['search']['value'].'%')
            );
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function firstById(string $resourceId): ?object
    {
        return DB::table('global_currencies')
            ->where('id', $resourceId)
            ->first();
    }

    public function insert(array $data): void
    {
        $data = $this->setTimestamps($data);

        DB::table('global_currencies')->insert($data);
    }
    public function getByExternalId(string $externalId): ?object
    {
        return DB::table(self::TABLE)
            ->where('external_id', $externalId)
            ->first();
    }

    private function getEntity(): GlobalCurrencyEntity
    {
        return new GlobalCurrencyEntity();
    }
}
