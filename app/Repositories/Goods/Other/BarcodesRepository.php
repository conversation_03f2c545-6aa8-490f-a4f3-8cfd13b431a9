<?php

namespace App\Repositories\Goods\Other;

use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Entities\BarcodeEntity;
use App\Enums\Api\Internal\BarcodeEnum;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BarcodesRepository implements BarcodesRepositoryContract
{
    use HasOrderedUuid;
    use HasBarcodes;

    private const TABLE = 'barcodes';

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('value', 'ILIKE', '%' . $filters['search']['value'] . '%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function getWhereBarcodableId(?string $id = null): Collection
    {
        return DB::table(self::TABLE)
            ->where('barcodable_id', $id)
            ->get();
    }

    public function getExistsInDb(int $code, string $cabinetId, string $barcode): bool
    {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->where('type', $code)
            ->where('value', $barcode)
            ->exists();
    }

    public function getMaxBarcode(int $type, string $cabinetId, int $length): ?int
    {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->where('type', $type)
            ->where('is_generated', true)
            ->whereRaw('CHAR_LENGTH(value) = ?', $length)
            ->max('value');
    }

    public function getAllBarcodesWhereInValue(array $codes, string $cabinetId): ?Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('value', $codes)
            ->where('cabinet_id', $cabinetId)
            ->pluck('value');
    }

    public function getByValueAndCabinet(string $value, string $cabinetId): ?object
    {
        return DB::table(self::TABLE)
            ->where('value', $value)
            ->where('cabinet_id', $cabinetId)
            ->first();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function upsert(array $barcodes): void
    {
        DB::table(self::TABLE)->upsert(
            $barcodes,
            ['id'],
            [
                'value',
                'type',
                'is_generated',
                'sort',
            ]
        );
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)->where('id', $id)->delete();
    }

    public function deleteByIds(array $id): int
    {
        return DB::table(self::TABLE)->whereIn('id', $id)->delete();
    }

    public function deleteOldBarcodesWhereBarcoddableId(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('barcodable_id', $id)
            ->delete();
    }

    public function deleteOldBarcodes(string $id, array $incomingBarcodeIds): int
    {
        return DB::table(self::TABLE)
            ->where('barcodable_id', $id)
            ->when(!empty($incomingBarcodeIds), function ($query) use ($incomingBarcodeIds) {
                $query->whereNotIn('id', $incomingBarcodeIds);
            })
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function getMaxInnerCodeByCabinet(string $cabinetId): ?int
    {
        return DB::table(DB::raw("
                (SELECT inner_code FROM products WHERE cabinet_id = :cabinetId
                UNION
                SELECT inner_code FROM packings WHERE cabinet_id = :cabinetId) AS combined
            "))
            ->setBindings(['cabinetId' => $cabinetId])
            ->max('inner_code');
    }

    public function getMaxCodeWithMaxInnerCodeForProductAndPackingsFirst(int $maxInnerCode): ?object
    {
        return  DB::table('products')
            ->select('products.code')
            ->where('products.inner_code', $maxInnerCode)
            ->union(
                DB::table('packings')
                    ->select('packings.code')
                    ->where('packings.inner_code', $maxInnerCode)
            )
            ->first();
    }


    public function getMaxBarcodeEAN13ByCabinet(string $cabinetId): ?int
    {
        return DB::table('barcodes')
            ->where('cabinet_id', $cabinetId)
            ->where('type', BarcodeEnum::EAN13->value)
            ->max('barcode');
    }


    public function getMaxBarcodeEANByCabinetFirst(string $cabinetId, int $maxBarcode): ?object
    {
        return DB::table('barcodes')
            ->where('cabinet_id', $cabinetId)
            ->where('barcode', $maxBarcode)
            ->first('barcode');
    }


    public function getMaxBarcodeEAN8ByCabinet(string $cabinetId): ?int
    {
        return DB::table('barcodes')
            ->where('cabinet_id', $cabinetId)
            ->where('type', BarcodeEnum::EAN8->value)
            ->max('barcode');
    }

    public function getWhereInBarcodableIds(array $ids): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('barcodable_id', $ids)
            ->get('id');
    }

    private function getEntity(): BarcodeEntity
    {
        return new BarcodeEntity();
    }
}
