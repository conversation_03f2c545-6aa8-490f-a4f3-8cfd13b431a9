<?php

namespace App\Repositories\Goods\Transfers;

use App\Contracts\Repositories\GoodsTransferWarehouseItemRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GoodsTransferWarehouseItemRepository implements GoodsTransferWarehouseItemRepositoryContract
{
    private const TABLE = 'goods_transfer_warehouse_items';

    public function deleteWhereInIds(array $ids): void
    {
        DB::table(self::TABLE)
            ->whereIn('goods_transfer_item_id', $ids)
            ->delete();
    }

    public function insert(array $data): void
    {
        DB::table(self::TABLE)->insert($data);
    }

    public function getTransferItemsByWarehouseItem(string $warehouseItemId): Collection
    {
        return DB::table('goods_transfer_warehouse_items as gtwi')
            ->join('goods_transfer_items as gti', 'gtwi.goods_transfer_item_id', '=', 'gti.id')
            ->where('gtwi.warehouse_item_id', $warehouseItemId)
            ->select('gtwi.goods_transfer_item_id', 'gti.total_price', 'gti.quantity', 'gti.goods_transfer_id')
            ->get();
    }

    public function calculateTransferCostsByItems(array $transferItemIds): Collection
    {
        return DB::table('goods_transfer_warehouse_items as gtwi')
            ->join('warehouse_items as wi', 'gtwi.warehouse_item_id', '=', 'wi.id')
            ->whereIn('gtwi.goods_transfer_item_id', $transferItemIds)
            ->groupBy('gtwi.goods_transfer_item_id')
            ->select('gtwi.goods_transfer_item_id', DB::raw('SUM(wi.unit_price * gtwi.quantity) as new_cost'))
            ->get()
            ->keyBy('goods_transfer_item_id');
    }
}
