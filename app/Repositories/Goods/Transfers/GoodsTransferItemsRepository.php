<?php

namespace App\Repositories\Goods\Transfers;

use App\Contracts\Repositories\GoodsTransferItemsRepositoryContract;
use App\Entities\GoodsTransferItemEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GoodsTransferItemsRepository implements GoodsTransferItemsRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'goods_transfer_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge(
                $data,
                ['updated_at' => Carbon::now()]
            ));
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $entity = new GoodsTransferItemEntity();
        [$baseFields, $relationFields] = $entity->parseFields($fields);

        $query = $entity;
        $query->select($baseFields)
            ->where('goods_transfer_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function getTransferDetails(string $resourceId, array $select = ['*']): ?object
    {
        return DB::table('goods_transfer_items')
            ->join('goods_transfers', 'goods_transfers.id', '=', 'goods_transfer_items.goods_transfer_id')
            ->where('goods_transfer_items.id', $resourceId)
            ->select($select)
            ->first();
    }

    public function getBulkTransferDetails(array $resourceIds, array $select = ['*']): Collection
    {
        // Преобразуем формат идентификаторов, если они приходят в формате объектов с goods_transfer_item_id
        $ids = [];
        foreach ($resourceIds as $id) {
            if (is_object($id) && isset($id->goods_transfer_item_id)) {
                $ids[] = $id->goods_transfer_item_id;
            } else {
                $ids[] = $id;
            }
        }

        return DB::table('goods_transfer_items')
            ->join('goods_transfers', 'goods_transfers.id', '=', 'goods_transfer_items.goods_transfer_id')
            ->whereIn('goods_transfer_items.id', $ids)
            ->select($select)
            ->get();
    }

    public function getTransferItems(string $productId, string $date, string $warehouseId): ?Collection
    {
        return DB::table('goods_transfer_items')
            ->join('goods_transfers', 'goods_transfers.id', '=', 'goods_transfer_items.goods_transfer_id')
            ->where('goods_transfer_items.product_id', $productId)
            ->where('goods_transfers.date_from', '>=', $date)
            ->where('goods_transfers.from_warehouse_id', $warehouseId)
            ->select(['goods_transfer_items.*', 'goods_transfers.date_from as transfer_date', 'goods_transfers.held'])
            ->orderBy('goods_transfers.date_from', 'asc')
            ->get();
    }

    public function getBulkTransferItems(
        array $productIds,
        string $transferDate,
        string $warehouseId
    ): Collection {
        return DB::table('goods_transfer_items')
            ->join('goods_transfers', 'goods_transfers.id', '=', 'goods_transfer_items.goods_transfer_id')
            ->whereIn('goods_transfer_items.product_id', $productIds)
            ->where('goods_transfers.date_from', '>=', $transferDate)
            ->where('goods_transfers.from_warehouse_id', $warehouseId)
            ->select(['goods_transfer_items.*', 'goods_transfers.date_from as transfer_date', 'goods_transfers.held'])
            ->orderBy('goods_transfers.date_from', 'asc')
            ->get();
    }

    public function getItemsByTransferId(string $transferId): Collection
    {
        return DB::table('goods_transfer_items')
            ->where('goods_transfer_id', $transferId)
            ->select('id as goods_transfer_item_id')
            ->get();
    }

    public function bulkUpdate(array $updates): void
    {
        foreach ($updates as $update) {
            DB::table(self::TABLE)
                ->where('id', $update['id'])
                ->update(array_merge(
                    $update['data'],
                    ['updated_at' => Carbon::now()]
                ));
        }
    }

    public function bulkDelete(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }
}
