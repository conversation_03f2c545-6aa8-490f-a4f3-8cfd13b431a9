<?php

namespace App\Repositories\Goods\Transfers;

use App\Contracts\Repositories\GoodsTransferRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\GoodsTransferEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\HasTimestamps;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GoodsTransferRepository implements GoodsTransferRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'goods_transfers';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::MOVEMENTS->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['is_common']['value']),
            fn ($query) => $query->where('is_common', $filters['is_common']['value'])
        );

        $query->when(
            isset($filters['is_held']['value']),
            fn ($query) => $query->where('held', $filters['is_held']['value'])
        );

        $query->when(
            isset($filters['search']['value']),
            function ($query) use ($filters) {
                $query->where('number', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('comment', 'ilike', '%' . $filters['search']['value'] . '%');
            }
        );

        $query->when(isset($filters['period']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('created_at', [$from, $to]);
        });

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE . ' as gt')
            ->leftJoin('statuses as s', 'gt.status_id', '=', 's.id')
            ->join('legal_entities as le', 'gt.legal_entity_id', '=', 'le.id')
            ->join('warehouses as tw', 'gt.to_warehouse_id', '=', 'tw.id')
            ->join('warehouses as fw', 'gt.from_warehouse_id', '=', 'fw.id')
            //->leftJoin('acceptance_items as ci', 'gt.id', '=', 'ci.acceptance_id')
            ->leftJoin('file_relations as fr', 'gt.id', '=', 'fr.related_id')
            ->leftJoin('files as f', 'fr.file_id', '=', 'f.id')
            ->select([
                'gt.*',
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', s.id,
                                    'name', s.name
                                )
                            ) filter (where s.id is not null), '[]'
                        ) AS status
                    "),
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', tw.id,
                                    'name', tw.name
                                )
                            ) filter (where tw.id is not null), '[]'
                        ) AS to_warehouse
                    "),
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', fw.id,
                                    'name', fw.name
                                )
                            ) filter (where fw.id is not null), '[]'
                        ) AS from_warehouse
                    "),
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', f.id,
                                    'name', f.name,
                                    'path', f.path,
                                    'is_private', f.is_private
                                )
                            ) filter (where f.id is not null), '[]'
                        ) AS files
                    "),
            ])
            ->where('gt.id', $id)
            ->groupBy(['gt.id', 's.name', 'tw.name', 'fw.name'])
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge(
                $data,
                ['updated_at' => Carbon::now()]
            ));
    }

    public function getEntity(): GoodsTransferEntity
    {
        return new GoodsTransferEntity();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'statuses' => $query->whereNull('status_id'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'statuses' => $query->whereNotNull('status_id'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'to_warehouse' => $query->whereNotIn('to_warehouse_id', $value),
            'from_warehouse' => $query->whereNotIn('from_warehouse_id', $value),
            'in_warehouse' => $query->where(function ($query) use ($value) {
                $query->whereNotIn('to_warehouse_id', $value)
                    ->whereNotIn('from_warehouse_id', $value);
            }),
            'legal_entity' => $query->whereNotIn('legal_entity_id', $value),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'statuses' => $query->where(function ($query) use ($value) {
                $query->whereNotIn('status_id', $value)
                    ->orWhere('status_id', null);
            }),
            'products' => $query->whereHas('items', function ($query) use ($value) {
                $query->whereNotIn('product_id', $value);
            }),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'to_warehouse' => $query->whereIn('to_warehouse_id', $value),
            'from_warehouse' => $query->whereIn('from_warehouse_id', $value),
            'in_warehouse' => $query->where(function ($query) use ($value) {
                $query->whereIn('to_warehouse_id', $value)
                    ->orWhereIn('from_warehouse_id', $value);
            }),
            'legal_entity' => $query->whereIn('legal_entity_id', $value),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'statuses' => $query->whereIn('status_id', $value),
            'products' => $query->whereHas('goods_transfer_items', function ($query) use ($value) {
                $query->whereIn('product_id', $value);
            }),
            default => null,
        };
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getFirst(string $resourceId): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->first();
    }
}
