<?php

namespace App\Repositories\Goods\Products;

use App\Contracts\Repositories\ProductsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\ProductEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\SoftDeletable;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProductsRepository implements ProductsRepositoryContract
{
    use SoftDeletable;
    private const TABLE = 'products';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::GOODS_AND_SERVICES->value, $query::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where(function ($query) use ($filters) {
                $query->where('title', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('code', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('article', 'ilike', '%' . $filters['search']['value'] . '%');
            });
        });

        $query->when(
            isset($filters['shared_access']['value']),
            fn ($query) => $query->where('shared_access', '=', $filters['shared_access']['value'])
        );
        $query->when(isset($filters['show_only']), function ($query) use ($filters) {
            if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                return $query->where('archived_at', '!=', null);
            }

            if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                return $query->whereNull('archived_at');
            }

            return $query;
        });

        return $query
        ->when($sortField, function ($query) use ($sortField, $sortDirection) {
            return $query->sort($sortField, $sortDirection);
        })
        ->paginate($perPage, $page)
        ->get();

    }

    private function getEntity(): ProductEntity
    {
        return new ProductEntity();
    }

    public function getMyCountWhereInIds(array|Collection $ids, string $cabinetId): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->where('cabinet_id', $cabinetId)
            ->count();
    }

    public function getFirst(string $resourceId): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->first();
    }


    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        $result = match ($filter) {
            'title' => $query->whereNull('title'),
            'description' => $query->whereNull('description'),
            'article' => $query->whereNull('article'),
            'code' => $query->whereNull('code'),
            'external_code' => $query->whereNull('external_code'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            'barcode' => function ($query) {
                $query->whereNotExists(function ($subQuery) {
                    $subQuery->select(DB::raw(1))
                             ->from('barcodes')
                             ->whereColumn('barcodes.barcodable_id', 'products.id');
                });
            },
            'type' => $query->whereNull('type'),
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        $result = match ($filter) {
            'title' => $query->whereNotNull('title'),
            'description' => $query->whereNotNull('description'),
            'article' => $query->whereNotNull('article'),
            'code' => $query->whereNotNull('code'),
            'external_code' => $query->whereNotNull('external_code'),
            'pack_type' => $query->whereNotNull('pack_type'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            'barcode' => function ($query) {
                $query->whereExists(function ($subQuery) {
                    $subQuery->select(DB::raw(1))
                             ->from('barcodes')
                             ->whereColumn('barcodes.barcodable_id', 'products.id');
                });
            },
            'type' => $query->whereNotNull('type'),
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        $result = match ($filter) {
            'title' => $query->where('title', 'not like', '%' . $value . '%'),
            'description' => $query->where(function ($q) use ($value) {
                $q->where('description', 'not like', '%' . $value . '%')
                  ->orWhereNull('description');
            }),
            'article' => $query->where(function ($q) use ($value) {
                $q->where('article', 'not like', '%' . $value . '%')
                  ->orWhereNull('article');
            }),
            'code' => $query->where(function ($q) use ($value) {
                $q->where('code', 'not like', '%' . $value . '%')
                  ->orWhereNull('code');
            }),
            'external_code' => $query->where(function ($q) use ($value) {
                $q->where('external_code', 'not like', '%' . $value . '%')
                  ->orWhereNull('external_code');
            }),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'barcode' => function ($query) use ($value) {
                $query->whereNotExists(function ($subQuery) use ($value) {
                    $subQuery->select(DB::raw(1))
                             ->from('barcodes')
                             ->whereColumn('barcodes.barcodable_id', 'products.id')
                             ->where('barcodes.value', 'ilike', '%' . $value . '%');
                });
            },
            'type' => $query->where('type', '!=', $value),
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        $result = match ($filter) {
            'title' => $query->where('title', 'ilike', '%' . $value . '%'),
            'type' => $query->where('type', 'ilike', '%' . $value . '%'),
            'description' => $query->where('description', 'ilike', '%' . $value . '%'),
            'article' => $query->where('article', 'ilike', '%' . $value . '%'),
            'code' => $query->where('code', 'ilike', '%' . $value . '%'),
            'external_code' => $query->where('external_code', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'barcode' => function ($query) use ($value) {
                $query->whereExists(function ($subQuery) use ($value) {
                    $subQuery->select(DB::raw(1))
                             ->from('barcodes')
                             ->whereColumn('barcodes.barcodable_id', 'products.id')
                             ->where('barcodes.value', 'ilike', '%' . $value . '%');
                });
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert(array_merge(
                $data,
                ['created_at' => now(), 'updated_at' => now()]
            ));
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => now()]
                )
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)->where('id', $id)->delete();
    }


    public function show(string $id): ?object
    {
        $product = DB::table('products as p')
            ->leftJoin('product_categories as categories', 'p.category_id', '=', 'categories.id')
            ->leftJoin('measurement_units as units', 'p.measurement_unit_id', '=', 'units.id')
            ->leftJoin('product_egais_codes as egais', 'p.id', '=', 'egais.product_id')
            ->leftJoin('product_accounting_features as features', 'p.id', '=', 'features.product_id')
            ->leftJoin('brands', 'p.brand_id', '=', 'brands.id')
            ->leftJoin('product_groups', 'p.product_group_id', '=', 'product_groups.id')
            ->leftJoin('contractors', 'contractors.id', '=', 'p.contractor_id')
            ->leftJoin('countries', 'countries.id', '=', 'p.country_id')
            ->leftJoin('product_images as images', 'images.product_id', '=', 'p.id')
            ->select([
                'p.*',
                DB::raw("STRING_AGG(DISTINCT egais.code, ', ') AS egais_codes"),

                // Изображения
                DB::raw('COALESCE(
                    jsonb_agg(DISTINCT images) FILTER (WHERE images.id IS NOT NULL),
                    \'[]\'::jsonb
                ) AS images'),

                // Штрих-коды товара
                DB::raw('COALESCE((
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            \'id\', b.id,
                            \'value\', b.value,
                            \'type\', b.type,
                            \'sort\', b.sort
                        )
                    )
                    FROM barcodes b
                    WHERE b.barcodable_id = p.id AND b.barcodable_type = \'products\'
                ), \'[]\'::jsonb) as barcodes'),

                // Упаковки с их штрих-кодами
                DB::raw('COALESCE((
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            \'id\', pp.id,
                            \'quantity\', pp.quantity,
                            \'measurement_unit_quantity_id\', pp.measurement_unit_quantity_id,
                            \'packing_id\', pp.packing_id,
                            \'barcodes\', COALESCE((
                                SELECT jsonb_agg(
                                    jsonb_build_object(
                                        \'id\', b.id,
                                        \'barcode\', b.value,
                                        \'type\', b.type,
                                        \'sort\', b.sort
                                    )
                                )
                                FROM barcodes b
                                WHERE b.barcodable_id = pp.id AND b.barcodable_type = \'product_packing\'
                            ), \'[]\'::jsonb)
                        )
                    )
                    FROM product_packing pp
                    WHERE pp.product_id = p.id
                ), \'[]\'::jsonb) as packings'),

                // Цены
                DB::raw('COALESCE((
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            \'cp_id\', cp.id,
                            \'cp_title\', cp.name,
                            \'amount\', pr.amount,
                            \'char_code\', cc.char_code
                        ) ORDER BY cp.sort ASC
                    )
                    FROM product_prices pr
                    LEFT JOIN cabinet_prices cp ON cp.id = pr.cabinet_price_id
                    LEFT JOIN cabinet_currencies cc ON pr.currency_id = cc.id
                    WHERE pr.product_id = p.id
                ), \'[]\'::jsonb) as product_prices'),

                // Атрибуты
                DB::raw('COALESCE((
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            \'atr_id\', av.id,
                            \'atr_name\', a.name,
                            \'atr_val\', av.value,
                            \'atr_gr\', ag.name
                        ) ORDER BY a.name
                    )
                    FROM product_attributes pa
                    LEFT JOIN attributes a ON a.id = pa.attribute_id
                    LEFT JOIN attribute_groups ag ON ag.id = a.attribute_groups_id
                    LEFT JOIN attribute_values av ON av.attribute_id = a.id
                    WHERE pa.product_id = p.id
                ), \'[]\'::jsonb) as product_attribute'),

                // Категория
                DB::raw('CASE
                    WHEN categories.id IS NOT NULL THEN jsonb_build_object(
                        \'id\', categories.id,
                        \'name\', categories.name
                    ) ELSE \'{}\'::jsonb
                END as categories'),

                // Единица измерения
                DB::raw('CASE
                    WHEN units.id IS NOT NULL THEN jsonb_build_object(
                        \'id\', units.id,
                        \'name\', units.name
                    ) ELSE \'{}\'::jsonb
                END AS measurement_unit'),

                // Бренд
                DB::raw('CASE
                    WHEN brands.id IS NOT NULL THEN jsonb_build_object(
                        \'id\', brands.id,
                        \'name\', brands.name
                    ) ELSE \'{}\'::jsonb
                END as brand'),

                // Контрагент
                DB::raw('CASE
                    WHEN contractors.id IS NOT NULL THEN jsonb_build_object(
                        \'id\', contractors.id,
                        \'title\', contractors.title
                    ) ELSE \'{}\'::jsonb
                END AS contractor'),

                // Страна
                DB::raw('CASE
                    WHEN countries.id IS NOT NULL THEN jsonb_build_object(
                        \'id\', countries.id,
                        \'name\', countries.name
                    ) ELSE \'{}\'::jsonb
                END AS country'),

                // Группа товаров
                DB::raw('CASE
                    WHEN product_groups.id IS NOT NULL THEN jsonb_build_object(
                        \'id\', product_groups.id,
                        \'name\', product_groups.name
                    ) ELSE \'{}\'::jsonb
                END as product_group'),

                // Особенности учета
                DB::raw('jsonb_build_object(
                    \'pack_type\', features.pack_type,
                    \'type_accounting\', features.type_accounting,
                    \'accounting_series\', features.accounting_series,
                    \'product_siz_name_id\', features.product_siz_name_id,
                    \'product_siz_type_id\', features.product_siz_type_id,
                    \'product_type_code\', features.product_type_code,
                    \'container_capacity\', features.container_capacity,
                    \'strength\', features.strength,
                    \'excise\', features.excise,
                    \'tnwed_id\', features.tnwed_id,
                    \'target_gender\', features.target_gender,
                    \'type_production\', features.type_production,
                    \'age_category\', features.age_category,
                    \'set\', features.set,
                    \'partial_sale\', features.partial_sale,
                    \'model\', features.model,
                    \'traceable\', features.traceable
                ) as accounting_features')
            ])
            ->where('p.id', $id)
            ->groupBy([
                'p.id', 'categories.id', 'units.id', 'brands.id', 'contractors.id', 'countries.id', 'product_groups.id', 'features.pack_type',
                'features.type_accounting', 'features.accounting_series', 'features.product_siz_name_id', 'features.product_siz_type_id',
                'features.product_type_code', 'features.container_capacity', 'features.strength', 'features.excise',
                'features.tnwed_id','features.target_gender', 'features.type_production', 'features.age_category',
                'features.set', 'features.partial_sale', 'features.model', 'features.traceable'
            ])
            ->first();

        if (!$product) {
            return null;
        }

        return $this->transformProduct($product);
    }

    private function transformProduct(object $product): object
    {
        // Декодируем JSON данные
        $product->packings = json_decode($product->packings ?? '[]', true);
        $product->product_prices = json_decode($product->product_prices ?? '[]', true);
        $product->product_attribute = json_decode($product->product_attribute ?? '[]', true);
        $product->accounting_features = json_decode($product->accounting_features ?? '{}', true);
        $product->categories = json_decode($product->categories ?? '{}', true);
        $product->measurement_unit = json_decode($product->measurement_unit ?? '{}', true);
        $product->brand = json_decode($product->brand ?? '{}', true);
        $product->contractor = json_decode($product->contractor ?? '{}', true);
        $product->country = json_decode($product->country ?? '{}', true);
        $product->product_group = json_decode($product->product_group ?? '{}', true);
        $product->barcodes = json_decode($product->barcodes ?? '[]', true);
        $product->images = json_decode($product->images ?? '[]', true);

        return $product;
    }



    private function getBaseProductGroupByFields(): array
    {
        return [
            'p.id', 'pi.url', 'p.title', 'p.description', 'p.type', 'p.code',
            'p.inner_code', 'p.external_code', 'p.discounts_retail_sales',
            'p.short_description', 'p.country_id', 'cntr.name', 'p.length',
            'p.width', 'p.height', 'p.weight', 'p.volume', 'p.tax_id',
            'p.tax_system', 'p.indication_subject_calculation', 'mu.name',
            'paf.pack_type', 'paf.type_accounting', 'paf.accounting_series',
            'paf.product_siz_name_id', 'paf.product_siz_type_id',
            'paf.product_type_code', 'paf.container_capacity', 'paf.strength',
            'paf.excise', 'paf.tnwed_id', 'paf.target_gender', 'paf.type_production',
            'paf.age_category', 'paf.set', 'paf.partial_sale', 'paf.model',
            'paf.traceable', 'brands.name', 'p.created_at', 'p.updated_at',
            'p.product_group_id', 'ca.name', 'co.title'
        ];
    }

}
