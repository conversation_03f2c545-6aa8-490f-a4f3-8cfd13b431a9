<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseReservationsRepository implements WarehouseReservationsRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'warehouse_reservations';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function bulkInsert(array $items): bool
    {
        $items = array_map(fn($item) => $this->setTimestamps($item), $items);
        return DB::table(self::TABLE)->insert($items);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge($data, ['updated_at' => Carbon::now()]));
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getReservationsByOrderItem(string $orderItemId): Collection
    {
        return DB::table(self::TABLE)
            ->where('customer_order_item_id', $orderItemId)
            ->where('status', 'reserved')
            ->get();
    }

    public function getReservationsByWarehouseItem(string $warehouseItemId): Collection
    {
        return DB::table(self::TABLE)
            ->where('warehouse_item_id', $warehouseItemId)
            ->where('status', 'reserved')
            ->get();
    }

    public function getTotalReservedQuantity(string $warehouseItemId): int
    {
        return DB::table(self::TABLE)
            ->where('warehouse_item_id', $warehouseItemId)
            ->where('status', 'reserved')
            ->sum('reserved_quantity');
    }

    public function cancelReservationsByOrderItem(string $orderItemId): int
    {
        return DB::table(self::TABLE)
            ->where('customer_order_item_id', $orderItemId)
            ->where('status', 'reserved')
            ->update([
                'status' => 'cancelled',
                'updated_at' => Carbon::now()
            ]);
    }

    public function markAsShipped(array $reservationIds): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $reservationIds)
            ->where('status', 'reserved')
            ->update([
                'status' => 'shipped',
                'updated_at' => Carbon::now()
            ]);
    }

    public function getAvailableQuantityForProduct(string $productId, string $warehouseId, string $dateFrom): int
    {
        return DB::selectOne("
            SELECT COALESCE(
                SUM(wi.quantity) - COALESCE(SUM(wr.reserved_quantity), 0), 0
            ) as available_quantity
            FROM warehouse_items wi
            LEFT JOIN warehouse_reservations wr ON wi.id = wr.warehouse_item_id 
                AND wr.status = 'reserved'
            WHERE wi.product_id = ?
              AND wi.warehouse_id = ?
              AND wi.received_at <= ?
              AND wi.status != 'out_of_stock'
        ", [$productId, $warehouseId, $dateFrom])->available_quantity ?? 0;
    }

    public function getExpiredReservations(): Collection
    {
        return DB::table(self::TABLE)
            ->where('status', 'reserved')
            ->where('auto_release', true)
            ->where('expires_at', '<', Carbon::now())
            ->get();
    }

    public function releaseExpiredReservations(): int
    {
        return DB::table(self::TABLE)
            ->where('status', 'reserved')
            ->where('auto_release', true)
            ->where('expires_at', '<', Carbon::now())
            ->update([
                'status' => 'expired',
                'updated_at' => Carbon::now()
            ]);
    }

    public function getReservationsByPriority(int $maxPriority, string $warehouseId): Collection
    {
        return DB::table(self::TABLE . ' as wr')
            ->join('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
            ->where('wi.warehouse_id', $warehouseId)
            ->where('wr.status', 'reserved')
            ->where('wr.priority', '>', $maxPriority)
            ->select('wr.*')
            ->get();
    }

    public function getReservationsByDocument(string $documentType, string $documentId): Collection
    {
        return DB::table(self::TABLE)
            ->where('document_type', $documentType)
            ->where('document_id', $documentId)
            ->where('status', 'reserved')
            ->get();
    }
}
