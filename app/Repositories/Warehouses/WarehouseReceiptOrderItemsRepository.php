<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseReceiptOrderItemsRepository implements WarehouseReceiptOrderItemsRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'warehouse_receipt_order_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function bulkInsert(array $items): bool
    {
        $items = array_map(fn($item) => $this->setTimestamps($item), $items);
        return DB::table(self::TABLE)->insert($items);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge($data, ['updated_at' => Carbon::now()]));
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getByReceiptOrder(string $receiptOrderId): Collection
    {
        return DB::table(self::TABLE . ' as wroi')
            ->leftJoin('products as p', 'wroi.product_id', '=', 'p.id')
            ->leftJoin('vat_rates as vr', 'wroi.vat_rate_id', '=', 'vr.id')
            ->select([
                'wroi.*',
                DB::raw("json_build_object('id', p.id, 'name', p.name, 'sku', p.sku) as product"),
                DB::raw("CASE WHEN vr.id is not null THEN json_build_object('id', vr.id, 'rate', vr.rate) ELSE NULL END as vat_rate")
            ])
            ->where('wroi.receipt_order_id', $receiptOrderId)
            ->get();
    }

    public function deleteByReceiptOrder(string $receiptOrderId): int
    {
        return DB::table(self::TABLE)
            ->where('receipt_order_id', $receiptOrderId)
            ->delete();
    }

    public function getByProduct(string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('product_id', $productId);

        if (isset($filters['warehouse_id'])) {
            $query->join('warehouse_receipt_orders as wro', 'warehouse_receipt_order_items.receipt_order_id', '=', 'wro.id')
                  ->where('wro.warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['batch_number'])) {
            $query->where('batch_number', $filters['batch_number']);
        }

        if (isset($filters['quality_status'])) {
            $query->where('quality_status', $filters['quality_status']);
        }

        return $query->get();
    }

    public function getTotalQuantityByReceiptOrder(string $receiptOrderId): int
    {
        return DB::table(self::TABLE)
            ->where('receipt_order_id', $receiptOrderId)
            ->sum('quantity');
    }
}
