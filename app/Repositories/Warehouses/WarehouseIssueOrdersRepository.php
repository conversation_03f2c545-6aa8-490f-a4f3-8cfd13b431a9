<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseIssueOrdersRepository implements WarehouseIssueOrdersRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'warehouse_issue_orders';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge($data, ['updated_at' => Carbon::now()]));
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE . ' as wio')
            ->leftJoin('employees as e', 'wio.employee_id', '=', 'e.id')
            ->leftJoin('departments as d', 'wio.department_id', '=', 'd.id')
            ->leftJoin('warehouses as w', 'wio.warehouse_id', '=', 'w.id')
            ->leftJoin('statuses as s', 'wio.status_id', '=', 's.id')
            ->select([
                'wio.*',
                DB::raw("json_build_object('id', e.id, 'name', e.name) as employee"),
                DB::raw("json_build_object('id', d.id, 'name', d.name) as department"),
                DB::raw("json_build_object('id', w.id, 'name', w.name) as warehouse"),
                DB::raw("CASE WHEN s.id is not null THEN json_build_object('id', s.id, 'name', s.name) ELSE NULL END as status")
            ])
            ->where('wio.id', $id)
            ->first();
    }

    public function getByWarehouse(string $warehouseId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId);

        if (isset($filters['date_from'])) {
            $query->where('date_from', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('date_from', '<=', $filters['date_to']);
        }

        if (isset($filters['held'])) {
            $query->where('held', $filters['held']);
        }

        if (isset($filters['write_off_reason'])) {
            $query->where('write_off_reason', $filters['write_off_reason']);
        }

        return $query->orderBy('date_from', 'desc')->get();
    }

    public function markAsHeld(string $id): int
    {
        return $this->update($id, ['held' => true]);
    }

    public function getByDocumentBasis(string $documentType, string $documentId): Collection
    {
        return DB::table(self::TABLE)
            ->where('document_basis_type', $documentType)
            ->where('document_basis_id', $documentId)
            ->get();
    }

    public function getByWriteOffReason(string $reason, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('write_off_reason', $reason);

        if (isset($filters['warehouse_id'])) {
            $query->where('warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('date_from', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('date_from', '<=', $filters['date_to']);
        }

        return $query->orderBy('date_from', 'desc')->get();
    }
}
