<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehousePhonesRepositoryContract;
use App\Entities\WarehousePhoneEntity;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehousePhonesRepository implements WarehousePhonesRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_phones';

    public function findOrCreate(string $phone, string $cabinetId): string
    {
        $resource = DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->where('phone', $phone)
            ->first('id');

        if ($resource) {
            return $resource->id;
        }

        $id = $this->generateUuid();
        DB::table(self::TABLE)
            ->insert(
                [
                    'id' => $id,
                    'cabinet_id' => $cabinetId,
                    'created_at' => Carbon::now(),
                    'phone' => $phone
                ]
            );
        return $id;
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('comment', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('phone', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    private function getEntity(): WarehousePhoneEntity
    {
        return new WarehousePhoneEntity();
    }
}
