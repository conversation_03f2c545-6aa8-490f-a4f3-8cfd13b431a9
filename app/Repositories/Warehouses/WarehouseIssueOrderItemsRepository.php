<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseIssueOrderItemsRepository implements WarehouseIssueOrderItemsRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'warehouse_issue_order_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)->insert($data);
    }

    public function bulkInsert(array $items): bool
    {
        $items = array_map(fn($item) => $this->setTimestamps($item), $items);
        return DB::table(self::TABLE)->insert($items);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge($data, ['updated_at' => Carbon::now()]));
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getByIssueOrder(string $issueOrderId): Collection
    {
        return DB::table(self::TABLE . ' as wioi')
            ->leftJoin('products as p', 'wioi.product_id', '=', 'p.id')
            ->leftJoin('warehouse_items as wi', 'wioi.warehouse_item_id', '=', 'wi.id')
            ->leftJoin('vat_rates as vr', 'wioi.vat_rate_id', '=', 'vr.id')
            ->select([
                'wioi.*',
                DB::raw("json_build_object('id', p.id, 'name', p.name, 'sku', p.sku) as product"),
                DB::raw("json_build_object('id', wi.id, 'received_at', wi.received_at, 'quantity', wi.quantity) as warehouse_item"),
                DB::raw("CASE WHEN vr.id is not null THEN json_build_object('id', vr.id, 'rate', vr.rate) ELSE NULL END as vat_rate")
            ])
            ->where('wioi.issue_order_id', $issueOrderId)
            ->get();
    }

    public function deleteByIssueOrder(string $issueOrderId): int
    {
        return DB::table(self::TABLE)
            ->where('issue_order_id', $issueOrderId)
            ->delete();
    }

    public function getByWarehouseItem(string $warehouseItemId): Collection
    {
        return DB::table(self::TABLE)
            ->where('warehouse_item_id', $warehouseItemId)
            ->get();
    }

    public function getTotalQuantityByIssueOrder(string $issueOrderId): int
    {
        return DB::table(self::TABLE)
            ->where('issue_order_id', $issueOrderId)
            ->sum('quantity');
    }

    public function getByProduct(string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('product_id', $productId);

        if (isset($filters['warehouse_id'])) {
            $query->join('warehouse_issue_orders as wio', 'warehouse_issue_order_items.issue_order_id', '=', 'wio.id')
                  ->where('wio.warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['batch_number'])) {
            $query->where('batch_number', $filters['batch_number']);
        }

        return $query->get();
    }
}
