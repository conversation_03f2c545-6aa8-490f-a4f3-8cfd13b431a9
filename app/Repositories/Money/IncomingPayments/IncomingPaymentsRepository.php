<?php

namespace App\Repositories\Money\IncomingPayments;

use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class IncomingPaymentsRepository implements IncomingPaymentsRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;

    protected const TABLE = 'incoming_payments';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = DB::table(self::TABLE)
            ->where('cabinet_id', $id)
            ->where('deleted_at', null);

        $query = $this->authService->queryFilter(
            $id,
            $query,
            PermissionNameEnum::INCOMING_PAYMENTS->value,
            self::TABLE
        );

        return $query->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function updateWhereInIds(array $ids, array $data): int
    {
        $data = $this->setUpdatedAt($data);
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->update($data);
    }

    public function getWhereIdIn(array $ids): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->get();
    }
}
