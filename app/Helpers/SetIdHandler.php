<?php

namespace App\Helpers;

use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class SetIdHandler
{
    use HasOrderedUuid;
    
    public static function setIdData(array|Collection $data, array $params = [], ?string $key = null): Collection 
    {
        $data = new Collection(is_array($data) && $key ? $data[$key] ?? [] : $data);
    
        return $data->map(function ($item) use ($params) {
            if (!isset($item['id'])) {
                $item['id'] = (new self)->generateUuid();
            }
    
            foreach ($params as $param => $value) {
                $item[$param] = $value;
            }
    
            return $item;
        });
    }
}
