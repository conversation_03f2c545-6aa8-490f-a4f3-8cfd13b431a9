<?php

namespace App\Contracts\Services\Internal;

use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\DTO\OzonV3PostingFbsListDTO;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface OzonV3PostingFbsListServiceContract
{
    public function index(array|IndexRequestDTO $data): Collection|LengthAwarePaginator;
    public function show(string $id): ?object;
    public function create(OzonV3PostingFbsListDTO $dto): void;
}
