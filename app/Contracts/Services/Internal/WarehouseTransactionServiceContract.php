<?php

namespace App\Contracts\Services\Internal;

interface WarehouseTransactionServiceContract
{
    /**
     * Создать транзакцию движения товаров
     */
    public function createTransaction(array $data): bool;

    /**
     * Создать множественные транзакции
     */
    public function createBulkTransactions(array $transactions): bool;

    /**
     * Получить транзакции по документу-основанию
     */
    public function getTransactionsByDocument(string $documentType, string $documentId): array;

    /**
     * Получить транзакции по складу
     */
    public function getTransactionsByWarehouse(string $warehouseId, array $filters = []): array;

    /**
     * Получить транзакции по товару
     */
    public function getTransactionsByProduct(string $productId, array $filters = []): array;

    /**
     * Получить отчет по движению товаров
     */
    public function getMovementReport(string $warehouseId, string $dateFrom, string $dateTo): array;
}
