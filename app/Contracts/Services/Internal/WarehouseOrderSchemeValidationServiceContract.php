<?php

namespace App\Contracts\Services\Internal;

interface WarehouseOrderSchemeValidationServiceContract
{
    /**
     * Валидация операции отгрузки
     */
    public function validateShipmentOperation(string $warehouseId, array $items): array;

    /**
     * Валидация операции резервирования
     */
    public function validateReservationOperation(string $warehouseId, array $items): array;

    /**
     * Валидация доступности товаров на складе
     */
    public function validateStockAvailability(string $warehouseId, string $productId, int $quantity, string $date): array;

    /**
     * Валидация операции перемещения между складами
     */
    public function validateTransferOperation(string $sourceWarehouseId, string $targetWarehouseId, array $items): array;

    /**
     * Валидация сроков годности товаров
     */
    public function validateExpiryDates(string $warehouseId, array $items): array;
}
