<?php

namespace App\Contracts\Services\Internal;

interface WarehouseOrderSchemeDetectionServiceContract
{
    /**
     * Проверить активна ли ордерная схема для приемок на указанную дату
     */
    public function isOrderSchemeActiveForReceipts(string $warehouseId, string $date): bool;

    /**
     * Проверить активна ли ордерная схема для отгрузок на указанную дату
     */
    public function isOrderSchemeActiveForShipments(string $warehouseId, string $date): bool;

    /**
     * Получить режим работы склада
     * Возвращает массив: ['receipts' => bool, 'shipments' => bool, 'full' => bool]
     */
    public function getOrderSchemeMode(string $warehouseId): array;

    /**
     * Получить настройки ордерной схемы для склада
     */
    public function getOrderSchemeSettings(string $warehouseId): ?object;

    /**
     * Проверить включен ли контроль операционных остатков
     */
    public function isOperationalBalanceControlEnabled(string $warehouseId): bool;
}
