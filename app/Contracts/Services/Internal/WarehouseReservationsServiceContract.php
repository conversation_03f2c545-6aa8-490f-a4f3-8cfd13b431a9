<?php

namespace App\Contracts\Services\Internal;

use App\DTO\IndexRequestDTO;

interface WarehouseReservationsServiceContract
{
    public function index(IndexRequestDTO $dto): array;
    
    public function create(object $dto): array;
    
    public function cancel(string $id): bool;
    
    public function markAsShipped(array $reservationIds): int;
    
    public function releaseExpired(): int;
    
    public function getAvailability(string $productId, string $warehouseId, string $date): array;

    public function getReservationTypes(): array;
}
