<?php

namespace App\Contracts\Services\Internal;

interface WarehouseOrderSchemeReportsServiceContract
{
    /**
     * Отчет по движению товаров в ордерной схеме
     */
    public function getStockMovementReport(array $filters): array;

    /**
     * Отчет по резервированию товаров
     */
    public function getReservationReport(array $filters): array;

    /**
     * Аналитика по ордерной схеме
     */
    public function getOrderSchemeAnalytics(array $filters): array;

    /**
     * Отчет по остаткам с детализацией по партиям
     */
    public function getInventoryReport(array $filters): array;
}
