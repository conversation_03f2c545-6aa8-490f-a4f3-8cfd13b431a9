<?php

namespace App\Contracts\Services\Internal;

use App\DTO\IndexRequestDTO;

interface WarehouseReceiptOrdersServiceContract
{
    public function index(IndexRequestDTO $dto): array;
    
    public function show(string $id): ?object;
    
    public function create(object $dto): string;
    
    public function update(string $id, object $dto): bool;
    
    public function delete(string $id): bool;
    
    public function hold(string $id): bool;
}
