<?php

namespace App\Contracts;

use Illuminate\Support\Collection;

interface CRUDRepositoryContract
{
    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection;

    public function insert(array $data): bool;

    public function update(string $id, array $data): int;

    public function delete(string $id): int;

    public function show(string $id): ?object;
}
