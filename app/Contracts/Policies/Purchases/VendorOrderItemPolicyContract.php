<?php

namespace App\Contracts\Policies\Purchases;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemCalculateDTO;

interface VendorOrderItemPolicyContract extends BaseResourcePolicyContract
{
    public function index(string $vendorOrderId): void;

    public function calculateMetrics(VendorOrderItemCalculateDTO $dto): void;

}
