<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseIssueOrderItemsRepositoryContract
{
    public function insert(array $data): bool;
    
    public function bulkInsert(array $items): bool;
    
    public function update(string $id, array $data): int;
    
    public function delete(string $id): int;
    
    public function getByIssueOrder(string $issueOrderId): Collection;
    
    public function deleteByIssueOrder(string $issueOrderId): int;
    
    public function getByWarehouseItem(string $warehouseItemId): Collection;
    
    public function getTotalQuantityByIssueOrder(string $issueOrderId): int;
    
    public function getByProduct(string $productId, array $filters = []): Collection;
}
