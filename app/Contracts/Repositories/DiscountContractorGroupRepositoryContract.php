<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;
use App\Entities\DiscountContractorGroupEntity;

interface DiscountContractorGroupRepositoryContract extends CRUDRepositoryContract
{
    public function getEntity(): DiscountContractorGroupEntity;
    public function upsert(array $data): int;
    public function oldContractorGroupIdsForDiscount(string $resourceId): ?Collection;
    public function deleteOldContractorGroupWhereGroupIds(string $id, array $recordsToDelete): int;
    
}
