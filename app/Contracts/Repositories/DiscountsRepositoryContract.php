<?php

namespace App\Contracts\Repositories;

use App\Contracts\CRUDRepositoryContract;

interface DiscountsRepositoryContract extends CRUDRepositoryContract
{
  public function setStatus(string $resourceId, int $status): int;
  public function getAllProductsForDiscountProductsId(string $resourceId): ?object;
  public function getAllGroupsForDiscountGroupsId(string $resourceId): ?object;
  public function getAllSavingsForDiscountSavingsId(string $resourceId): ?object;
  
}
