<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;
use App\Entities\ContractorContactEntity;

interface ContractorContactsRepositoryContract extends CRUDRepositoryContract
{
    public function getEntity(): ContractorContactEntity;
    public function upsert(array $data): int;
    public function oldContractorContactsIdsForContractor(string $resourceId): ?Collection;
    public function deleteOldContractorContactsWhereContactsIds(array $recordsToDelete): int;
    
}
