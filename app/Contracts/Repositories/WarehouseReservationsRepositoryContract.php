<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseReservationsRepositoryContract
{
    public function insert(array $data): bool;
    
    public function bulkInsert(array $items): bool;
    
    public function update(string $id, array $data): int;
    
    public function delete(string $id): int;
    
    public function getReservationsByOrderItem(string $orderItemId): Collection;
    
    public function getReservationsByWarehouseItem(string $warehouseItemId): Collection;
    
    public function getTotalReservedQuantity(string $warehouseItemId): int;
    
    public function cancelReservationsByOrderItem(string $orderItemId): int;
    
    public function markAsShipped(array $reservationIds): int;
    
    public function getAvailableQuantityForProduct(string $productId, string $warehouseId, string $dateFrom): int;
    
    public function getExpiredReservations(): Collection;
    
    public function releaseExpiredReservations(): int;
    
    public function getReservationsByPriority(int $maxPriority, string $warehouseId): Collection;
    
    public function getReservationsByDocument(string $documentType, string $documentId): Collection;
}
