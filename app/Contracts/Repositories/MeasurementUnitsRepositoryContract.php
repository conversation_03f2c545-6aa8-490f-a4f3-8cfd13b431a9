<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface MeasurementUnitsRepositoryContract extends CRUDRepositoryContract
{
    public function findBaseUnitInGroup(string $groupId): ?object;
    public function getOtherUnitsInGroup(string $groupId, string $unitId): Collection;
    public function findById(string $id, string $cabinetId, array $columns = ['*']): ?object;
    public function getUnitsByGroup(string $groupId): Collection;

    public function deleteWhereIn(array $ids): int;
}
