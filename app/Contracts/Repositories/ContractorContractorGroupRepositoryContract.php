<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;
use App\Entities\ContractorContractorGroupEntity;

interface ContractorContractorGroupRepositoryContract extends CRUDRepositoryContract
{
    public function getEntity(): ContractorContractorGroupEntity;
    public function upsert(array $data): int;
    public function oldContractorGroupIdsForContractor(string $resourceId): ?Collection;
    public function deleteOldContractorGroupWhereGroupIds(string $id, array $recordsToDelete): int;
    
}
