<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface ProductPricesRepositoryContract extends CRUDRepositoryContract
{
    public function getMyCountWhereInIds(array|Collection $ids, string $cabinetId): int;

    public function oldProductPricesIdsForProducts(string $resourceId): ?Collection;

    public function upsert(Collection $data): int;

    public function getWhereProductId(string $resourceId): Collection;

    public function getFirst(string $resourceId): ?object;
    
    public function deleteArray(array|Collection $id): int;

}
