<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface FilesRepositoryContract
{
    public function insert(array $data): void;

    public function deleteByRelatedId(string $relatedId): void;

    public function bulkDeleteByRelatedId(array $relatedIds): void;

    public function getByRelatedId(string $relatedId): Collection;

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection;

    public function delete(string $id): void;
    public function getById(string $id): ?object;
}
