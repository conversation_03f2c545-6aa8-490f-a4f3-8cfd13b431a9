<?php

namespace App\Contracts\Repositories;

use App\Contracts\CRUDRepositoryContract;
use App\Contracts\HasArchiveRepositoryContract;

interface EmployeeRepositoryContract extends CRUDRepositoryContract, HasArchiveRepositoryContract
{
    public function getByUserIdAndCabinet(int $userId, string $cabinetId): ?object;
    public function checkUserExistsInCabinet(int $userId, string $cabinetId): bool;

    public function deleteWhereIn(array $ids): int;
}
