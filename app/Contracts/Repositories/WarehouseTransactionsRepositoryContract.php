<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseTransactionsRepositoryContract
{
    public function insert(array $data): bool;
    
    public function bulkInsert(array $items): bool;
    
    public function update(string $id, array $data): int;
    
    public function delete(string $id): int;
    
    public function getByDocument(string $documentType, string $documentId): Collection;
    
    public function getByWarehouse(string $warehouseId, array $filters = []): Collection;
    
    public function getByProduct(string $productId, array $filters = []): Collection;
    
    public function getMovementReport(string $warehouseId, string $dateFrom, string $dateTo): Collection;
    
    public function getByReservation(string $reservationId): Collection;
    
    public function getTotalQuantityByOperation(string $warehouseId, string $productId, string $operationType, array $filters = []): int;
}
