<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;
use App\Entities\ContractorAccountEntity;

interface ContractorAccountsRepositoryContract extends CRUDRepositoryContract
{
    public function getEntity(): ContractorAccountEntity;
    public function upsert(array $data): int;
    public function oldContractorAccountIdsForContractor(string $resourceId): ?Collection;
    public function deleteOldContractorAccountWhereGroupIds(array $recordsToDelete): int;
    
}
