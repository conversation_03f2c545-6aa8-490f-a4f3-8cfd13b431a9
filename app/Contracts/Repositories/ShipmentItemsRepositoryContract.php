<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface ShipmentItemsRepositoryContract extends CRUDRepositoryContract
{
    public function checkItemExists(string $productId, string $acceptanceId): bool;
    public function getShipmentTotalsById(string $shipmentId): ?object;
    public function getShipmentTotalsByIds(array $shipmentIds): array;
    public function getShipmentDetails(string $resourceId, array $select = ['*']): ?object;
    public function getNewestShipmentItems(string $productId, string $date, string $warehouseId): ?Collection;
    public function getByShipmentId(string $shipmentId): Collection;
    public function getByShipmentIds(array $shipmentIds): Collection;

    public function findUncompletedOrPendingShipmentItemByProductWarehouseAndDate(string $productId, string $warehouseId, string $dateFrom): ?object;

    public function getBulkShipmentDetails(array $shipmentItemIds, array $fields): Collection;

    public function getBulkNewestShipmentItems(array $productIds, string $shipmentDate, string $warehouseId): Collection;

    public function bulkDelete(array $ids): int;
    
    public function calculateMetricsForNewItem(string $productId, string $warehouseId, string $dateFrom, string $cabinetId): object;

}
