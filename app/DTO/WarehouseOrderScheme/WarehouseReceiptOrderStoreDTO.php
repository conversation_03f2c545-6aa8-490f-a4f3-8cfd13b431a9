<?php

namespace App\DTO\WarehouseOrderScheme;

use App\Contracts\DtoContract;

class WarehouseReceiptOrderStoreDTO implements DtoContract
{
    public function __construct(
        public readonly string $cabinet_id,
        public readonly string $employee_id,
        public readonly string $department_id,
        public readonly string $warehouse_id,
        public readonly string $date_from,
        public readonly int $total_quantity,
        public readonly string $total_cost,
        public readonly ?string $number = null,
        public readonly ?string $status_id = null,
        public readonly ?string $document_basis_type = null,
        public readonly ?string $document_basis_id = null,
        public readonly ?string $reason = null,
        public readonly ?string $comment = null,
        public readonly ?array $items = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'],
            employee_id: $data['employee_id'],
            department_id: $data['department_id'],
            warehouse_id: $data['warehouse_id'],
            date_from: $data['date_from'],
            total_quantity: $data['total_quantity'],
            total_cost: $data['total_cost'],
            number: $data['number'] ?? null,
            status_id: $data['status_id'] ?? null,
            document_basis_type: $data['document_basis_type'] ?? null,
            document_basis_id: $data['document_basis_id'] ?? null,
            reason: $data['reason'] ?? null,
            comment: $data['comment'] ?? null,
            items: $data['items'] ?? null
        );
    }
}
