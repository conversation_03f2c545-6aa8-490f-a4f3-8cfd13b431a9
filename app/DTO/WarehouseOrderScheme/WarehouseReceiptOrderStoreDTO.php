<?php

namespace App\DTO\WarehouseOrderScheme;

class WarehouseReceiptOrderStoreDTO
{
    public function __construct(
        public readonly string $cabinet_id,
        public readonly string $employee_id,
        public readonly string $department_id,
        public readonly string $warehouse_id,
        public readonly string $date_from,
        public readonly int $total_quantity,
        public readonly string $total_cost,
        public readonly ?string $number = null,
        public readonly ?string $status_id = null,
        public readonly ?string $document_basis_type = null,
        public readonly ?string $document_basis_id = null,
        public readonly ?string $reason = null,
        public readonly ?string $comment = null,
        public readonly ?array $items = null
    ) {
    }
}
