<?php

namespace App\DTO\WarehouseOrderScheme;

use App\Contracts\DtoContract;

class WarehouseIssueOrderUpdateDTO implements DtoContract
{
    public function __construct(
        public readonly ?string $number = null,
        public readonly ?string $dateFrom = null,
        public readonly ?string $statusId = null,
        public readonly ?string $writeOffReason = null,
        public readonly ?string $reasonDescription = null,
        public readonly ?string $comment = null,
        public readonly ?string $resourceId = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            number: $data['number'] ?? null,
            dateFrom: $data['date_from'] ?? null,
            statusId: $data['status_id'] ?? null,
            writeOffReason: $data['write_off_reason'] ?? null,
            reasonDescription: $data['reason_description'] ?? null,
            comment: $data['comment'] ?? null,
            resourceId: $data['resource_id'] ?? null
        );
    }
}
