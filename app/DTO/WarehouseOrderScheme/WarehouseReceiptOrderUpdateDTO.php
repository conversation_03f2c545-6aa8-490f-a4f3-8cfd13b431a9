<?php

namespace App\DTO\WarehouseOrderScheme;

use App\Contracts\DtoContract;

class WarehouseReceiptOrderUpdateDTO implements DtoContract
{
    public function __construct(
        public readonly ?string $number = null,
        public readonly ?string $date_from = null,
        public readonly ?string $status_id = null,
        public readonly ?string $reason = null,
        public readonly ?string $comment = null,
        public readonly ?string $resourceId = null
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            number: $data['number'] ?? null,
            date_from: $data['date_from'] ?? null,
            status_id: $data['status_id'] ?? null,
            reason: $data['reason'] ?? null,
            comment: $data['comment'] ?? null,
            resourceId: $data['resource_id'] ?? null
        );
    }
}
