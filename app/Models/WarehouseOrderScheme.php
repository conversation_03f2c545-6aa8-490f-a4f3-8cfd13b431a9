<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseOrderScheme extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'warehouse_id',
        'on_coming_from',
        'on_shipment_from',
        'control_operational_balances',
    ];

    protected $casts = [
        'on_coming_from' => 'date',
        'on_shipment_from' => 'date',
        'control_operational_balances' => 'boolean',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }
}
