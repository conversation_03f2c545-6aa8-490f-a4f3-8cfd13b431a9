<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseReceiptOrderItem extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'receipt_order_id',
        'product_id',
        'quantity',
        'unit_price',
        'total_price',
        'batch_number',
        'lot_number',
        'expiry_date',
        'quality_status',
        'storage_location',
        'vat_rate_id',
    ];

    protected $casts = [
        'expiry_date' => 'date',
    ];

    public function receiptOrder(): BelongsTo
    {
        return $this->belongsTo(WarehouseReceiptOrder::class, 'receipt_order_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function vatRate(): BelongsTo
    {
        return $this->belongsTo(VatRate::class);
    }
}
