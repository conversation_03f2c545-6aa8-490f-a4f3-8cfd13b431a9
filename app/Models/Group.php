<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Group extends Model
{
    use HasFactory;
    use HasUuids;

    public $timestamps = false;

    /**
     * @return BelongsToMany<Contractor>
     */
    public function contractors(): BelongsToMany
    {
        return $this->belongsToMany(Contractor::class, 'contractor_group', 'group_id', 'contractor_id');
    }

    /**
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'cabinet_id',
        'title',
    ];

}
