<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LegalDetailAddress extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'legal_detail_id',
        'postcode',
        'country',
        'region',
        'city',
        'street',
        'house',
        'office',
        'other',
        'comment',
    ];

    public function legalDetail(): BelongsTo
    {
        return $this->belongsTo(LegalDetail::class);
    }
}
