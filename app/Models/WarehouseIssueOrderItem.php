<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseIssueOrderItem extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'issue_order_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'unit_price',
        'total_price',
        'batch_number',
        'lot_number',
        'expiry_date',
        'vat_rate_id',
    ];

    protected $casts = [
        'expiry_date' => 'date',
    ];

    public function issueOrder(): BelongsTo
    {
        return $this->belongsTo(WarehouseIssueOrder::class, 'issue_order_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class);
    }

    public function vatRate(): BelongsTo
    {
        return $this->belongsTo(VatRate::class);
    }
}
