<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseTransaction extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'warehouse_id',
        'product_id',
        'quantity',
        'document_type',
        'document_id',
        'operation_type',
        'batch_number',
        'lot_number',
        'expiry_date',
        'quality_status',
        'cost_per_unit',
        'reservation_id',
        // Старые поля для обратной совместимости
        'item_id',
        'shipment_id',
        'transaction_type',
        'total_cost',
        'transaction_date'
    ];

    protected $casts = [
        'expiry_date' => 'date',
        'transaction_date' => 'datetime',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function reservation(): BelongsTo
    {
        return $this->belongsTo(WarehouseReservation::class);
    }

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class, 'item_id');
    }

    public function shipment(): BelongsTo
    {
        return $this->belongsTo(Shipment::class);
    }
}
