<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LegalHead extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'head_name',
        'head_position',
        'accountant_name',
        'head_signature',
        'accountant_signature',
        'stamp',
        'logo',
    ];

    public function legalEntity(): BelongsTo
    {
        return $this->belongsTo(LegalEntity::class);
    }
}
