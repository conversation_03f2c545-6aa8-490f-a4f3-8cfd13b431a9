<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseReservation extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'warehouse_item_id',
        'customer_order_item_id',
        'reserved_quantity',
        'reserved_at',
        'status',
        'reservation_type',
        'document_type',
        'document_id',
        'priority',
        'expires_at',
        'auto_release',
    ];

    protected $casts = [
        'reserved_at' => 'datetime',
        'expires_at' => 'datetime',
        'auto_release' => 'boolean',
    ];

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class);
    }

    public function customerOrderItem(): BelongsTo
    {
        return $this->belongsTo(CustomerOrderItem::class);
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isActive(): bool
    {
        return $this->status === 'reserved' && !$this->isExpired();
    }

    public function canAutoRelease(): bool
    {
        return $this->auto_release && $this->isExpired();
    }
}
