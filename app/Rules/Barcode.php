<?php

namespace App\Rules;

use Closure;
use Exception;
use App\Enums\Api\Internal\BarcodeEnum;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use RuntimeException;

class Barcode implements DataAwareRule, ValidationRule
{
    /**
    * All of the data under validation.
    *
    * @var array<string, mixed>
    */
    protected $data = [];

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {

        foreach ($this->data['barcodes'] as $item) {

            if (isset($item['type']) && $item['type'] == BarcodeEnum::EAN13->value) {

                if (isset($item['barcode'])) {
                    if (strlen($item['barcode']) !== 13) {
                        $fail('The :attribute '.$item['barcode'].' EAN-13 must be 13 digits long.');
                        break;
                    }

                    if (!$this->isValidBarcodeEan13($item['barcode'])) {
                        $fail('The :attribute '.$item['barcode'].' EAN-13 code is invalid.');
                        break;
                    }
                }

            }

            if (isset($item['type']) && $item['type'] == BarcodeEnum::EAN8->value) {

                if (isset($item['barcode'])) {

                    if (strlen($item['barcode']) !== 8) {
                        $fail('The :attribute '.$item['barcode'].' EAN-8 must be 8 digits long.');
                        break;
                    }

                    if (!$this->isValidBarcodeEan8($item['barcode'])) {
                        $fail('The :attribute '.$item['barcode'].' EAN-8 code is invalid.');
                        break;
                    }
                }

            }
        }

    }

    protected function isValidBarcodeEan8(string $code): bool
    {
        // Получаем первые 12 цифр кода
        $codeWithoutCheckDigit = substr($code, 0, 7);

        // Вычисляем контрольную сумму
        $calculatedCheckDigit = $this->calculateEAN13CheckDigitEan8($codeWithoutCheckDigit);

        // Получаем последнюю цифру кода (контрольную сумму)
        $providedCheckDigit = substr($code, 7, 1);

        return $calculatedCheckDigit == $providedCheckDigit;
    }

    /**
     * @throws Exception
     */
    public function calculateEAN13CheckDigitEan8(string $code): int
    {
        // Убедитесь, что код имеет длину 12 символов
        if (strlen($code) !== 7) {
            throw new RuntimeException('EAN-8 code must be 8 digits long.');
        }

        // Преобразуем код в массив цифр
        $digits = str_split($code);

        // Инициализируем суммы для четных и нечетных позиций
        $oddSum = 0;
        $evenSum = 0;

        // Проходим по цифрам и суммируем их с учетом позиции
        for ($i = 0; $i < 7; $i++) {
            if ($i % 2 == 0) {
                $oddSum += $digits[$i];
            } else {
                $evenSum += $digits[$i];
            }
        }

        // Вычисляем общую сумму
        $totalSum = $oddSum * 3 + $evenSum;

        // Вычисляем контрольную сумму
        $checkDigit = (10 - ($totalSum % 10)) % 10;

        return $checkDigit;
    }

    protected function isValidBarcodeEan13(string $code): bool
    {
        // Получаем первые 12 цифр кода
        $codeWithoutCheckDigit = substr($code, 0, 12);

        // Вычисляем контрольную сумму
        $calculatedCheckDigit = $this->calculateEAN13CheckDigitEan13($codeWithoutCheckDigit);

        // Получаем последнюю цифру кода (контрольную сумму)
        $providedCheckDigit = substr($code, 12, 1);

        if ($calculatedCheckDigit == $providedCheckDigit) {
            return true;
        } else {
            return false;
        }
    }

    public function calculateEAN13CheckDigitEan13(string $code): int
    {
        // Убедитесь, что код имеет длину 12 символов
        if (strlen($code) !== 12) {
            throw new Exception('EAN-13 code must be 12 digits long.');
        }

        // Преобразуем код в массив цифр
        $digits = str_split($code);

        // Инициализируем суммы для четных и нечетных позиций
        $oddSum = 0;
        $evenSum = 0;

        // Проходим по цифрам и суммируем их с учетом позиции
        for ($i = 0; $i < 12; $i++) {
            if ($i % 2 == 0) {
                $oddSum += $digits[$i];
            } else {
                $evenSum += $digits[$i];
            }
        }

        // Вычисляем общую сумму
        $totalSum = $oddSum + $evenSum * 3;

        // Вычисляем контрольную сумму
        $checkDigit = (10 - ($totalSum % 10)) % 10;

        return $checkDigit;
    }

    /**
     * Set the data under validation.
     *
     * @param  array<string, mixed>  $data
     */
    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

}
