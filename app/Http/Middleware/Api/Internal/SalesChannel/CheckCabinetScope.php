<?php

namespace App\Http\Middleware\Api\Internal\SalesChannel;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;

class CheckCabinetScope
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $data = $request->validate(['cabinet_id' => 'required|UUID']);

        if (Gate::denies('access-cabinet', $data['cabinet_id'])) {
            return response()->json('Access denied or cabinet cannot be found', 403);
        }

        return $next($request);
    }
}
