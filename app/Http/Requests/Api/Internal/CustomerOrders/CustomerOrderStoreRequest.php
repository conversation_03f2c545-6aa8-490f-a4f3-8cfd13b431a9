<?php

namespace App\Http\Requests\Api\Internal\CustomerOrders;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\DTO\CustomerOrderDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CustomerOrderStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'number' => 'nullable|string',
            'date_from' => 'nullable|date',
            'status_id' => 'nullable|UUID',
            'held' => 'nullable|boolean',
            'reserve' => 'nullable|boolean',
            'legal_entity_id' => 'required|UUID',
            'contractor_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'plan_date' => 'nullable|date',
            'sales_channel_id' => 'nullable|UUID',
            'warehouse_id' => 'nullable|UUID',

            'delivery_info' => 'nullable|array',
            'delivery_info.comment' => 'nullable|string|max:255',
            'delivery_info.post_code' => 'nullable|string|max:255',
            'delivery_info.country' => 'nullable|string|max:255',
            'delivery_info.region' => 'nullable|string|max:255',
            'delivery_info.city' => 'nullable|string|max:255',
            'delivery_info.street' => 'nullable|string|max:255',
            'delivery_info.house' => 'nullable|string|max:255',
            'delivery_info.office' => 'nullable|string|max:255',
            'delivery_info.other' => 'nullable|string|max:255',

            'comment' => 'nullable|string',

            'files.*' => 'uuid',
            
            'has_vat' => 'nullable|boolean',
            'price_includes_vat' => 'nullable|boolean',
        ];
    }

    public function toDTO(): CustomerOrderDTO
    {
        return CustomerOrderDTO::fromArray($this->validated());
    }
}
