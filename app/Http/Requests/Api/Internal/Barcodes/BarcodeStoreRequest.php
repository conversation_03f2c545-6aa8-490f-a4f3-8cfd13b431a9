<?php

namespace App\Http\Requests\Api\Internal\Barcodes;

use App\Enums\Api\Internal\BarcodableTypeEnum;
use App\Enums\Api\Internal\BarcodeEnum;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BarcodeStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',         // id компании
            'entity_id' => 'required|UUID',          // id сущности
            'entity_type' => 'required|string|'. Rule::in(BarcodableTypeEnum::cases()), // тип сущности
            'barcodes' => 'required|array',
            'barcodes.type' => 'required|integer|'. Rule::in(BarcodeEnum::cases()), // тип штрихкода
            'barcodes.value' => 'nullable|string|max:48',  // штрихкода required_with:barcodes.type|
        ];
    }

    public function toDTO(): BarcodeDTO
    {
        return BarcodeDTO::fromArray($this->validated());
    }
}
