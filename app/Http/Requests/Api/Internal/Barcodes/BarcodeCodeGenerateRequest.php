<?php

namespace App\Http\Requests\Api\Internal\Barcodes;

use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use Illuminate\Foundation\Http\FormRequest;

class BarcodeCodeGenerateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',    // id компании
        ];
    }

    public function toDTO(): BarcodeDTO
    {
        return BarcodeDTO::fromArray($this->validated());
    }
}
