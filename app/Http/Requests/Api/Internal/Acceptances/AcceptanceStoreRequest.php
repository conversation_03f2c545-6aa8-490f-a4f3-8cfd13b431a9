<?php

namespace App\Http\Requests\Api\Internal\Acceptances;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\DTO\AcceptanceDto;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AcceptanceStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'number' => 'nullable|string',
            'date_from' => 'nullable|date_format:Y-m-d H:i:s',
            'status_id' => 'nullable|UUID',
            'held' => 'nullable|boolean',

            'legal_entity_id' => 'required|UUID',
            'contractor_id' => 'required|UUID',

            'warehouse_id' => 'required|UUID',

            'incoming_number' => 'nullable|string',
            'incoming_date' => 'required_with:incoming_number|date_format:Y-m-d',

            //TODO Договор + проект
            /*'project_id' => 'nullable|UUID',
            'agreement_id' => 'nullable|UUID',*/

            'currency_id' => 'required|UUID',
            'currency_value' => ['nullable', 'regex:/^\d{1,8}(\.\d{1,2})?$/'],

            'comment' => 'nullable|string',
            'price_includes_vat' => 'nullable|boolean',
            'has_vat' => 'nullable|boolean',
            'overhead_cost' => 'nullable|integer|min:0',

            'files.*' => 'uuid',

            //TODO Задачи
            /*'tasks' => 'nullable|array',
            'tasks.*.task_id' => 'required_with:tasks|UUID',*/
        ];
    }

    public function toDTO(): AcceptanceDto
    {
        return AcceptanceDto::fromArray($this->validated());
    }
}
