<?php

namespace App\Http\Requests\Api\Internal\GlobalCurrencies;

use App\DTO\IndexRequestDTO;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\GlobalCurrencyEntity;
use App\Contracts\Requests\ToDtoContract;
use Illuminate\Foundation\Http\FormRequest;

class GlobalCurrencyIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly GlobalCurrencyEntity $entity
    ) {
        parent::__construct();
    }
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'fields' => 'nullable|array',
            'fields.*' => ['string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'filters.search.value' => 'string',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray($data);
    }
}
