<?php

namespace App\Http\Requests\Api\Internal\WorkSchedules;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\DTO\WorkScheduleDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkScheduleStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID', //ID компании
            'name' => 'required|string', // Наименование
            'date_from' => 'required|date', //Дата начала
            'filling_type' => 'required|integer|' . Rule::in([1,2]), //Способ заполнения
            'description' => 'nullable|string', //Описание
            'date_to' => 'nullable|date', //Дата окончания
            'cycle_day_lenght' => 'required_if:filling_type,2|nullable|integer', //Длина цикла
            'cycle_day_from' => 'required_if:filling_type,2|nullable|date', //Начало цикла
            'keep_holidays' => 'bool',

             /**
              * {"start": "08:00","end": "12:00"}
              * Должны передать массивы вида
              **/
            'holiday_schedule' => 'required_if:keep_holidays,1,true|json',
            'holiday_schedule.*.start' => 'required|date_format:H:i',
            'holiday_schedule.*.end' => 'required|date_format:H:i|after:holiday_schedule.*.start',

            /**
             * {
             * "Monday": [
             * {
             *  "start": "08:00",
             *  "end": "12:00"
             * },
             * {
             *  "start": "13:00",
             *  "end": "17:00"
             * }
             * ],
             * "Tuesday": [
             * {
             *  "start": "09:00",
             *  "end": "15:00"
             * }
             * ]
             * }
             */
            'filling_template' => 'required|json',
            'filling_template.*.start' => 'required|date_format:H:i',
            'filling_template.*.end' => 'required|date_format:H:i|after:filling_template.*.start',

        ];
    }

    public function toDTO(): WorkScheduleDTO
    {
        return WorkScheduleDTO::fromArray($this->validated());
    }
}
