<?php

namespace App\Http\Requests\Api\Internal\VatRates;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\References\VatRatesService\DTO\VatRateDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class VatRateUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'rate' => 'required|integer|min:0|max:100', // Ставка
            'description' => 'nullable|string', //Описание
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID'
        ];
    }

    public function toDTO(): VatRateDTO
    {
        return VatRateDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
