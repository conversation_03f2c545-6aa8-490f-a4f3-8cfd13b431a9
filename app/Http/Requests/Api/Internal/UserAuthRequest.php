<?php

namespace App\Http\Requests\Api\Internal;

use App\Rules\PhoneNumber;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;

class UserAuthRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'lastname' => 'nullable|string|max:255',
            'firstname' => 'required|string|max:255',
            'patronymic' => 'nullable|string|max:255',
            'tel' => ['required','string',new PhoneNumber()],
            'email' => 'required|string|email|unique:users',
            'password' => 'required|string|min:8',
            'invite_token' => [
                'nullable',
                'string',
                    'nullable',
                    'string',
                    'size:16',
                    Rule::exists('cabinet_invites', 'token')
                        ->where('email', $this->input('email'))
                        ->where('status', CabinetInviteStatusEnum::WAITING),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'email' => strtolower($this->email),
        ]);
    }
}
