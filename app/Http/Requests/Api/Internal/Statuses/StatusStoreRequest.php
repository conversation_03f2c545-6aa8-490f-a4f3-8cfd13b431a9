<?php

namespace App\Http\Requests\Api\Internal\Statuses;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\StatusTypeEnum;
use App\Services\Api\Internal\Workspace\StatusesService\DTO\StatusDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StatusStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
    public function rules(): array
    {
        return [
            'cabinet_id' => ['required', 'UUID'],
            'name' => ['required', 'string', 'max:255'],

            'color' => ['required', 'string'],

            // Значения енумов ACCEPTANCES - 1, SHIPMENTS - 2, VENDOR_ORDERS - 3, CUSTOMER_ORDERS - 4, CONTRACTS - 5, GOODS_TRANSFERS - 6, INCOMING_PAYMENTS - 7, OUTGOING_PAYMENTS - 8, ISSUED_COMISSION_REPORTS - 9, RECEIVED_COMISSION_REPORTS - 10, CONTRACTORS - 11
            'type' => ['required', 'int', Rule::in(StatusTypeEnum::cases())],
        ];
    }

    public function toDTO(): StatusDTO
    {
        return StatusDTO::fromArray($this->validated());
    }
}
