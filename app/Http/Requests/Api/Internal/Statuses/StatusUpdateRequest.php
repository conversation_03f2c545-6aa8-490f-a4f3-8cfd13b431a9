<?php

namespace App\Http\Requests\Api\Internal\Statuses;

use App\Services\Api\Internal\Workspace\StatusesService\DTO\StatusDTO;
use Illuminate\Foundation\Http\FormRequest;

class StatusUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'color' => ['required', 'string'],
        ];
    }

    public function toDTO(): StatusDTO
    {
        return StatusDTO::fromArray(array_merge(
            $this->validated(),
            ['resource_id' => $this->route('id')]
        ));
    }
}
