<?php

namespace App\Http\Requests\Api\Internal\UserViewSettings;

use Illuminate\Validation\Rule;
use App\Enums\Api\Internal\ResourcesEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

class UserViewSettingsIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'name' => ['nullable', Rule::in(ResourcesEnum::cases())]
        ];
    }
}
