<?php

namespace App\Http\Requests\Api\Internal\SalesChannels;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\References\SalesChannelsService\DTO\SalesChannelDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class SalesChannelUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string', // Наименование
            'description' => 'nullable|string', //Описание
            'sales_channel_type_id' => 'required|UUID|string|exists:sales_channel_types,id', // Тип
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'sort' => 'nullable|integer',
            'is_common' => 'nullable|boolean'
        ];
    }

    public function toDTO(): SalesChannelDTO
    {
        return SalesChannelDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
