<?php

namespace App\Http\Requests\Api\Internal\ProductCategories;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Goods\Products\ProductCategoriesService\DTO\ProductCategoryDTO;
use Illuminate\Foundation\Http\FormRequest;

class ProductCategoryStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'parent_id' => 'nullable|string|max:36',
            'name' => 'required|string|max:100',
        ];
    }

    public function toDTO(): ProductCategoryDTO
    {
        return ProductCategoryDTO::fromArray($this->validated());
    }
}
