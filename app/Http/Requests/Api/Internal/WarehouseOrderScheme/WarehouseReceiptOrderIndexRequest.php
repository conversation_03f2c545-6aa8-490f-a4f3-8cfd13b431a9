<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\WarehouseReceiptOrderEntity;
use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseReceiptOrderIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly WarehouseReceiptOrderEntity $entity
    ) {
        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],

            'filters.warehouses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.warehouses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.warehouses.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.warehouses.value');
                    $condition = $this->input('filters.warehouses.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.employees.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employees.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employees.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.employees.value');
                    $condition = $this->input('filters.employees.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.departments.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.departments.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.departments.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.departments.value');
                    $condition = $this->input('filters.departments.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.statuses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.statuses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.statuses.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.statuses.value');
                    $condition = $this->input('filters.statuses.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.is_held.value' => 'boolean',
            'filters.search.value' => 'string',

            'filters.period.from' => 'date_format:d.m.Y H:i',
            'filters.period.to' => 'date_format:d.m.Y H:i|after_or_equal:filters.period.from',

            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
