<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderStoreDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseIssueOrderStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => ['required', 'uuid', 'exists:cabinets,id'],
            'employee_id' => ['required', 'uuid', 'exists:employees,id'],
            'department_id' => ['required', 'uuid', 'exists:departments,id'],
            'warehouse_id' => ['required', 'uuid', 'exists:warehouses,id'],
            'number' => ['nullable', 'string', 'max:255'],
            'date_from' => ['required', 'date'],
            'status_id' => ['nullable', 'uuid', 'exists:statuses,id'],
            'document_basis_type' => ['nullable', 'string', 'max:255'],
            'document_basis_id' => ['nullable', 'uuid'],
            'write_off_reason' => ['required', 'in:defective,expired,shortage,internal_use,return_to_supplier,damage,other'],
            'reason_description' => ['nullable', 'string'],
            'total_quantity' => ['required', 'integer', 'min:1'],
            'total_cost' => ['required', 'string'],
            'comment' => ['nullable', 'string'],
            'items' => ['required', 'array', 'min:1'],
            'items.*.product_id' => ['required', 'uuid', 'exists:products,id'],
            'items.*.warehouse_item_id' => ['required', 'uuid', 'exists:warehouse_items,id'],
            'items.*.quantity' => ['required', 'integer', 'min:1'],
            'items.*.unit_price' => ['required', 'string'],
            'items.*.total_price' => ['required', 'string'],
            'items.*.batch_number' => ['nullable', 'string', 'max:255'],
            'items.*.lot_number' => ['nullable', 'string', 'max:255'],
            'items.*.expiry_date' => ['nullable', 'date'],
            'items.*.vat_rate_id' => ['nullable', 'uuid', 'exists:vat_rates,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'cabinet_id.required' => 'Кабинет обязателен',
            'employee_id.required' => 'Сотрудник обязателен',
            'department_id.required' => 'Подразделение обязательно',
            'warehouse_id.required' => 'Склад обязателен',
            'date_from.required' => 'Дата документа обязательна',
            'write_off_reason.required' => 'Причина списания обязательна',
            'write_off_reason.in' => 'Недопустимая причина списания',
            'total_quantity.required' => 'Общее количество обязательно',
            'total_cost.required' => 'Общая стоимость обязательна',
            'items.required' => 'Позиции документа обязательны',
            'items.min' => 'Должна быть минимум одна позиция',
            'items.*.product_id.required' => 'Товар обязателен',
            'items.*.warehouse_item_id.required' => 'Партия товара обязательна',
            'items.*.quantity.required' => 'Количество обязательно',
            'items.*.quantity.min' => 'Количество должно быть больше 0',
            'items.*.unit_price.required' => 'Цена за единицу обязательна',
            'items.*.total_price.required' => 'Общая стоимость позиции обязательна',
        ];
    }

    public function toDTO(): WarehouseIssueOrderStoreDTO
    {
        return WarehouseIssueOrderStoreDTO::fromArray($this->validated());
    }
}
