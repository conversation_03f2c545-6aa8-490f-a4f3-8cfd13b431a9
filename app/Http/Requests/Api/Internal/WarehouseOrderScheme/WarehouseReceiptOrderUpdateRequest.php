<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderUpdateDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseReceiptOrderUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'number' => ['sometimes', 'string', 'max:255'],
            'date_from' => ['sometimes', 'date'],
            'status_id' => ['sometimes', 'nullable', 'uuid', 'exists:statuses,id'],
            'reason' => ['sometimes', 'nullable', 'string'],
            'comment' => ['sometimes', 'nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'date_from.date' => 'Дата документа должна быть корректной',
            'status_id.exists' => 'Указанный статус не существует',
        ];
    }

    public function toDTO(): WarehouseReceiptOrderUpdateDTO
    {
        return new WarehouseReceiptOrderUpdateDTO(
            number: $this->number,
            date_from: $this->date_from,
            status_id: $this->status_id,
            reason: $this->reason,
            comment: $this->comment
        );
    }
}
