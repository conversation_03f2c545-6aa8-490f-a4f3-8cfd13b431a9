<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use Illuminate\Foundation\Http\FormRequest;

class WarehouseReservationStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'warehouse_item_id' => ['required', 'uuid', 'exists:warehouse_items,id'],
            'customer_order_item_id' => ['required', 'uuid', 'exists:customer_order_items,id'],
            'reserved_quantity' => ['required', 'integer', 'min:1'],
            'reservation_type' => ['required', 'in:order,production,transfer,marketing,quality'],
            'document_type' => ['nullable', 'string', 'max:255'],
            'document_id' => ['nullable', 'uuid'],
            'priority' => ['sometimes', 'integer', 'min:1', 'max:10'],
            'expires_at' => ['nullable', 'date', 'after:now'],
            'auto_release' => ['sometimes', 'boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'warehouse_item_id.required' => 'Партия товара обязательна',
            'customer_order_item_id.required' => 'Позиция заказа обязательна',
            'reserved_quantity.required' => 'Количество для резервирования обязательно',
            'reserved_quantity.min' => 'Количество должно быть больше 0',
            'reservation_type.required' => 'Тип резервирования обязателен',
            'reservation_type.in' => 'Недопустимый тип резервирования',
            'priority.min' => 'Приоритет должен быть от 1 до 10',
            'priority.max' => 'Приоритет должен быть от 1 до 10',
            'expires_at.after' => 'Срок действия резерва должен быть в будущем',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'priority' => $this->priority ?? 5,
            'auto_release' => $this->auto_release ?? true,
        ]);
    }
}
