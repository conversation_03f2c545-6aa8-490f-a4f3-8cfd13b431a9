<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use Illuminate\Foundation\Http\FormRequest;

class WarehouseIssueOrderUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'number' => ['sometimes', 'string', 'max:255'],
            'date_from' => ['sometimes', 'date'],
            'status_id' => ['sometimes', 'nullable', 'uuid', 'exists:statuses,id'],
            'write_off_reason' => ['sometimes', 'in:defective,expired,shortage,internal_use,return_to_supplier,damage,other'],
            'reason_description' => ['sometimes', 'nullable', 'string'],
            'comment' => ['sometimes', 'nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'date_from.date' => 'Дата документа должна быть корректной',
            'status_id.exists' => 'Указанный статус не существует',
            'write_off_reason.in' => 'Недопустимая причина списания',
        ];
    }
}
