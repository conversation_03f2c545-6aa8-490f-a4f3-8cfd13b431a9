<?php

namespace App\Http\Requests\Api\Internal\Contractors;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\LegalEntityTaxation;
use App\Enums\Api\Internal\LegalEntityType;
use App\Rules\PhoneNumber;
use App\Services\Api\Internal\Contractors\ContractorsService\DTO\ContractorDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContractorStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',                   // Название
            'cabinet_id' => 'required|UUID',                        // Компания
            'status_id' => 'nullable|UUID',                         // Статус
            'is_buyer' => 'boolean',                          // Покупатель
            'is_supplier' => 'boolean',                       // Поставщик
            'phone' => ['nullable', 'string', new PhoneNumber()],                       // Телефон
            'fax' => 'nullable|string',                             // Факс
            'email' => 'nullable|email',                            // Элетронный адрес
            'description' => 'nullable|string',                     // Комментарий
            'code' => 'nullable|string',                            // Код
            'external_code' => 'nullable|string',                   // Внешний код
            'discounts_and_prices' => 'nullable|string',            // Скидки и цены  -Цена продажи
            'discount_card_number' => 'nullable|string',            // Номер диск. карты
            'department_id' => 'required|UUID',                     // Отдел
            'employee_id' => 'required|UUID',                       // Сотрудник
            'shared_access' => 'nullable|boolean',                  // Общий доступ

            'address.postcode' => 'nullable|string',
            'address.country' => 'nullable|string',
            'address.region' => 'nullable|string',
            'address.city' => 'nullable|string',
            'address.street' => 'nullable|string',
            'address.house' => 'nullable|string',
            'address.office' => 'nullable|string',
            'address.other' => 'nullable|string',
            'address.comment' => 'nullable|string',

            'detail.type' => ['required', Rule::enum(LegalEntityType::class)],
            'detail.vat_rate_id' => ['nullable', 'uuid'],
            'detail.inn' => ['nullable','string', 'max_digits:12'],
            'detail.kpp' => 'nullable|string|max_digits:9',
            'detail.ogrn' => ['nullable','string', 'regex:/^(\d{13}|\d{15})$/'],
            'detail.okpo' => 'nullable|string|max_digits:10',
            'detail.full_name' => 'required_if:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.firstname' => 'required_unless:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.patronymic' => 'required_unless:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.lastname' => 'required_unless:detail.type,' . LegalEntityType::LEGAL->value . '|nullable|string',
            'detail.ogrnip' => 'nullable|string',
            'detail.certificate_number' => 'nullable|string',
            'detail.certificate_date' => 'nullable|date',
            'detail.taxation_type' => [Rule::enum(LegalEntityTaxation::class)],

            'detail.address.postcode' => 'nullable|string',
            'detail.address.country' => 'nullable|string',
            'detail.address.region' => 'nullable|string',
            'detail.address.city' => 'nullable|string',
            'detail.address.street' => 'nullable|string',
            'detail.address.house' => 'nullable|string',
            'detail.address.office' => 'nullable|string',
            'detail.address.other' => 'nullable|string',
            'detail.address.comment' => 'nullable|string',

            'accounts.*.bik' => 'nullable|string',
            'accounts.*.payment_account' =>  ['nullable','string', 'regex:/^[0-9]{20}$/'],
            'accounts.*.correspondent_account' =>  ['nullable','string', 'regex:/^[0-9]{15,34}$/'],
            'accounts.*.bank' => 'nullable|string',
            'accounts.*.address' => 'nullable|string',
            'accounts.*.is_main' => 'nullable|boolean',

            'contacts.*.full_name' => 'required|string',
            'contacts.*.position' => 'nullable|string',
            'contacts.*.phone' => 'nullable|string',
            'contacts.*.email' => 'nullable|string',
            'contacts.*.comment' => 'nullable|string',

            'contractor_groups'                         => 'sometimes|nullable|array',
            'contractor_groups.*.group_id'   => 'required_with:contractor_groups|nullable|UUID',

            'files.*' => 'uuid',
        ];
    }

    public function toDTO(): ContractorDTO
    {
        return ContractorDTO::fromArray($this->validated());
    }
}
