<?php

namespace App\Http\Resources\Contractors;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор контрагента */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var bool $shared_access Общий доступ */
            'shared_access' => (bool) $this->shared_access,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $title Наименование */
            'title' => (string) $this->title,
            /** @var string $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var bool $is_buyer Покупатель */
            'is_buyer' => (bool) $this->is_buyer,
            /** @var bool $is_supplier Поставщик */
            'is_supplier' => (bool) $this->is_supplier,
            /** @var string|null $phone Телефон */
            'phone' => $this->phone,
            /** @var string|null $fax Факс */
            'fax' => $this->fax,
            /** @var string|null $email Электронный адрес */
            'email' => $this->email,
            /** @var string|null $description Комментарий */
            'description' => $this->description,
            /** @var string|null $code Код */
            'code' => $this->code,
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->external_code,
            /** @var string|null $discounts_and_prices Скидки и цены */
            'discounts_and_prices' => $this->discounts_and_prices,
            /** @var string|null $discount_card_number Номер дисконтной карты */
            'discount_card_number' => $this->discount_card_number,
            /** @var bool $is_default Контрагент по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var array{taxation_type: string|null, type: string|null, inn: string|null, kpp: string|null, ogrn: string|null, okpo: string|null, full_name: string|null, firstname: string|null, patronymic: string|null, lastname: string|null, ogrnip: string|null} $detail Детали контрагента */
            'detail' => !empty($this->detail) ? [
                'taxation_type' => $this->detail['taxation_type'] ?? null,
                'type' => $this->detail['type'] ?? null,
                'inn' => $this->detail['inn'] ?? null,
                'kpp' => $this->detail['kpp'] ?? null,
                'ogrn' => $this->detail['ogrn'] ?? null,
                'okpo' => $this->detail['okpo'] ?? null,
                'full_name' => $this->detail['full_name'] ?? null,
                'firstname' => $this->detail['firstname'] ?? null,
                'patronymic' => $this->detail['patronymic'] ?? null,
                'lastname' => $this->detail['lastname'] ?? null,
                'ogrnip' => $this->detail['ogrnip'] ?? null,
            ] : (object) [],
            /** @var array{id: string, bank: string, account_number: string, correspondent_account: string, bik: string}[] $accounts Банковские счета */
            'accounts' => !empty($this->accounts) ? array_map(function($account) {
                return [
                    'id' => $account['id'],
                    'bank' => $account['bank'],
                    'account_number' => $account['account_number'],
                    'correspondent_account' => $account['correspondent_account'],
                    'bik' => $account['bik'],
                ];
            }, $this->accounts) : [],
            /** @var array{postcode: string|null, city: string|null, region: string|null, house: string|null, office: string|null, other: string|null, street: string|null} $address Адрес */
            'address' => !empty($this->address) ? [
                'postcode' => $this->address['postcode'] ?? null,
                'city' => $this->address['city'] ?? null,
                'region' => $this->address['region'] ?? null,
                'house' => $this->address['house'] ?? null,
                'office' => $this->address['office'] ?? null,
                'other' => $this->address['other'] ?? null,
                'street' => $this->address['street'] ?? null,
            ] : (object) [],
            /** @var array{id: string, name: string, phone: string|null, email: string|null, position: string|null}[] $contacts Контакты */
            'contacts' => !empty($this->contacts) ? array_map(function($contact) {
                return [
                    'id' => $contact['id'],
                    'name' => $contact['name'],
                    'phone' => $contact['phone'] ?? null,
                    'email' => $contact['email'] ?? null,
                    'position' => $contact['position'] ?? null,
                ];
            }, $this->contacts) : [],
            /** @var array{id: string, name: string}[] $contractor_groups Группы контрагентов */
            'contractor_groups' => !empty($this->contractor_groups) ? array_map(function($group) {
                return [
                    'id' => $group['id'],
                    'name' => $group['name'],
                ];
            }, $this->contractor_groups) : [],
            /** @var array{id: string, name: string, path: string, size: int, mime_type: string}[] $files Файлы */
            'files' => !empty($this->files) ? array_map(function($file) {
                return [
                    'id' => $file['id'],
                    'name' => $file['name'],
                    'path' => $file['path'],
                    'size' => (int) $file['size'],
                    'mime_type' => $file['mime_type'],
                ];
            }, $this->files) : [],
        ];
    }
}
