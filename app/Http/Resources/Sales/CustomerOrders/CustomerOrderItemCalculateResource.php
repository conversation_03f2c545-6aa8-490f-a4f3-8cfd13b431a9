<?php

namespace App\Http\Resources\Sales\CustomerOrders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerOrderItemCalculateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * Используется для метода calculateMetrics в CustomerOrderItemController
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var int $shipped_quantity Отгруженное количество */
            'shipped_quantity' => (int) ($this->shipped_quantity ?? 0),
            /** @var int $available_quantity Доступное количество на складе */
            'available_quantity' => (int) ($this->available_quantity ?? 0),
            /** @var int $recidual Остаток на складе */
            'recidual' => (int) ($this->recidual ?? 0),
        ];
    }
}
