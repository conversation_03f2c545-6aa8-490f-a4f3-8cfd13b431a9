<?php

namespace App\Http\Resources\Api\Internal\WarehouseOrderScheme;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @var string $id
 * @var string $created_at
 * @var string $updated_at
 * @var string $warehouse_item_id
 * @var string $customer_order_item_id
 * @var int $reserved_quantity
 * @var string $reserved_at
 * @var string $status
 * @var string $reservation_type
 * @var string|null $document_type
 * @var string|null $document_id
 * @var int $priority
 * @var string|null $expires_at
 * @var bool $auto_release
 */
class WarehouseReservationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->id,
            'created_at' => (string) $this->created_at,
            'updated_at' => (string) $this->updated_at,
            'warehouse_item_id' => (string) $this->warehouse_item_id,
            'customer_order_item_id' => (string) $this->customer_order_item_id,
            'reserved_quantity' => (int) $this->reserved_quantity,
            'reserved_at' => (string) $this->reserved_at,
            'status' => (string) $this->status,
            'reservation_type' => (string) $this->reservation_type,
            'document_type' => $this->document_type,
            'document_id' => $this->document_id ? (string) $this->document_id : null,
            'priority' => (int) $this->priority,
            'expires_at' => $this->expires_at ? (string) $this->expires_at : null,
            'auto_release' => (bool) $this->auto_release,
        ];
    }
}
