<?php

namespace App\Http\Resources\Api\Internal\WarehouseOrderScheme;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @var string $id
 * @var string $created_at
 * @var string $updated_at
 * @var string $cabinet_id
 * @var string $employee_id
 * @var string $department_id
 * @var string $warehouse_id
 * @var string|null $number
 * @var string $date_from
 * @var string|null $status_id
 * @var bool $held
 * @var string|null $document_basis_type
 * @var string|null $document_basis_id
 * @var string $write_off_reason
 * @var string|null $reason_description
 * @var int $total_quantity
 * @var string $total_cost
 * @var string|null $comment
 * @var object|null $employee
 * @var object|null $department
 * @var object|null $warehouse
 * @var object|null $status
 */
class WarehouseIssueOrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->id,
            'created_at' => (string) $this->created_at,
            'updated_at' => (string) $this->updated_at,
            'cabinet_id' => (string) $this->cabinet_id,
            'employee_id' => (string) $this->employee_id,
            'department_id' => (string) $this->department_id,
            'warehouse_id' => (string) $this->warehouse_id,
            'number' => $this->number,
            'date_from' => (string) $this->date_from,
            'status_id' => $this->status_id ? (string) $this->status_id : null,
            'held' => (bool) $this->held,
            'document_basis_type' => $this->document_basis_type,
            'document_basis_id' => $this->document_basis_id ? (string) $this->document_basis_id : null,
            'write_off_reason' => (string) $this->write_off_reason,
            'reason_description' => $this->reason_description,
            'total_quantity' => (int) $this->total_quantity,
            'total_cost' => (string) $this->total_cost,
            'comment' => $this->comment,
            'employee' => $this->when($this->employee, function () {
                return is_string($this->employee) ? json_decode($this->employee, true) : $this->employee;
            }),
            'department' => $this->when($this->department, function () {
                return is_string($this->department) ? json_decode($this->department, true) : $this->department;
            }),
            'warehouse' => $this->when($this->warehouse, function () {
                return is_string($this->warehouse) ? json_decode($this->warehouse, true) : $this->warehouse;
            }),
            'status' => $this->when($this->status, function () {
                return is_string($this->status) ? json_decode($this->status, true) : $this->status;
            }),
        ];
    }
}
