<?php

namespace App\Http\Resources\Marketplaces\Ozon\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MatchedProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор сопоставленного товара */
            'id' => $this->id,
            /** @var string $created_at Дата создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции Ozon */
            'integration_id' => $this->integration_id,
            /** @var string|null $product_id Идентификатор товара в системе */
            'product_id' => $this->product_id,
            /** @var int $ozon_product_id Идентификатор товара в Ozon */
            'ozon_product_id' => (int) $this->ozon_product_id,
            /** @var string $title Название товара */
            'title' => $this->title,
            /** @var string $offer_id Артикул товара (offer_id) */
            'offer_id' => $this->offer_id,
            /** @var string|null $sku SKU товара */
            'sku' => $this->sku,
            /** @var array $barcodes Массив штрихкодов товара */
            'barcodes' => $this->barcodes ?? [],
            /** @var string $match_type Тип сопоставления */
            'match_type' => $this->match_type,
            /** @var bool $is_created Признак создания товара */
            'is_created' => (bool) $this->is_created,

            // Поля из JOIN с таблицей products
            /** @var string|null $product_title Название товара в системе */
            'product_title' => $this->product_title,
            /** @var string|null $product_article Артикул товара в системе */
            'product_article' => $this->product_article,
            /** @var string|null $product_code Код товара в системе */
            'product_code' => $this->product_code,
        ];
    }
}
