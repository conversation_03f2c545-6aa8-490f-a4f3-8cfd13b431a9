<?php

namespace App\Http\Resources\Marketplaces\Ozon\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class SuggestedMatchCollection extends ResourceCollection
{
    public $collects = SuggestedMatchResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'suggested_matches' => $this->collection,
        ];
    }
}
