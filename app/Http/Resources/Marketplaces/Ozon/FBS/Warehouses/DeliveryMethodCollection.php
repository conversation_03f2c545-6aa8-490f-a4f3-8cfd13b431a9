<?php

namespace App\Http\Resources\Marketplaces\Ozon\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class DeliveryMethodCollection extends ResourceCollection
{
    public $collects = DeliveryMethodResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'delivery_methods' => $this->collection,
        ];
    }
}
