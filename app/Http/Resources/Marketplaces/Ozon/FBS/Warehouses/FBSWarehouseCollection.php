<?php

namespace App\Http\Resources\Marketplaces\Ozon\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class FBSWarehouseCollection extends ResourceCollection
{
    public $collects = FBSWarehouseResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'warehouses' => $this->collection,
        ];
    }
}
