<?php

namespace App\Http\Resources\Marketplaces\Wildberries\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class MatchedProductCollection extends ResourceCollection
{
    public $collects = MatchedProductResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'matched_products' => $this->collection,
        ];
    }
}
