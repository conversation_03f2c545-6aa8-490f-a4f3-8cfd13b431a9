<?php

namespace App\Http\Resources\Marketplaces\Wildberries\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UnmatchedItemsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var array<int, array{id: string, order_id: string, nm_id: int|null, chrt_id: int|null, article: string|null, sku: string|null, quantity: int, price: string|null, is_matched: bool, products_to_match: array, suggested_products: array}> $unmatched_items Несопоставленные товары заказа */
            'unmatched_items' => collect($this->resource)->map(function ($item) {
                return [
                    /** @var string $id Идентификатор товара заказа */
                    'id' => $item->id,
                    /** @var string $order_id Идентификатор заказа */
                    'order_id' => $item->order_id,
                    /** @var int|null $nm_id Идентификатор номенклатуры в Wildberries */
                    'nm_id' => $item->nm_id,
                    /** @var int|null $chrt_id Идентификатор характеристики в Wildberries */
                    'chrt_id' => $item->chrt_id,
                    /** @var string|null $article Артикул товара */
                    'article' => $item->article,
                    /** @var string|null $sku SKU товара */
                    'sku' => $item->sku,
                    /** @var int $quantity Количество */
                    'quantity' => (int) $item->quantity,
                    /** @var string|null $price Цена товара */
                    'price' => $item->price ? (string) $item->price : null,
                    /** @var bool $is_matched Признак сопоставления */
                    'is_matched' => (bool) $item->is_matched,

                    /** @var array<int, array{id: string, cabinet_id: string, wildberries_integration_id: string, wb_id: int, size_id: int|null, title: string, brand: string, article: string, skus: array, is_matched: bool, created_at: string, updated_at: string}> $products_to_match Товары Wildberries для сопоставления */
                    'products_to_match' => collect($item->products_to_match ?? [])->map(function ($product) {
                        return [
                            /** @var string $id Идентификатор записи */
                            'id' => $product->id,
                            /** @var string $cabinet_id Идентификатор кабинета */
                            'cabinet_id' => $product->cabinet_id,
                            /** @var string $wildberries_integration_id Идентификатор интеграции Wildberries */
                            'wildberries_integration_id' => $product->wildberries_integration_id,
                            /** @var int $wb_id Идентификатор товара в Wildberries */
                            'wb_id' => (int) $product->wb_id,
                            /** @var int|null $size_id Идентификатор размера */
                            'size_id' => $product->size_id ? (int) $product->size_id : null,
                            /** @var string $title Название товара */
                            'title' => $product->title,
                            /** @var string $brand Бренд товара */
                            'brand' => $product->brand,
                            /** @var string $article Артикул товара */
                            'article' => $product->article,
                            /** @var array $skus Массив SKU товара */
                            'skus' => $product->skus ?? [],
                            /** @var bool $is_matched Признак сопоставления */
                            'is_matched' => (bool) $product->is_matched,
                            /** @var string $created_at Дата создания */
                            'created_at' => $product->created_at,
                            /** @var string $updated_at Дата обновления */
                            'updated_at' => $product->updated_at,
                        ];
                    })->toArray(),

                    /** @var array<int, array{id: string, title: string, article: string, code: string}> $suggested_products Предлагаемые товары из системы */
                    'suggested_products' => collect($item->suggested_products ?? [])->map(function ($product) {
                        return [
                            /** @var string $id Идентификатор товара */
                            'id' => $product->id,
                            /** @var string $title Название товара */
                            'title' => $product->title,
                            /** @var string $article Артикул товара */
                            'article' => $product->article,
                            /** @var string $code Код товара */
                            'code' => $product->code,
                        ];
                    })->toArray(),
                ];
            })->toArray(),
        ];
    }
}
