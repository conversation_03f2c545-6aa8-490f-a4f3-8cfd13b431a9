<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CabinetSettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'cabinet_id' => $this->cabinet_id,
            'numbering_type' => $this->numbering_type,
            'email' => $this->email,
            'global_numbering' => $this->global_numbering,
            'use_cabinet_email' => $this->use_cabinet_email,
            'check_stock' => $this->check_stock,
            'check_min_price' => $this->check_min_price,
            'use_bin' => $this->use_bin,
            'use_product_series' => $this->use_product_series,
            'auto_update_purchase_price' => $this->auto_update_purchase_price,
            'logo_image_id' => $this->logo_image_id,
            'logo_image' => $this->logo_image ? ImageResource::make($this->logo_image) : [],
        ];
    }
}
