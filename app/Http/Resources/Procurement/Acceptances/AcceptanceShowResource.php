<?php

namespace App\Http\Resources\Procurement\Acceptances;

use App\Http\Resources\FileResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Флаг общего доступа */
            'is_common' => (bool) $this->is_common,
            /** @var string $number Номер документа */
            'number' => (string) $this->number,
            /** @var string $date_from Дата начала действия */
            'date_from' => $this->date_from,
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var bool $held Флаг удержания */
            'held' => (bool) $this->held,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->warehouse_id,
            /** @var string|null $incoming_number Входящий номер */
            'incoming_number' => $this->incoming_number,
            /** @var string|null $incoming_date Дата поступления */
            'incoming_date' => $this->incoming_date,
            /** @var string|null $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string|null $currency_value Значение валюты */
            'currency_value' => $this->currency_value ? (string) $this->currency_value : null,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var bool $price_includes_vat Цена включает НДС */
            'price_includes_vat' => (bool) $this->price_includes_vat,
            /** @var bool $has_vat Наличие НДС */
            'has_vat' => (bool) $this->has_vat,
            /** @var string|null $overhead_cost Накладные расходы */
            'overhead_cost' => $this->overhead_cost ? (string) $this->overhead_cost : null,
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,

            /** @var array{id: string, title: string} $contractor Контрагент */
            'contractor' => $this->contractor ? [
                'id' => $this->contractor['id'],
                'title' => $this->contractor['title'],
            ] : [],
            /** @var array{id: string, name: string}|null $status Статус */
            'status' => $this->status ? [
                'id' => $this->status['id'],
                'name' => $this->status['name'],
            ] : [],

            /** @var array{id: string, name: string}|null $warehouse Склад */
            'warehouse' => $this->warehouse ? [
                'id' => $this->warehouse['id'],
                'name' => $this->warehouse['name']
            ] : [],

            /** @var AnonymousResourceCollection<FileResource>|null $files Файлы */
            'files' => $this->files ? FileResource::collection($this->files) : [],
        ];
    }
}
