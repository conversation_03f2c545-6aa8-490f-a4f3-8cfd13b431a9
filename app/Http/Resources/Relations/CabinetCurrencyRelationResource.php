<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CabinetCurrencyRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор валюты кабинета */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string|null $archived_at Дата архивирования */
            'archived_at' => $this->when(isset($this->resource['archived_at']), function () {
                return $this->resource['archived_at'];
            }),
            /** @var string|null $currency_id Идентификатор валюты */
            'currency_id' => $this->when(isset($this->resource['currency_id']), function () {
                return $this->resource['currency_id'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var bool|null $is_accouting Учетная */
            'is_accouting' => $this->when(isset($this->resource['is_accouting']), function () {
                return (bool) $this->resource['is_accouting'];
            }),
            /** @var string|null $external_id Внешний идентификатор */
            'external_id' => $this->when(isset($this->resource['external_id']), function () {
                return $this->resource['external_id'];
            }),
            /** @var string|null $num_code Числовой код */
            'num_code' => $this->when(isset($this->resource['num_code']), function () {
                return $this->resource['num_code'];
            }),
            /** @var string|null $char_code Символьный код */
            'char_code' => $this->when(isset($this->resource['char_code']), function () {
                return $this->resource['char_code'];
            }),
            /** @var string|null $short_name Краткое название */
            'short_name' => $this->when(isset($this->resource['short_name']), function () {
                return $this->resource['short_name'];
            }),
            /** @var string|null $name Название */
            'name' => $this->when(isset($this->resource['name']), function () {
                return $this->resource['name'];
            }),
            /** @var string|null $type Тип */
            'type' => $this->when(isset($this->resource['type']), function () {
                return $this->resource['type'];
            }),
            /** @var string|null $markup Наценка */
            'markup' => $this->when(isset($this->resource['markup']), function () {
                return (string) $this->resource['markup'];
            }),
            /** @var string|null $nominal Номинал */
            'nominal' => $this->when(isset($this->resource['nominal']), function () {
                return (string) $this->resource['nominal'];
            }),
            /** @var string|null $value Значение */
            'value' => $this->when(isset($this->resource['value']), function () {
                return (string) $this->resource['value'];
            }),
            /** @var bool|null $is_reverse Обратная */
            'is_reverse' => $this->when(isset($this->resource['is_reverse']), function () {
                return (bool) $this->resource['is_reverse'];
            }),
            /** @var string|null $pluralization Склонение */
            'pluralization' => $this->when(isset($this->resource['pluralization']), function () {
                return $this->resource['pluralization'];
            }),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var bool|null $is_common Общая */
            'is_common' => $this->when(isset($this->resource['is_common']), function () {
                return (bool) $this->resource['is_common'];
            }),
            /** @var bool|null $is_other Другая */
            'is_other' => $this->when(isset($this->resource['is_other']), function () {
                return (bool) $this->resource['is_other'];
            }),
        ];
    }
}
