<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MeasurementUnitRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор единицы измерения */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string|null $archived_at Дата архивирования */
            'archived_at' => $this->when(isset($this->resource['archived_at']), function () {
                return $this->resource['archived_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string $name Название единицы измерения */
            'name' => $this->when(isset($this->resource['name']), function () {
                return $this->resource['name'];
            }),
            /** @var string|null $short_name Краткое название */
            'short_name' => $this->when(isset($this->resource['short_name']), function () {
                return $this->resource['short_name'];
            }),
            /** @var string|null $code Код единицы измерения */
            'code' => $this->when(isset($this->resource['code']), function () {
                return $this->resource['code'];
            }),
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->when(isset($this->resource['external_code']), function () {
                return $this->resource['external_code'];
            }),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(isset($this->resource['is_default']), function () {
                return (bool) $this->resource['is_default'];
            }),
        ];
    }
}
