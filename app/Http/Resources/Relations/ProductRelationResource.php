<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор товара */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $archived_at Дата архивирования */
            'archived_at' => $this->when(isset($this->resource['archived_at']), function () {
                return $this->resource['archived_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string $title Название товара */
            'title' => $this->when(isset($this->resource['title']), function () {
                return $this->resource['title'];
            }),
            /** @var string|null $short_title Краткое название */
            'short_title' => $this->when(isset($this->resource['short_title']), function () {
                return $this->resource['short_title'];
            }),
            /** @var string|null $type Тип товара */
            'type' => $this->when(isset($this->resource['type']), function () {
                return $this->resource['type'];
            }),
            /** @var string|null $description Описание товара */
            'description' => $this->when(isset($this->resource['description']), function () {
                return $this->resource['description'];
            }),
            /** @var string|null $short_description Краткое описание */
            'short_description' => $this->when(isset($this->resource['short_description']), function () {
                return $this->resource['short_description'];
            }),
            /** @var bool|null $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => $this->when(isset($this->resource['discounts_retail_sales']), function () {
                return (bool) $this->resource['discounts_retail_sales'];
            }),
            /** @var string|null $product_group_id Идентификатор группы товаров */
            'product_group_id' => $this->when(isset($this->resource['product_group_id']), function () {
                return $this->resource['product_group_id'];
            }),
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->when(isset($this->resource['country_id']), function () {
                return $this->resource['country_id'];
            }),
            /** @var string|null $article Артикул товара */
            'article' => $this->when(isset($this->resource['article']), function () {
                return $this->resource['article'];
            }),
            /** @var string|null $code Код товара */
            'code' => $this->when(isset($this->resource['code']), function () {
                return $this->resource['code'];
            }),
            /** @var string|null $inner_code Внутренний код товара */
            'inner_code' => $this->when(isset($this->resource['inner_code']), function () {
                return $this->resource['inner_code'];
            }),
            /** @var string|null $external_code Внешний код товара */
            'external_code' => $this->when(isset($this->resource['external_code']), function () {
                return $this->resource['external_code'];
            }),
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->when(isset($this->resource['measurement_unit_id']), function () {
                return $this->resource['measurement_unit_id'];
            }),
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->when(isset($this->resource['brand_id']), function () {
                return $this->resource['brand_id'];
            }),
            /** @var string|null $min_price Минимальная цена */
            'min_price' => $this->when(isset($this->resource['min_price']), function () {
                return (string) $this->resource['min_price'];
            }),
            /** @var string|null $min_price_currency_id Идентификатор валюты минимальной цены */
            'min_price_currency_id' => $this->when(isset($this->resource['min_price_currency_id']), function () {
                return $this->resource['min_price_currency_id'];
            }),
            /** @var string|null $purchase_price Закупочная цена */
            'purchase_price' => $this->when(isset($this->resource['purchase_price']), function () {
                return (string) $this->resource['purchase_price'];
            }),
            /** @var string|null $purchase_price_currency_id Идентификатор валюты закупочной цены */
            'purchase_price_currency_id' => $this->when(isset($this->resource['purchase_price_currency_id']), function () {
                return $this->resource['purchase_price_currency_id'];
            }),
            /** @var string|null $length Длина */
            'length' => $this->when(isset($this->resource['length']), function () {
                return (string) $this->resource['length'];
            }),
            /** @var string|null $width Ширина */
            'width' => $this->when(isset($this->resource['width']), function () {
                return (string) $this->resource['width'];
            }),
            /** @var string|null $height Высота */
            'height' => $this->when(isset($this->resource['height']), function () {
                return (string) $this->resource['height'];
            }),
            /** @var string|null $weight Вес */
            'weight' => $this->when(isset($this->resource['weight']), function () {
                return (string) $this->resource['weight'];
            }),
            /** @var string|null $volume Объем */
            'volume' => $this->when(isset($this->resource['volume']), function () {
                return (string) $this->resource['volume'];
            }),
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->when(isset($this->resource['tax_id']), function () {
                return $this->resource['tax_id'];
            }),
            /** @var string|null $tax_system Система налогообложения */
            'tax_system' => $this->when(isset($this->resource['tax_system']), function () {
                return $this->resource['tax_system'];
            }),
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->when(isset($this->resource['indication_subject_calculation']), function () {
                return $this->resource['indication_subject_calculation'];
            }),
            /** @var string|null $threshold_type Тип порога */
            'threshold_type' => $this->when(isset($this->resource['threshold_type']), function () {
                return $this->resource['threshold_type'];
            }),
            /** @var int|null $threshold_count Количество порога */
            'threshold_count' => $this->when(isset($this->resource['threshold_count']), function () {
                return (int) $this->resource['threshold_count'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(isset($this->resource['contractor_id']), function () {
                return $this->resource['contractor_id'];
            }),
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->when(isset($this->resource['category_id']), function () {
                return $this->resource['category_id'];
            }),
            /** @var bool|null $is_default По умолчанию */
            'is_default' => $this->when(isset($this->resource['is_default']), function () {
                return (bool) $this->resource['is_default'];
            }),
        ];
    }
}
