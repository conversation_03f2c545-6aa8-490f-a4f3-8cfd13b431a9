<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LegalEntityRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор юридического лица */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $archived_at Дата архивирования */
            'archived_at' => $this->when(isset($this->resource['archived_at']), function () {
                return $this->resource['archived_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string|null $logo_image_id Идентификатор изображения логотипа */
            'logo_image_id' => $this->when(isset($this->resource['logo_image_id']), function () {
                return $this->resource['logo_image_id'];
            }),
            /** @var string $short_name Краткое название */
            'short_name' => $this->when(isset($this->resource['short_name']), function () {
                return $this->resource['short_name'];
            }),
            /** @var string|null $code Код */
            'code' => $this->when(isset($this->resource['code']), function () {
                return $this->resource['code'];
            }),
            /** @var string|null $phone Телефон */
            'phone' => $this->when(isset($this->resource['phone']), function () {
                return $this->resource['phone'];
            }),
            /** @var string|null $fax Факс */
            'fax' => $this->when(isset($this->resource['fax']), function () {
                return $this->resource['fax'];
            }),
            /** @var string|null $email Email */
            'email' => $this->when(isset($this->resource['email']), function () {
                return $this->resource['email'];
            }),
            /** @var string|null $discount_card Дисконтная карта */
            'discount_card' => $this->when(isset($this->resource['discount_card']), function () {
                return $this->resource['discount_card'];
            }),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(isset($this->resource['is_default']), function () {
                return (bool) $this->resource['is_default'];
            }),
            /** @var bool $shared_access Общий доступ */
            'shared_access' => $this->when(isset($this->resource['shared_access']), function () {
                return (bool) $this->resource['shared_access'];
            }),
        ];
    }
}
