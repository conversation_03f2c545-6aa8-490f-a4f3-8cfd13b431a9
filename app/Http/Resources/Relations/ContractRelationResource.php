<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор контракта */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string $number Номер контракта */
            'number' => $this->when(isset($this->resource['number']), function () {
                return $this->resource['number'];
            }),
            /** @var string $date_from Дата начала действия */
            'date_from' => $this->when(isset($this->resource['date_from']), function () {
                return $this->resource['date_from'];
            }),
            /** @var string|null $date_to Дата окончания действия */
            'date_to' => $this->when(isset($this->resource['date_to']), function () {
                return $this->resource['date_to'];
            }),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(isset($this->resource['contractor_id']), function () {
                return $this->resource['contractor_id'];
            }),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(isset($this->resource['legal_entity_id']), function () {
                return $this->resource['legal_entity_id'];
            }),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(isset($this->resource['comment']), function () {
                return $this->resource['comment'];
            }),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(isset($this->resource['is_default']), function () {
                return (bool) $this->resource['is_default'];
            }),
            /** @var bool $is_common Общий */
            'is_common' => $this->when(isset($this->resource['is_common']), function () {
                return (bool) $this->resource['is_common'];
            }),
        ];
    }
}
