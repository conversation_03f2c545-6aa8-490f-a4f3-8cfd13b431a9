<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttributeRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор атрибута */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string|null $attribute_groups_id Идентификатор группы атрибутов */
            'attribute_groups_id' => $this->when(isset($this->resource['attribute_groups_id']), function () {
                return $this->resource['attribute_groups_id'];
            }),
            /** @var string $name Название атрибута */
            'name' => $this->when(isset($this->resource['name']), function () {
                return $this->resource['name'];
            }),
            /** @var string|null $description Описание атрибута */
            'description' => $this->when(isset($this->resource['description']), function () {
                return $this->resource['description'];
            }),
            /** @var int $sort_order Порядок сортировки */
            'sort_order' => $this->when(isset($this->resource['sort_order']), function () {
                return (int) $this->resource['sort_order'];
            }),
            /** @var bool $status Статус */
            'status' => $this->when(isset($this->resource['status']), function () {
                return (bool) $this->resource['status'];
            }),
        ];
    }
}
