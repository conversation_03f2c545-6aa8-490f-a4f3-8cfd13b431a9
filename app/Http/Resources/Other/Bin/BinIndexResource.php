<?php

namespace App\Http\Resources\Other\Bin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BinIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор элемента корзины */
            'id' => $this->id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $record_id Идентификатор записи */
            'record_id' => $this->when(
                property_exists($this->resource, 'record_id'),
                function() {
                    return $this->resource->record_id;
                }
            ),
            /** @var string $table_name Название таблицы */
            'table_name' => $this->when(
                property_exists($this->resource, 'table_name'),
                function() {
                    return (string) $this->resource->table_name;
                }
            ),
            /** @var string|null $record_name Название записи */
            'record_name' => $this->when(
                property_exists($this->resource, 'record_name'),
                function() {
                    return $this->resource->record_name;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
        ];
    }
}
