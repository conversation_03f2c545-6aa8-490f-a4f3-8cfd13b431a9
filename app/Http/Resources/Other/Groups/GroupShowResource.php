<?php

namespace App\Http\Resources\Other\Groups;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GroupShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор группы */
            'id' => $this->id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $title Название группы */
            'title' => (string) $this->title,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
        ];
    }
}
