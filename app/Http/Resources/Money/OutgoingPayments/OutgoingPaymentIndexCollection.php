<?php

namespace App\Http\Resources\Money\OutgoingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OutgoingPaymentIndexCollection extends ResourceCollection
{
    public $resource = OutgoingPaymentIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => isset($this->additional['meta']) ? [
                /** @var int $current_page Текущая страница */
                'current_page' => $this->additional['meta']['current_page'],
                /** @var int $per_page Количество элементов на странице */
                'per_page' => $this->additional['meta']['per_page'],
                /** @var int $total Общее количество элементов */
                'total' => $this->additional['meta']['total'],
                /** @var int $last_page Последняя страница */
                'last_page' => $this->additional['meta']['last_page']
            ] : []
        ];
    }
}
