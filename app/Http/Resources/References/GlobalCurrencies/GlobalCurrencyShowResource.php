<?php

namespace App\Http\Resources\References\GlobalCurrencies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GlobalCurrencyShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор валюты */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $num_code Числовой код валюты */
            'num_code' => (string) $this->num_code,
            /** @var string $char_code Символьный код валюты */
            'char_code' => (string) $this->char_code,
            /** @var string $short_name Краткое название валюты */
            'short_name' => (string) $this->short_name,
            /** @var string $external_id Внешний идентификатор */
            'external_id' => (string) $this->external_id,
            /** @var string|null $name Полное название валюты */
            'name' => $this->name,
            /** @var string $value Курс валюты */
            'value' => (string) $this->value,
            /** @var string $currency_date Дата курса */
            'currency_date' => $this->currency_date,
            /** @var bool $is_default Валюта по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var array $pluralization Склонения валюты */
            'pluralization' => $this->pluralization ? (array) $this->pluralization : [],
        ];
    }
}
