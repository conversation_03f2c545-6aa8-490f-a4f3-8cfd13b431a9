<?php

namespace App\Http\Resources\References\SalesChannelTypes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalesChannelTypeShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор типа канала продаж */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $name Название типа канала продаж */
            'name' => (string) $this->name,
        ];
    }
}
