<?php

namespace App\Http\Resources\References\MeasurementUnits;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MeasurementUnitShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор единицы измерения */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $name Полное название единицы измерения */
            'name' => $this->name,
            /** @var string|null $short_name Краткое название единицы измерения */
            'short_name' => $this->short_name,
            /** @var string|null $code Код единицы измерения */
            'code' => $this->code,
            /** @var string $conversion_factor Коэффициент пересчета */
            'conversion_factor' => (string) $this->conversion_factor,
            /** @var string|null $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $group_id Идентификатор группы единиц измерения */
            'group_id' => $this->group_id,
            /** @var bool $is_default Единица измерения по умолчанию */
            'is_default' => (bool) $this->is_default,
        ];
    }
}
