<?php

namespace App\Http\Resources\References\ProfitTaxRates;

use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfitTaxRateIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор ставки налога на прибыль */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function () {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function () {
                    return $this->resource->department_id;
                }
            ),
            /** @var int $rate Ставка налога на прибыль в процентах */
            'rate' => $this->when(
                property_exists($this->resource, 'rate'),
                function () {
                    return (int) $this->resource->rate;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function () {
                    return $this->resource->description;
                }
            ),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function () {
                    return (bool) $this->resource->is_default;
                }
            ),

            /** @var EmployeeRelationResource|null $employees Информация о сотруднике */
            'employees' => $this->when(property_exists($this->resource, 'employees'), function () {
                return new EmployeeRelationResource($this->resource->employees ?? []);
            }),
            /** @var DepartmentRelationResource|null $departments Информация об отделе */
            'departments' => $this->when(property_exists($this->resource, 'departments'), function () {
                return new DepartmentRelationResource($this->resource->departments ?? []);
            }),
        ];
    }
}
