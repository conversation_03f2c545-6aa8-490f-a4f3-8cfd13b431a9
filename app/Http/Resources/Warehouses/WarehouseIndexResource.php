<?php

namespace App\Http\Resources\Warehouses;

use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\WarehouseAddressRelationResource;
use App\Http\Resources\Relations\WarehouseGroupRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function () {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function () {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $name Название склада */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function () {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $work_schedule_id Идентификатор рабочего расписания */
            'work_schedule_id' => $this->when(
                property_exists($this->resource, 'work_schedule_id'),
                function () {
                    return $this->resource->work_schedule_id;
                }
            ),
            /** @var bool $control_free_residuals Контроль свободных остатков */
            'control_free_residuals' => $this->when(
                property_exists($this->resource, 'control_free_residuals'),
                function () {
                    return (bool) $this->resource->control_free_residuals;
                }
            ),
            /** @var string|null $address_id Идентификатор адреса */
            'address_id' => $this->when(
                property_exists($this->resource, 'address_id'),
                function () {
                    return $this->resource->address_id;
                }
            ),
            /** @var string|null $phone_id Идентификатор телефона */
            'phone_id' => $this->when(
                property_exists($this->resource, 'phone_id'),
                function () {
                    return $this->resource->phone_id;
                }
            ),
            /** @var string|null $responsible_employee_id Идентификатор ответственного сотрудника */
            'responsible_employee_id' => $this->when(
                property_exists($this->resource, 'responsible_employee_id'),
                function () {
                    return $this->resource->responsible_employee_id;
                }
            ),
            /** @var bool $is_default Склад по умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function () {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function () {
                    return $this->resource->department_id;
                }
            ),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function () {
                    return $this->resource->employee_id;
                }
            ),
            /** @var bool $is_common Общий склад */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function () {
                    return (bool) $this->resource->is_common;
                }
            ),
            /** @var string|null $group_id Идентификатор группы складов */
            'group_id' => $this->when(
                property_exists($this->resource, 'group_id'),
                function () {
                    return $this->resource->group_id;
                }
            ),
            /** @var string|null $source_type Источник склада */
            'source_type' => $this->when(
                property_exists($this->resource, 'source_type'),
                function () {
                    return $this->resource->source_type;
                }
            ),

            /** @var WarehouseAddressRelationResource|null $warehouse_addresses Информация об адресе склада */
            'warehouse_addresses' => $this->when(property_exists($this->resource, 'warehouse_addresses'), function () {
                return new WarehouseAddressRelationResource($this->resource->warehouse_addresses ?? []);
            }),
            /** @var WarehouseGroupRelationResource|null $warehouse_groups Информация о группе складов */
            'warehouse_groups' => $this->when(property_exists($this->resource, 'warehouse_groups'), function () {
                return new WarehouseGroupRelationResource($this->resource->warehouse_groups ?? []);
            }),
            /** @var EmployeeRelationResource|null $employees Информация о сотруднике */
            'employees' => $this->when(property_exists($this->resource, 'employees'), function () {
                return new EmployeeRelationResource($this->resource->employees ?? []);
            }),
            /** @var DepartmentRelationResource|null $departments Информация об отделе */
            'departments' => $this->when(property_exists($this->resource, 'departments'), function () {
                return new DepartmentRelationResource($this->resource->departments ?? []);
            }),
            /** @var EmployeeRelationResource|null $responsible_employees Информация об ответственном сотруднике */
            'responsible_employees' => $this->when(property_exists($this->resource, 'responsible_employees'), function () {
                return new EmployeeRelationResource($this->resource->responsible_employees ?? []);
            }),
        ];
    }
}
