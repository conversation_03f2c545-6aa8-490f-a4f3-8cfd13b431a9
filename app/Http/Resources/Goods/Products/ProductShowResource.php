<?php

namespace App\Http\Resources\Goods\Products;

use App\Http\Resources\ImageResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор товара */
            'id' => $this->id,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $title Название товара */
            'title' => (string) $this->title,
            /** @var string $short_title Короткое название */
            'short_title' => (string) $this->short_title,
            /** @var int $type Тип товара */
            'type' => (int) $this->type,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $short_description Короткое описание */
            'short_description' => $this->short_description,
            /** @var bool $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => (bool) $this->discounts_retail_sales,
            /** @var string|null $product_group_id Идентификатор группы товаров */
            'product_group_id' => $this->product_group_id,
            /** @var string|null $article Артикул */
            'article' => $this->article,
            /** @var string|null $code Код товара */
            'code' => $this->code,
            /** @var int|null $inner_code Внутренний код */
            'inner_code' => $this->inner_code ? (int) $this->inner_code : null,
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->external_code,
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->measurement_unit_id,
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->brand_id,
            /** @var object{value: string, currency_id: string} $min_price Минимальная цена */
            'min_price' => [
                'value' => (string) $this->min_price,
                'currency_id' => $this->min_price_currency_id,
            ],
            /** @var object{value: string, currency_id: string} $purchase_price Закупочная цена */
            'purchase_price' => [
                'value' => (string) $this->purchase_price,
                'currency_id' => $this->purchase_price_currency_id,
            ],
            /** @var int|null $threshold_type Тип порога */
            'threshold_type' => $this->threshold_type ? (int) $this->threshold_type : null,
            /** @var int|null $threshold_count Количество порога */
            'threshold_count' => $this->threshold_count ? (int) $this->threshold_count : null,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->category_id,
            /** @var bool $is_default Товар по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var array{length: string, width: string, height: string, weight: string, volume: string} $dimensions Габариты */
            'dimensions' => [
                'length' => (string) $this->length,
                'width' => (string) $this->width,
                'height' => (string) $this->height,
                'weight' => (string) $this->weight,
                'volume' => (string) $this->volume,
            ],
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->tax_id,
            /** @var string|null $tax_system Система налогообложения */
            'tax_system' => $this->tax_system,
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->indication_subject_calculation,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $egais_codes Коды ЕГАИС */
            'egais_codes' => $this->egais_codes,
            /** @var array{id: string, url: string}[] $images Изображения */
            'images' => $this->images ? ImageResource::collection($this->images) : [],
            /** @var array{id: string, value: string, type: string, sort: int}[] $barcodes Штрихкоды */
            'barcodes' => $this->barcodes ? array_map(function ($barcode) {
                return [
                    'id' => $barcode['id'],
                    'value' => $barcode['value'],
                    'type' => $barcode['type'],
                    'sort' => (int) $barcode['sort'],
                ];
            }, $this->barcodes) : [],
            /** @var array{id: string, name: string} $categories Категория */
            'categories' => !empty($this->categories) ? [
                'id' => $this->categories['id'],
                'name' => $this->categories['name'],
            ] : (object) [],
            /** @var array{id: string, name: string} $measurement_unit Единица измерения */
            'measurement_unit' => !empty($this->measurement_unit) ? [
                'id' => $this->measurement_unit['id'],
                'name' => $this->measurement_unit['name'],
            ] : (object) [],
            /** @var array{id: string, name: string} $brand Бренд */
            'brand' => !empty($this->brand) ? [
                'id' => $this->brand['id'],
                'name' => $this->brand['name'],
            ] : (object) [],
            /** @var array{id: string, title: string} $contractor Контрагент */
            'contractor' => !empty($this->contractor) ? [
                'id' => $this->contractor['id'],
                'title' => $this->contractor['title'],
            ] : (object) [],
            /** @var array{id: string, name: string} $country Страна */
            'country' => !empty($this->country) ? [
                'id' => $this->country['id'],
                'name' => $this->country['name'],
            ] : (object) [],
            /** @var array{id: string, name: string} $product_group Группа товаров */
            'product_group' => !empty($this->product_group) ? [
                'id' => $this->product_group['id'],
                'name' => $this->product_group['name'],
            ] : (object) [],
            /** @var array{pack_type: int|null, type_accounting: int|null, accounting_series: bool|null, product_siz_name_id: string|null, product_siz_type_id: string|null, product_type_code: string|null, container_capacity: string|null, strength: string|null, excise: bool|null, tnwed_id: string|null, target_gender: int|null, type_production: int|null, age_category: int|null, set: bool|null, partial_sale: bool|null, model: string|null, traceable: bool|null} $accounting_features Особенности учета */
            'accounting_features' => !empty($this->accounting_features) ? [
                'pack_type' => $this->accounting_features['pack_type'] ? (int) $this->accounting_features['pack_type'] : null,
                'type_accounting' => $this->accounting_features['type_accounting'] ? (int) $this->accounting_features['type_accounting'] : null,
                'accounting_series' => $this->accounting_features['accounting_series'] ? (bool) $this->accounting_features['accounting_series'] : null,
                'product_siz_name_id' => $this->accounting_features['product_siz_name_id'] ?? null,
                'product_siz_type_id' => $this->accounting_features['product_siz_type_id'] ?? null,
                'product_type_code' => $this->accounting_features['product_type_code'] ?? null,
                'container_capacity' => $this->accounting_features['container_capacity'] ?? null,
                'strength' => $this->accounting_features['strength'] ?? null,
                'excise' => $this->accounting_features['excise'] ? (bool) $this->accounting_features['excise'] : null,
                'tnwed_id' => $this->accounting_features['tnwed_id'] ?? null,
                'target_gender' => $this->accounting_features['target_gender'] ? (int) $this->accounting_features['target_gender'] : null,
                'type_production' => $this->accounting_features['type_production'] ? (int) $this->accounting_features['type_production'] : null,
                'age_category' => $this->accounting_features['age_category'] ? (int) $this->accounting_features['age_category'] : null,
                'set' => $this->accounting_features['set'] ? (bool) $this->accounting_features['set'] : null,
                'partial_sale' => $this->accounting_features['partial_sale'] ? (bool) $this->accounting_features['partial_sale'] : null,
                'model' => $this->accounting_features['model'] ?? null,
                'traceable' => $this->accounting_features['traceable'] ? (bool) $this->accounting_features['traceable'] : null,
            ] : (object) [],
            /** @var array{id: string, quantity: int, measurement_unit_quantity_id: string, packing_id: string, barcodes: array{id: string, barcode: string, type: string, sort: int}[]}[] $packings Упаковки */
            'packings' => $this->packings ? array_map(function ($packing) {
                return [
                    'id' => $packing['id'],
                    'quantity' => (int) $packing['quantity'],
                    'measurement_unit_quantity_id' => $packing['measurement_unit_quantity_id'],
                    'packing_id' => $packing['packing_id'],
                    'barcodes' => $packing['barcodes'] ? array_map(function ($barcode) {
                        return [
                            'id' => $barcode['id'],
                            'barcode' => $barcode['barcode'],
                            'type' => $barcode['type'],
                            'sort' => (int) $barcode['sort'],
                        ];
                    }, $packing['barcodes']) : [],
                ];
            }, $this->packings) : [],
            /** @var array{cp_id: string, cp_title: string, amount: string, char_code: string}[] $product_prices Цены товара */
            'product_prices' => $this->product_prices ? array_map(function ($price) {
                return [
                    'cp_id' => $price['cp_id'],
                    'cp_title' => $price['cp_title'],
                    'amount' => (string) $price['amount'],
                    'char_code' => $price['char_code'],
                ];
            }, $this->product_prices) : [],
            /** @var array{atr_id: string, atr_name: string, atr_val: string, atr_gr: string}[] $product_attribute Атрибуты товара */
            'product_attribute' => $this->product_attribute ? array_map(function ($attribute) {
                return [
                    'atr_id' => $attribute['atr_id'],
                    'atr_name' => $attribute['atr_name'],
                    'atr_val' => $attribute['atr_val'],
                    'atr_gr' => $attribute['atr_gr'],
                ];
            }, $this->product_attribute) : [],
        ];
    }
}
