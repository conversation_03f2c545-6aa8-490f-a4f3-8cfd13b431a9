<?php

namespace App\Http\Resources\Goods\Attributes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttributeShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор атрибута */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string|null $attribute_groups_id Идентификатор группы атрибутов */
            'attribute_groups_id' => $this->attribute_groups_id,
            /** @var string $name Название атрибута */
            'name' => (string) $this->name,
            /** @var string|null $description Описание атрибута */
            'description' => $this->description,
            /** @var int $sort_order Порядок сортировки */
            'sort_order' => (int) $this->sort_order,
            /** @var bool $status Статус активности */
            'status' => (bool) $this->status,
            /** @var array{id: string, name: string} $attribute_groups Группа атрибутов */
            'attribute_groups' => !empty($this->attributeGroups) ? [
                'id' => $this->attributeGroups->id,
                'name' => $this->attributeGroups->name,
            ] : (object) [],
            /** @var array{id: string, name: string} $cabinet Кабинет */
            'cabinet' => !empty($this->cabinet) ? [
                'id' => $this->cabinet->id,
                'name' => $this->cabinet->name,
            ] : (object) [],
            /** @var array{id: string, value: string}[] $attribute_values Значения атрибута */
            'attribute_values' => !empty($this->attributeValues) ? $this->attributeValues->map(function($value) {
                return [
                    'id' => $value->id,
                    'value' => $value->value,
                ];
            })->toArray() : [],
            /** @var array{id: string, name: string}[] $categories Категории */
            'categories' => !empty($this->categories) ? $this->categories->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                ];
            })->toArray() : [],
        ];
    }
}
