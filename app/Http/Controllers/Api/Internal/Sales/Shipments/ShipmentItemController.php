<?php

namespace App\Http\Controllers\Api\Internal\Sales\Shipments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\ShipmentItemPolicyContract;
use App\Contracts\Services\Internal\Sales\ShipmentItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ShipmentItems\ShipmentItemCalculateRequest;
use App\Http\Requests\Api\Internal\ShipmentItems\ShipmentItemIndexRequest;
use App\Http\Requests\Api\Internal\ShipmentItems\ShipmentItemStoreRequest;
use App\Http\Requests\Api\Internal\ShipmentItems\ShipmentItemUpdateRequest;
use App\Http\Resources\Sales\Shipments\ShipmentItemIndexCollection;
use App\Http\Resources\Sales\Shipments\ShipmentItemShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ShipmentItemController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ShipmentItemsServiceContract $service,
        private readonly ShipmentItemPolicyContract $policy
    ) {
    }

    /**
     * @response ShipmentItemIndexCollection<ShipmentItemIndexResource>
     */
    public function index(ShipmentItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);

            $collection = new ShipmentItemIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(ShipmentItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ShipmentItemShowResource::make($data));
        });
    }

    public function update(ShipmentItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции отгрузки
     */
    public function calculateMetrics(ShipmentItemCalculateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->policy->calculateMetrics($dto);

            $data = $this->service->calculateMetrics($dto);
            return $this->successResponse($data);
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
