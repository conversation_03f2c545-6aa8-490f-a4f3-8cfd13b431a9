<?php

namespace App\Http\Controllers\Api\Internal\Sales\CustomerOrders;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\CustomerOrderPolicyContract;
use App\Contracts\Services\Internal\Sales\CustomerOrdersServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\CustomerOrders\CustomerOrderBulkRequest;
use App\Http\Requests\Api\Internal\CustomerOrders\CustomerOrderIndexRequest;
use App\Http\Requests\Api\Internal\CustomerOrders\CustomerOrderStoreRequest;
use App\Http\Requests\Api\Internal\CustomerOrders\CustomerOrderUpdateRequest;
use App\Http\Resources\Sales\CustomerOrders\CustomerOrderIndexCollection;
use App\Http\Resources\Sales\CustomerOrders\CustomerOrderShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerOrdersController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly CustomerOrdersServiceContract $service,
        private readonly CustomerOrderPolicyContract $policy
    ) {

    }

    /**
     * @response CustomerOrderIndexCollection<CustomerOrderIndexResource>
     */
    public function index(CustomerOrderIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new CustomerOrderIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(CustomerOrderStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response CustomerOrderShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(CustomerOrderShowResource::make($data));
        });
    }

    public function update(CustomerOrderUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(CustomerOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkHeld(CustomerOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkHeld($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkUnheld(CustomerOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkUnheld($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkReserve(CustomerOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $this->service->bulkReserve($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkUnreserve(CustomerOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $this->service->bulkUnreserve($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkCopy(CustomerOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            $this->service->bulkCopy($data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
