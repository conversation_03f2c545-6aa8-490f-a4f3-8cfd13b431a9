<?php

namespace App\Http\Controllers\Api\Internal\Sales\CustomerOrders;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\CustomerOrderItemPolicyContract;
use App\Contracts\Services\Internal\Sales\CustomerOrderItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\CustomerOrderItems\CustomerOrderItemCalculateRequest;
use App\Http\Requests\Api\Internal\CustomerOrderItems\CustomerOrderItemIndexRequest;
use App\Http\Requests\Api\Internal\CustomerOrderItems\CustomerOrderItemStoreRequest;
use App\Http\Requests\Api\Internal\CustomerOrderItems\CustomerOrderItemUpdateRequest;
use App\Http\Resources\Sales\CustomerOrders\CustomerOrderItemCalculateResource;
use App\Http\Resources\Sales\CustomerOrders\CustomerOrderItemIndexCollection;
use App\Http\Resources\Sales\CustomerOrders\CustomerOrderItemShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerOrderItemController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly CustomerOrderItemsServiceContract $service,
        private readonly CustomerOrderItemPolicyContract $policy
    ) {
    }

    /**
     * @response CustomerOrderItemIndexCollection<CustomerOrderItemIndexResource>
     */
    public function index(CustomerOrderItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $this->policy->index($data->id);
            $result = $this->service->index($data);

            $collection = new CustomerOrderItemIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    public function store(CustomerOrderItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response CustomerOrderItemShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(CustomerOrderItemShowResource::make($data));
        });
    }

    public function update(CustomerOrderItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции заказа покупателя
     * @response CustomerOrderItemCalculateResource
     */
    public function calculateMetrics(CustomerOrderItemCalculateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->policy->calculateMetrics($dto);

            $data = $this->service->calculateMetrics($dto);
            return $this->successResponse(CustomerOrderItemCalculateResource::make($data));
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
