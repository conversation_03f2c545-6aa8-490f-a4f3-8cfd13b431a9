<?php

namespace App\Http\Controllers\Api\Internal\Ozon;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\OzonV3PostingFbsListPolicyContract;
use App\Http\Requests\Api\Internal\Ozon\OzonV3PostingFbsListRequest;
use App\Contracts\Services\Internal\OzonV3PostingFbsListServiceContract;
use App\Http\Requests\Api\Internal\Ozon\OzonV3PostingFbsListIndexRequest;

class OzonV3PostingFbsListController extends Controller
{
  use ApiPolicy;

  public function __construct(
      private readonly OzonV3PostingFbsListServiceContract $service,
      private readonly OzonV3PostingFbsListPolicyContract $policy
  ) {
  }

    /**
   * Display a listing of the resource.
   */
  public function index(OzonV3PostingFbsListIndexRequest $request): JsonResponse
  {
    return $this->executeAction(function () use ($request) {
        $data = $request->toDTO();
        $data = $this->service->index($data);
        return $this->successResponse($data);
    });
  }

  public function store(OzonV3PostingFbsListRequest $request): ?JsonResponse
  {
      return $this->executeAction(function () use ($request) {
          $data = $request->toDto();

          $this->authorizeCreate($request, $data);

          $this->service->create($data);
          return $this->successResponse();
      });
  }

  /**
   * Display the specified resource.
   */
  public function show(Request $request, string $id): JsonResponse
  {
      return $this->executeAction(function () use ($request, $id) {
          $this->authorizeView($request, $id);

          $data = $this->service->show($id);
          return $this->successResponse($data);
      });
  }

  protected function getPolicy(): BaseResourcePolicyContract
  {
      return $this->policy;
  }

}
