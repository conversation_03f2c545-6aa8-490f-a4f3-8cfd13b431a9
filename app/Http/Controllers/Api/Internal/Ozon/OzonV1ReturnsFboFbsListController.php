<?php

namespace App\Http\Controllers\Api\Internal\Ozon;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\OzonV1ReturnsFboFbsListPolicyContract;
use App\Http\Requests\Api\Internal\Ozon\Returns\OzonV1ReturnsFboFbsListRequest;
use App\Contracts\Services\Internal\OzonV1ReturnsFboFbsListServiceContract;
use App\Http\Requests\Api\Internal\Ozon\Returns\OzonV1ReturnsFboFbsListIndexRequest;

class OzonV1ReturnsFboFbsListController extends Controller
{
  use ApiPolicy;
 
  public function __construct(
      private readonly OzonV1ReturnsFboFbsListServiceContract $service,
      private readonly OzonV1ReturnsFboFbsListPolicyContract $policy
  ) {
  }
  
    /**
   * Display a listing of the resource.
   */
  public function index(OzonV1ReturnsFboFbsListIndexRequest $request): JsonResponse
  {
      return $this->executeAction(function () use ($request) {
          $data = $this->service->index($request->validated());
          return $this->successResponse($data);
      });
  }

  public function store(OzonV1ReturnsFboFbsListRequest $request): ?JsonResponse
  {
      return $this->executeAction(function () use ($request) {
          $data = $request->toDto();

          $this->authorizeCreate($request, $data);

          $this->service->create($data);
          return $this->successResponse();
      });
  }

  /**
   * Display the specified resource.
   */
  public function show(Request $request, string $id): JsonResponse
  {
      return $this->executeAction(function () use ($request, $id) {
          $this->authorizeView($request, $id);

          $data = $this->service->show($id);
          return $this->successResponse($data);
      });
  }

  protected function getPolicy(): BaseResourcePolicyContract
  {
      return $this->policy;
  }
  
}