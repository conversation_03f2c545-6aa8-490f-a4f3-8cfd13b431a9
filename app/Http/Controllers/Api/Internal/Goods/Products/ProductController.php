<?php

namespace App\Http\Controllers\Api\Internal\Goods\Products;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\ProductPolicyContract;
use App\Contracts\Services\Internal\Products\ProductsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Products\ProductIndexRequest;
use App\Http\Requests\Api\Internal\Products\ProductStoreRequest;
use App\Http\Requests\Api\Internal\Products\ProductUpdateRequest;
use App\Http\Resources\Goods\Products\ProductIndexCollection;
use App\Http\Resources\Goods\Products\ProductShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ProductsServiceContract $productsServiceContract,
        private readonly ProductPolicyContract $policy
    ) {
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }

    /**
     * Display a listing of the resource.
     *
     * @response ProductIndexCollection<ProductIndexResource>
     */
    public function index(ProductIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $products = $this->productsServiceContract->index($data);
            $collection = new ProductIndexCollection($products['data']);
            return $this->successResponse($collection->additional(['meta' => $products['meta']]));
        });
    }

    public function deleteProductsCount(string $id): JsonResponse
    {
        // удалить все продукты у кабинета и его связки
        DB::table('packings')->where('cabinet_id', $id)->delete();
        DB::table('barcodes')->where('cabinet_id', $id)->delete();
        DB::table('products')->where('cabinet_id', $id)->delete();

        return response()->json([], 204);

    }

    /**
     * Store a newly created resource in storage.
     * ProductStoreRequest
     */
    public function store(ProductStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $product_id = $this->productsServiceContract->create($data);
            return $this->createdResponse($product_id);
        });

    }


    /**
     * @response ProductShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $product = $this->productsServiceContract->show($id);
            return $this->successResponse(ProductShowResource::make($product));
        });
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ProductUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {

            $data = $request->toDto();

            $data->resourceId = $id;

            $this->authorizeUpdate($request, $data);

            $this->productsServiceContract->update($data);

            return $this->noContentResponse();
        });

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {

        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->productsServiceContract->delete($id);
            return $this->noContentResponse();
        });

    }

}
