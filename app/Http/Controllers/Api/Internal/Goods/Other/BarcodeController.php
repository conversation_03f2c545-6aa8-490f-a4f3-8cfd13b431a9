<?php

namespace App\Http\Controllers\Api\Internal\Goods\Other;

use App\Contracts\Policies\BarcodePolicyContract;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Services\Internal\BarcodesServiceContract;
use App\Helpers\StringGenerator;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeBulkStoreRequest;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeBulkUpdateRequest;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeCodeGenerateRequest;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeIndexRequest;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeStoreRequest;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeUpdateRequest;
use App\Http\Requests\Api\Internal\Barcodes\BarcodeValidateRequest;
use App\Traits\ApiPolicy;
use App\Traits\HasBarcodes;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BarcodeController extends Controller
{
    use ApiPolicy;
    use HasBarcodes;

    public function __construct(
        private readonly BarcodePolicyContract $policy,
        private readonly BarcodesServiceContract $barcode,
    ) {
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }


    public function index(BarcodeIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $this->policy->index($request->validated('cabinet_id'));

            $data = $request->toDTO();
            $result = $this->barcode->index($data);
            return $this->successResponse($result);
        });
    }


    public function store(BarcodeStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->barcode->create($data);
            return $this->createdResponse($id);
        });
    }

    public function bulkStore(BarcodeBulkStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->policy->bulkStore($data);

            $this->barcode->bulkCreate($data);
            return $this->noContentResponse();
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->barcode->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(BarcodeUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->barcode->update($data);
            return $this->noContentResponse();
        });
    }

    public function bulkUpdate(BarcodeBulkUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->policy->bulkUpdate($data);

            $this->barcode->bulkUpdate($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->barcode->delete($id);
            return $this->noContentResponse();
        });
    }


    public function codeAndBarcodeGenerate(BarcodeCodeGenerateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->barcode->generateCodeAndBarcodeByCabinetId($data['cabinet_id']);
            return $this->successResponse($data);
        });
    }

    public function codeGenerate(BarcodeCodeGenerateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->barcode->generateCodeByCabinetId($data['cabinet_id']);
            return $this->successResponse($data);
        });
    }

    public function barcodeGenerateEAN13(BarcodeValidateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->codeGenerateEAN13($data);
            return $this->successResponse($data);
        });
    }

    public function validateBarcodeEAN13(BarcodeValidateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->validateEAN13($data);
            return $this->successResponse($data);
        });
    }

    public function barcodeGenerateEAN8(BarcodeValidateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->codeGenerateEAN8($data);
            return $this->successResponse($data);
        });
    }

    public function validateBarcodeEAN8(BarcodeValidateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->validateEAN8($data);
            return $this->successResponse($data);
        });
    }

    public function barcodeGenerateEAN13ForCabinet(BarcodeCodeGenerateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->barcode->getBarcodeGenerateEAN13ForCabinet($data['cabinet_id']);
            return $this->successResponse($data);
        });
    }

    public function barcodeGenerateEAN8ForCabinet(BarcodeCodeGenerateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $data = $this->barcode->getBarcodeGenerateEAN8ForCabinet($data['cabinet_id']);
            return $this->successResponse($data);
        });
    }

    public function externalCodeGenerate(): JsonResponse
    {
        return $this->executeAction(function () {
            $data = StringGenerator::generateExternalCode();
            return $this->successResponse($data);
        });
    }
}
