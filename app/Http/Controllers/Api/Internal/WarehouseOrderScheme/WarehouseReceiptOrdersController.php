<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderStoreRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderUpdateRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderResource;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderCollection;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseReceiptOrdersController extends Controller
{
    public function __construct(
        private readonly WarehouseReceiptOrdersRepositoryContract $repository,
        private readonly WarehouseOrderSchemeDetectionServiceContract $orderSchemeService
    ) {
    }

    /**
     * @response 200 WarehouseReceiptOrderCollection<WarehouseReceiptOrderResource>
     */
    public function index(Request $request): WarehouseReceiptOrderCollection
    {
        $dto = IndexRequestDTO::fromRequest($request);
        $orders = $this->repository->getByWarehouse($dto->filters['warehouse_id'] ?? '', $dto->filters);
        
        return WarehouseReceiptOrderCollection::make($orders);
    }

    /**
     * @response 200 WarehouseReceiptOrderResource
     */
    public function show(string $id): WarehouseReceiptOrderResource
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Приходный ордер не найден');
        }
        
        return WarehouseReceiptOrderResource::make($order);
    }

    /**
     * @response 201 {"id": "uuid", "message": "Приходный ордер создан"}
     */
    public function store(WarehouseReceiptOrderStoreRequest $request): JsonResponse
    {
        // Проверяем что склад работает в ордерной схеме
        $isOrderSchemeActive = $this->orderSchemeService
            ->isOrderSchemeActiveForReceipts($request->warehouse_id, $request->date_from);
            
        if (!$isOrderSchemeActive) {
            return response()->json([
                'error' => 'Склад не работает в ордерной схеме для приемок'
            ], 422);
        }

        $data = $request->validated();
        $data['id'] = \Illuminate\Support\Str::uuid()->toString();
        
        $this->repository->insert($data);
        
        return response()->json([
            'id' => $data['id'],
            'message' => 'Приходный ордер создан'
        ], 201);
    }

    /**
     * @response 200 {"message": "Приходный ордер обновлен"}
     */
    public function update(string $id, WarehouseReceiptOrderUpdateRequest $request): JsonResponse
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Приходный ордер не найден');
        }
        
        if ($order->held) {
            return response()->json([
                'error' => 'Нельзя изменять проведенный приходный ордер'
            ], 422);
        }
        
        $this->repository->update($id, $request->validated());
        
        return response()->json([
            'message' => 'Приходный ордер обновлен'
        ]);
    }

    /**
     * @response 200 {"message": "Приходный ордер удален"}
     */
    public function destroy(string $id): JsonResponse
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Приходный ордер не найден');
        }
        
        if ($order->held) {
            return response()->json([
                'error' => 'Нельзя удалять проведенный приходный ордер'
            ], 422);
        }
        
        $this->repository->delete($id);
        
        return response()->json([
            'message' => 'Приходный ордер удален'
        ]);
    }

    /**
     * @response 200 {"message": "Приходный ордер проведен"}
     */
    public function hold(string $id): JsonResponse
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Приходный ордер не найден');
        }
        
        if ($order->held) {
            return response()->json([
                'error' => 'Приходный ордер уже проведен'
            ], 422);
        }
        
        $this->repository->markAsHeld($id);
        
        return response()->json([
            'message' => 'Приходный ордер проведен'
        ]);
    }
}
