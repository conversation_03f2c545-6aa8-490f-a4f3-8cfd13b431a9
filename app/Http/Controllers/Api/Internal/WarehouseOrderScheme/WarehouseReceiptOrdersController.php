<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Policies\WarehouseOrderScheme\WarehouseReceiptOrderPolicyContract;
use App\Contracts\Services\Internal\WarehouseReceiptOrdersServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderIndexRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderStoreRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderUpdateRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderCollection;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseReceiptOrderResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseReceiptOrdersController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseReceiptOrdersServiceContract $service,
        private readonly WarehouseReceiptOrderPolicyContract $policy
    ) {
    }

    protected function getPolicy(): WarehouseReceiptOrderPolicyContract
    {
        return $this->policy;
    }

    /**
     * @response 200 WarehouseReceiptOrderCollection<WarehouseReceiptOrderResource>
     */
    public function index(WarehouseReceiptOrderIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $result = $this->service->index($request->toDTO());

            $collection = new WarehouseReceiptOrderCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    /**
     * @response 200 WarehouseReceiptOrderResource
     */
    public function show(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeView($request, $id);

            $order = $this->service->show($id);

            if (!$order) {
                abort(404, 'Приходный ордер не найден');
            }

            return $this->successResponse(new WarehouseReceiptOrderResource($order));
        });
    }

    /**
     * @response 201 {"id": "uuid", "message": "Приходный ордер создан"}
     */
    public function store(WarehouseReceiptOrderStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->authorizeCreate($request, $dto);

            $id = $this->service->create($dto);

            return $this->successResponse([
                'id' => $id,
                'message' => 'Приходный ордер создан'
            ], 201);
        });
    }

    /**
     * @response 200 {"message": "Приходный ордер обновлен"}
     */
    public function update(string $id, WarehouseReceiptOrderUpdateRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $dto = $request->toDTO();

            $this->authorizeUpdate($request, $dto);

            $this->service->update($id, $dto);

            return $this->successResponse([
                'message' => 'Приходный ордер обновлен'
            ]);
        });
    }

    /**
     * @response 200 {"message": "Приходный ордер удален"}
     */
    public function destroy(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);

            return $this->successResponse([
                'message' => 'Приходный ордер удален'
            ]);
        });
    }

    /**
     * @response 200 {"message": "Приходный ордер проведен"}
     */
    public function hold(string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($id) {
            $this->policy->hold($id);

            $this->service->hold($id);

            return $this->successResponse([
                'message' => 'Приходный ордер проведен'
            ]);
        });
    }
}
