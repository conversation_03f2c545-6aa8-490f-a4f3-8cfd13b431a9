<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeReportsServiceContract;
use App\Http\Controllers\Controller;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseOrderSchemeReportsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseOrderSchemeReportsServiceContract $service
    ) {
    }

    /**
     * @response 200 {"movements": [], "summary": [], "period": {"from": "2024-01-01", "to": "2024-01-31"}}
     */
    public function stockMovementReport(Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $filters = $request->validate([
                'warehouse_id' => 'required|uuid|exists:warehouses,id',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'product_id' => 'nullable|uuid|exists:products,id'
            ]);

            $report = $this->service->getStockMovementReport($filters);

            return $this->successResponse($report);
        });
    }

    /**
     * @response 200 {"reservations": [], "summary": [], "period": {"from": "2024-01-01", "to": "2024-01-31"}}
     */
    public function reservationReport(Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $filters = $request->validate([
                'warehouse_id' => 'required|uuid|exists:warehouses,id',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'reservation_type' => 'nullable|in:order,production,transfer,marketing,quality',
                'status' => 'nullable|in:reserved,shipped,cancelled,expired'
            ]);

            $report = $this->service->getReservationReport($filters);

            return $this->successResponse($report);
        });
    }

    /**
     * @response 200 {"analytics": {"document_statistics": {}, "reservation_efficiency": {}}}
     */
    public function orderSchemeAnalytics(Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $filters = $request->validate([
                'warehouse_id' => 'required|uuid|exists:warehouses,id',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from'
            ]);

            $analytics = $this->service->getOrderSchemeAnalytics($filters);

            return $this->successResponse($analytics);
        });
    }

    /**
     * @response 200 {"inventory": [], "product_summary": [], "total_summary": {}}
     */
    public function inventoryReport(Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $filters = $request->validate([
                'warehouse_id' => 'required|uuid|exists:warehouses,id',
                'product_id' => 'nullable|uuid|exists:products,id',
                'quality_status' => 'nullable|in:good,defective,quarantine,expired',
                'include_expired' => 'nullable|boolean'
            ]);

            $report = $this->service->getInventoryReport($filters);

            return $this->successResponse($report);
        });
    }
}
