<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Policies\WarehouseOrderScheme\WarehouseIssueOrderPolicyContract;
use App\Contracts\Services\Internal\WarehouseIssueOrdersServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderIndexRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderStoreRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderUpdateRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderCollection;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseIssueOrdersController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseIssueOrdersServiceContract $service,
        private readonly WarehouseIssueOrderPolicyContract $policy
    ) {
    }

    protected function getPolicy(): WarehouseIssueOrderPolicyContract
    {
        return $this->policy;
    }

    /**
     * @response 200 WarehouseIssueOrderCollection<WarehouseIssueOrderResource>
     */
    public function index(WarehouseIssueOrderIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $result = $this->service->index($request->toDTO());

            $collection = new WarehouseIssueOrderCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    /**
     * @response 200 WarehouseIssueOrderResource
     */
    public function show(string $id): WarehouseIssueOrderResource
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Расходный ордер не найден');
        }
        
        return WarehouseIssueOrderResource::make($order);
    }

    /**
     * @response 201 {"id": "uuid", "message": "Расходный ордер создан"}
     */
    public function store(WarehouseIssueOrderStoreRequest $request): JsonResponse
    {
        // Проверяем что склад работает в ордерной схеме
        $isOrderSchemeActive = $this->orderSchemeService
            ->isOrderSchemeActiveForShipments($request->warehouse_id, $request->date_from);
            
        if (!$isOrderSchemeActive) {
            return response()->json([
                'error' => 'Склад не работает в ордерной схеме для отгрузок'
            ], 422);
        }

        $data = $request->validated();
        $data['id'] = \Illuminate\Support\Str::uuid()->toString();
        
        $this->repository->insert($data);
        
        return response()->json([
            'id' => $data['id'],
            'message' => 'Расходный ордер создан'
        ], 201);
    }

    /**
     * @response 200 {"message": "Расходный ордер обновлен"}
     */
    public function update(string $id, WarehouseIssueOrderUpdateRequest $request): JsonResponse
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Расходный ордер не найден');
        }
        
        if ($order->held) {
            return response()->json([
                'error' => 'Нельзя изменять проведенный расходный ордер'
            ], 422);
        }
        
        $this->repository->update($id, $request->validated());
        
        return response()->json([
            'message' => 'Расходный ордер обновлен'
        ]);
    }

    /**
     * @response 200 {"message": "Расходный ордер удален"}
     */
    public function destroy(string $id): JsonResponse
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Расходный ордер не найден');
        }
        
        if ($order->held) {
            return response()->json([
                'error' => 'Нельзя удалять проведенный расходный ордер'
            ], 422);
        }
        
        $this->repository->delete($id);
        
        return response()->json([
            'message' => 'Расходный ордер удален'
        ]);
    }

    /**
     * @response 200 {"message": "Расходный ордер проведен"}
     */
    public function hold(string $id): JsonResponse
    {
        $order = $this->repository->show($id);
        
        if (!$order) {
            abort(404, 'Расходный ордер не найден');
        }
        
        if ($order->held) {
            return response()->json([
                'error' => 'Расходный ордер уже проведен'
            ], 422);
        }
        
        $this->repository->markAsHeld($id);
        
        return response()->json([
            'message' => 'Расходный ордер проведен'
        ]);
    }

    /**
     * @response 200 {"reasons": ["defective", "expired", "shortage", "internal_use", "return_to_supplier", "damage", "other"]}
     */
    public function getWriteOffReasons(): JsonResponse
    {
        return response()->json([
            'reasons' => [
                'defective' => 'Брак',
                'expired' => 'Истек срок годности',
                'shortage' => 'Недостача',
                'internal_use' => 'Внутренние нужды',
                'return_to_supplier' => 'Возврат поставщику',
                'damage' => 'Повреждение',
                'other' => 'Прочее'
            ]
        ]);
    }
}
