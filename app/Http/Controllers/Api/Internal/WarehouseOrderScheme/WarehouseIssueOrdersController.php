<?php

namespace App\Http\Controllers\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Policies\WarehouseOrderScheme\WarehouseIssueOrderPolicyContract;
use App\Contracts\Services\Internal\WarehouseIssueOrdersServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderIndexRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderStoreRequest;
use App\Http\Requests\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderUpdateRequest;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderCollection;
use App\Http\Resources\Api\Internal\WarehouseOrderScheme\WarehouseIssueOrderResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseIssueOrdersController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseIssueOrdersServiceContract $service,
        private readonly WarehouseIssueOrderPolicyContract $policy
    ) {
    }

    protected function getPolicy(): WarehouseIssueOrderPolicyContract
    {
        return $this->policy;
    }

    /**
     * @response 200 WarehouseIssueOrderCollection<WarehouseIssueOrderResource>
     */
    public function index(WarehouseIssueOrderIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $result = $this->service->index($request->toDTO());

            $collection = new WarehouseIssueOrderCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    /**
     * @response 200 WarehouseIssueOrderResource
     */
    public function show(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeView($request, $id);

            $order = $this->service->show($id);

            if (!$order) {
                abort(404, 'Расходный ордер не найден');
            }

            return $this->successResponse(new WarehouseIssueOrderResource($order));
        });
    }

    /**
     * @response 201 {"id": "uuid", "message": "Расходный ордер создан"}
     */
    public function store(WarehouseIssueOrderStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->authorizeCreate($request, $dto);

            $id = $this->service->create($dto);

            return $this->successResponse([
                'id' => $id,
                'message' => 'Расходный ордер создан'
            ], 201);
        });
    }

    /**
     * @response 200 {"message": "Расходный ордер обновлен"}
     */
    public function update(string $id, WarehouseIssueOrderUpdateRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $dto = $request->toDTO();

            $this->authorizeUpdate($request, $dto);

            $this->service->update($id, $dto);

            return $this->successResponse([
                'message' => 'Расходный ордер обновлен'
            ]);
        });
    }

    /**
     * @response 200 {"message": "Расходный ордер удален"}
     */
    public function destroy(string $id, Request $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);

            return $this->successResponse([
                'message' => 'Расходный ордер удален'
            ]);
        });
    }

    /**
     * @response 200 {"message": "Расходный ордер проведен"}
     */
    public function hold(string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($id) {
            $this->policy->hold($id);

            $this->service->hold($id);

            return $this->successResponse([
                'message' => 'Расходный ордер проведен'
            ]);
        });
    }

    /**
     * @response 200 {"reasons": ["defective", "expired", "shortage", "internal_use", "return_to_supplier", "damage", "other"]}
     */
    public function getWriteOffReasons(): ?JsonResponse
    {
        return $this->executeAction(function () {
            return $this->successResponse([
                'reasons' => [
                    'defective' => 'Брак',
                    'expired' => 'Истек срок годности',
                    'shortage' => 'Недостача',
                    'internal_use' => 'Внутренние нужды',
                    'return_to_supplier' => 'Возврат поставщику',
                    'damage' => 'Повреждение',
                    'other' => 'Прочее'
                ]
            ]);
        });
    }
}
