<?php

namespace App\Http\Controllers\Api\Internal\References\MeasurementUnits;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\MeasurementUnitPolicyContract;
use App\Contracts\Services\Internal\Directories\MeasurementUnitServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\MeasurementUnits\MeasurementUnitBulkRequest;
use App\Http\Requests\Api\Internal\MeasurementUnits\MeasurementUnitIndexRequest;
use App\Http\Requests\Api\Internal\MeasurementUnits\MeasurementUnitStoreRequest;
use App\Http\Requests\Api\Internal\MeasurementUnits\MeasurementUnitUpdateRequest;
use App\Http\Resources\References\MeasurementUnits\MeasurementUnitIndexCollection;
use App\Http\Resources\References\MeasurementUnits\MeasurementUnitShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MeasurementUnitController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly MeasurementUnitServiceContract $service,
        private readonly MeasurementUnitPolicyContract $policy
    ) {
    }
    /**
     * @response MeasurementUnitIndexCollection<MeasurementUnitIndexResource>
     */
    public function index(MeasurementUnitIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new MeasurementUnitIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }
    public function store(MeasurementUnitStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response MeasurementUnitShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(MeasurementUnitShowResource::make($data));
        });
    }

    public function update(MeasurementUnitUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }
    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(MeasurementUnitBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
