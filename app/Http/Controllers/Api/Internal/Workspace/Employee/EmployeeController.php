<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Employee;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Cabinet\EmployeePolicyContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\Cabinet\EmployeeServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Employees\EmployeeBulkRequest;
use App\Http\Requests\Api\Internal\Employees\EmployeeIndexRequest;
use App\Http\Requests\Api\Internal\Employees\EmployeeUpdateRequest;
use App\Http\Resources\Workspace\Employees\EmployeeIndexCollection;
use App\Http\Resources\Workspace\Employees\EmployeeShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EmployeeController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly EmployeeServiceContract $service,
        private readonly EmployeePolicyContract $policy
    ) {
    }

    /**
     * @response EmployeeIndexCollection<EmployeeIndexResource>
     */
    public function index(EmployeeIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $cabinetId = $request->validated('cabinet_id');
            $this->policy->index($cabinetId);

            $data = $this->service->index($request->toDTO());
            $collection = new EmployeeIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    /**
     * @response EmployeeShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(EmployeeShowResource::make($data));
        });
    }

    public function update(EmployeeUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function profile(Request $request, string $cabinet_id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $cabinet_id) {
            $this->policy->profile($cabinet_id);
            $data = $this->service->getProfile($request->user(), $cabinet_id);
            return $this->successResponse($data);
        });
    }

    public function bulkDelete(EmployeeBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function archive(
        EmployeeBulkRequest $request,
        ArchiveServiceContract $archiveService,
        EmployeeRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $repository, $archiveService) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->archive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }
    public function unarchive(
        EmployeeBulkRequest $request,
        ArchiveServiceContract $archiveService,
        EmployeeRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $archiveService, $repository) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->unarchive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
