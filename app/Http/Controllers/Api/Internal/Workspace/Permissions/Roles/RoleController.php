<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Permissions\Roles;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\Permissions\RolePolicyContract;
use App\Contracts\Services\Internal\RolesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Roles\RoleIndexRequest;
use App\Http\Requests\Api\Internal\Roles\RoleStoreRequest;
use App\Http\Requests\Api\Internal\Roles\RoleUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly RolesServiceContract $service,
        private readonly RolePolicyContract $policy
    ) {
    }

    public function index(RoleIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $this->policy->index($data->id);
            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
    }

    public function store(RoleStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(RoleUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
