<?php

namespace App\Http\Controllers\Api\Internal\Other;

use App\Contracts\Policies\BinPolicyContract;
use App\Contracts\Services\Internal\BinServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Bin\BinBulkRequest;
use App\Http\Requests\Api\Internal\Bin\BinIndexRequest;
use App\Http\Resources\Other\Bin\BinIndexCollection;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class BinController extends Controller
{
    use ApiResponse;
    public function __construct(
        private readonly BinServiceContract $service,
        private readonly BinPolicyContract $policy
    ) {
    }

    /**
     * @response BinIndexCollection<BinIndexResource>
     */
    public function index(BinIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $result = $this->service->index($data);
            $collection = new BinIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    public function delete(BinBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->delete($data['cabinet_id'], $data['ids']);
            $this->service->bulkForceDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function recover(BinBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->recover($data['cabinet_id'], $data['ids']);
            $this->service->bulkRecover($data['ids']);
            return $this->noContentResponse();
        });
    }
}
