<?php

namespace App\Http\Controllers\Api\Internal\Procurement\Acceptances;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Purchases\AcceptanceItemPolicyContract;
use App\Contracts\Services\Internal\Purchases\AcceptanceItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\AcceptanceItems\AcceptanceItemCalculateRequest;
use App\Http\Requests\Api\Internal\AcceptanceItems\AcceptanceItemIndexRequest;
use App\Http\Requests\Api\Internal\AcceptanceItems\AcceptanceItemStoreRequest;
use App\Http\Requests\Api\Internal\AcceptanceItems\AcceptanceItemUpdateRequest;
use App\Http\Resources\Procurement\Acceptances\AcceptanceItemIndexCollection;
use App\Http\Resources\Procurement\Acceptances\AcceptanceItemShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AcceptanceItemController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly AcceptanceItemsServiceContract $service,
        private readonly AcceptanceItemPolicyContract $policy
    ) {
    }

    /**
     * @response AcceptanceItemIndexCollection<AcceptanceItemIndexResource>
     */
    public function index(AcceptanceItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            
            $result = $this->service->index($data);
            
            $collection = new AcceptanceItemIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    public function store(AcceptanceItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response AcceptanceItemShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(AcceptanceItemShowResource::make($data));
        });
    }

    public function update(AcceptanceItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции приемки
     */
    public function calculateMetrics(AcceptanceItemCalculateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->policy->calculateItemMetrics($dto);

            $data = $this->service->calculateMetrics($dto);
            return $this->successResponse($data);
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
