<?php

namespace App\Http\Controllers\Api\Internal\Procurement\Acceptances;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Purchases\AcceptancePolicyContract;
use App\Contracts\Services\Internal\Purchases\AcceptancesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Acceptances\AcceptanceBulkRequest;
use App\Http\Requests\Api\Internal\Acceptances\AcceptanceIndexRequest;
use App\Http\Requests\Api\Internal\Acceptances\AcceptanceStoreRequest;
use App\Http\Requests\Api\Internal\Acceptances\AcceptanceUpdateRequest;
use App\Http\Resources\Procurement\Acceptances\AcceptanceIndexCollection;
use App\Http\Resources\Procurement\Acceptances\AcceptanceShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AcceptanceController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly AcceptancesServiceContract $service,
        private readonly AcceptancePolicyContract $policy
    ) {
    }


    /**
     * @response AcceptanceIndexCollection<AcceptanceIndexResource>
     */
    public function index(AcceptanceIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $result = $this->service->index($request->toDTO());
            
            $collection = new AcceptanceIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    public function store(AcceptanceStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response AcceptanceShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(AcceptanceShowResource::make($data));
        });
    }

    public function update(AcceptanceUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(AcceptanceBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkHeld(AcceptanceBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkHeld($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkUnheld(AcceptanceBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkUnheld($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkCopy(AcceptanceBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            $this->service->bulkCopy($data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
