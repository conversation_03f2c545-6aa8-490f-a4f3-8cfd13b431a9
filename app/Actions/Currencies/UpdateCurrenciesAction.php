<?php

namespace App\Actions\Currencies;

use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UpdateCurrenciesAction
{
    use HasOrderedUuid;

    public function handle(): void
    {
        $action = new FetchCurrenciesAction();
        $currencies = $action->handle();

        $currenciesDate = Carbon::createFromFormat('d.m.Y', $currencies['@attributes']['Date'])->format('Y-m-d');

        $flag = DB::table('global_currencies')
            ->where('currency_date', $currenciesDate)
            ->exists();

        if($flag) {
            return;
        }

        $now = Carbon::now();

        if($now->lessThan($currenciesDate)) {
            return;
        }

        foreach ($currencies['Valute'] as $value) {
            $externalId = $value['@attributes']['ID'];
            $data = [
                'id' => $this->generateUuid(),
                'num_code' => $value['NumCode'],
                'char_code' => $value['CharCode'],
                'value' => $action->convertCommaToDot($value['Value']),
                'external_id' => $externalId,
                'updated_at' => $now,
                'currency_date' => $currenciesDate,
            ];

            DB::table('global_currencies')
                ->where('external_id', $data['external_id'])
                ->update($data);

            DB::table('global_currencies_history')
                ->insert(
                    array_merge(
                        $data,
                        [
                            'created_at' => $now
                        ]
                    )
                );

        }
    }
}
