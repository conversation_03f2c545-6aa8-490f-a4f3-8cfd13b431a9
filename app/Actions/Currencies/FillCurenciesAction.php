<?php

namespace App\Actions\Currencies;

use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class FillCurenciesAction
{
    use HasOrderedUuid;
    public function handle(): bool
    {
        $action = new FetchCurrenciesAction();
        $currencies = $action->handle();

        $now = Carbon::now();
        $insertData = [];
        $currenciesData = Carbon::createFromFormat('d.m.Y', $currencies['@attributes']['Date'])->format('Y-m-d');
        $defaultData = require base_path('app/Data/Currencies.php');

        foreach ($currencies['Valute'] as $value) {
            $insertData[] = [
                'id' => $this->generateUuid(),
                'num_code' => $value['NumCode'],
                'char_code' => $value['CharCode'],
                'name' => $defaultData[$value['NumCode']]['name'],
                'short_name' => $defaultData[$value['NumCode']]['short_name'],
                'value' => (string) str_replace(',', '.', trim($value['VunitRate'])),
                'external_id' => $value['@attributes']['ID'],
                'updated_at' => $now,
                'created_at' => $now,
                'currency_date' => $currenciesData,
            ];
        }
        DB::table('global_currencies_history')->insert($insertData);
        $insertData[] = [
            'id' => $this->generateUuid(),
            'num_code' => 643,
            'char_code' => 'RUB',
            'name' => 'Российский рубль',
            'external_id' => 'R00000',
            'short_name' => 'руб.',
            'value' => 1,
            'updated_at' => $now,
            'created_at' => $now,
            'currency_date' => $currenciesData,
        ];

        $insertData = array_map(function ($item) use ($defaultData) {
            $item['pluralization'] = json_encode(
                $defaultData[$item['num_code']]['pluralization'],
                JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE
            );
            return $item;
        }, $insertData);


        return DB::table('global_currencies')->insert($insertData);
    }

}
