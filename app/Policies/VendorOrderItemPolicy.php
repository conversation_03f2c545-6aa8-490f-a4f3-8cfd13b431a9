<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Purchases\VendorOrderItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemCalculateDTO;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemDTO;

readonly class VendorOrderItemPolicy implements VendorOrderItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof VendorOrderItemDTO) {
            return;
        }

        $order = $this->authService->validateRelationAccess(
            'vendor_orders',
            $dto->orderId,
            null,
            PermissionNameEnum::VENDOR_ORDERS->value,
            'update',
        );

        $product = $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $order->cabinet_id,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );
        if ($product->archived_at) {
            throw new AccessDeniedException('Product is archived');
        }

        if ($dto->vatRateId) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vatRateId,
                $order->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }

        $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $dto->currencyId,
            $order->cabinet_id,
        );
    }

    /**
     * @throws AccessDeniedException
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof VendorOrderItemDTO) {
            return;
        }

        $item = $this->authService->validateRelationAccess(
            'vendor_order_items',
            $dto->resourceId,
            null,
            PermissionNameEnum::VENDOR_ORDERS->value,
            'update',
            null,
            [
                'table' => 'vendor_orders',
                'field' => 'order_id',
                'value' => $dto->resourceId
            ]
        );

        if ($dto->vatRateId && $item->vat_rate_id !== $dto->vatRateId) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vatRateId,
                $item->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }

        if ($dto->currencyId && $item->currency_id !== $dto->currencyId) {
            $this->authService->validateRelationAccess(
                'cabinet_currencies',
                $dto->currencyId,
                $item->cabinet_id,
            );
        }
    }

    public function index(string $vendorOrderId): void
    {
        $this->authService->validateRelationAccess(
            'vendor_orders',
            $vendorOrderId,
            null,
            PermissionNameEnum::VENDOR_ORDERS->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'vendor_order_items',
            $resourceId,
            null,
            PermissionNameEnum::VENDOR_ORDERS->value,
            'view',
            null,
            [
                'table' => 'vendor_orders',
                'field' => 'order_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'vendor_order_items',
            $resourceId,
            null,
            PermissionNameEnum::VENDOR_ORDERS->value,
            'update',
            null,
            [
                'table' => 'vendor_orders',
                'field' => 'order_id',
                'value' => $resourceId
            ]
        );
    }

    public function calculateMetrics(VendorOrderItemCalculateDTO $dto): void
    {
        $this->authService->hasAccessToCabinet(
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $dto->cabinetId,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );

        $this->authService->validateRelationAccess(
            'warehouses',
            $dto->warehouseId,
            $dto->cabinetId,
            PermissionNameEnum::WAREHOUSES->value,
            'view'
        );
    }
}
