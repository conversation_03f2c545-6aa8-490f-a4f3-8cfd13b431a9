<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Products\ProductPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Exceptions\NotFoundException;
use App\Models\User;
use App\Services\Api\Internal\Goods\Products\ProductsService\DTO\ProductDto;
use App\Traits\HasBarcodes;
use App\Traits\HasEmployeeAndDepartment;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class ProductPolicy implements ProductPolicyContract
{
    use HasEmployeeAndDepartment;
    use HasBarcodes;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductDto) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId, [PermissionNameEnum::GOODS_AND_SERVICES->value => 'create']);

        if ($dto->contractorId) {
            $contractor = $this->authService->validateRelationAccess(
                'contractors',
                $dto->contractorId,
                $dto->cabinetId,
                PermissionNameEnum::CONTRACTORS->value,
                'view'
            );
            if ($contractor->archived_at) {
                throw new AccessDeniedException('ContractorEntity is archived');
            }
        }

        if ($dto->category_id) {
            $this->authService->validateRelationAccess(
                'product_categories',
                $dto->category_id,
                $dto->cabinetId
            );
        }

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        if ($dto->country_id) {
            $this->authService->validateRelationAccess(
                'countries',
                $dto->country_id,
                $dto->cabinetId
            );
        }

        if ($dto->measurement_unit_id) {
            $this->authService->validateRelationAccess(
                'measurement_units',
                $dto->measurement_unit_id,
                $dto->cabinetId,
            );
        }

        if ($dto->product_siz_name_id) {
            $sizeName = DB::table('product_siz_names')
                ->where('id', $dto->product_siz_name_id)
                ->exists();

            if (!$sizeName) {
                throw new NotFoundException('Product siz name not found');
            }
        }

        if ($dto->product_siz_type_id) {
            $sizeType = DB::table('product_siz_types')
                ->where('id', $dto->product_siz_type_id)
                ->exists();

            if (!$sizeType) {
                throw new NotFoundException('Product siz type not found');
            }
        }

        if ($dto->brand_id) {
            $this->authService->validateRelationAccess(
                'brands',
                $dto->brand_id,
                $dto->cabinetId
            );
        }

        if ($dto->tnwed_id) {
            $tnwed = DB::table('tnwed_full')
                ->where('id', $dto->tnwed_id)
                ->exists();

            if (!$tnwed) {
                throw new NotFoundException('Tnwed not found');
            }
        }

        if ($dto->tax_id) {
            $this->authService->validateRelationAccess(
                'profit_tax_rates',
                $dto->tax_id,
                $dto->cabinetId
            );
        }

        if ($dto->product_type_id) {
            $productType = DB::table('product_types')
                ->where('id', $dto->product_type_id)
                ->exists();

            if (!$productType) {
                throw new NotFoundException('Product type not found');
            }
        }

        if ($dto->product_group_id) {
            $this->authService->validateRelationAccess(
                'product_groups',
                $dto->product_group_id,
                $dto->cabinetId
            );
        }

        if ($dto->thresholds && isset($dto->thresholds['warehouses'])) {
            $thresholds = collect($dto->thresholds['warehouses']);

            $this->authService->validateResourcesAccess(
                'warehouses',
                $dto->cabinetId,
                $thresholds->pluck('warehouse_id')->toArray(),
                PermissionNameEnum::WAREHOUSES->value,
                'view'
            );
        }


        $flag = DB::table('products')
            ->when($dto->article, fn ($query) => $query->where('article', $dto->article))
            ->orWhere('code', $dto->code)
            ->where('cabinet_id', $dto->cabinetId)
            ->exists();

        if ($flag) {
            throw new RuntimeException('Code or article not unique.');
        }


        if ($dto->attributes) {

            $attributes = collect($dto->attributes);

            $attribute_id = $attributes->pluck('attribute_id')
                        ->toArray();

            $this->authService->validateResourcesAccess(
                'attributes',
                $dto->cabinetId,
                $attribute_id,
            );


            $this->authService->validateResourcesAccess(
                'attribute_values',
                $dto->cabinetId,
                $attributes->pluck('attribute_values_id')->toArray(),
                null,
                null,
                [
                    'table' => 'attributes',
                    'children_field' => 'attribute_id',
                    'field' => 'id',
                ]
            );

        }

        if ($dto->sale_price) {

            $sale_price = collect($dto->sale_price);

            $this->authService->validateResourcesAccess(
                'cabinet_prices',
                $dto->cabinetId,
                $sale_price->pluck('cabinet_price_id')->toArray(),
            );

        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductDto) {
            return;
        }

        $product = $this->authService->validateRelationAccess(
            'products',
            $dto->resourceId,
            null,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'update',
        );

        if ($product->employee_id != $dto->employeeId || $product->department_id != $dto->departmentId) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $product->cabinet_id
            );
        }

        if ($dto->category_id && $dto->category_id != $product->category_id) {
            $this->authService->validateRelationAccess(
                'product_categories',
                $dto->category_id,
                $product->cabinet_id
            );
        }

        if ($dto->country_id && $dto->country_id != $product->country_id) {
            $this->authService->validateRelationAccess(
                'countries',
                $dto->country_id,
                $product->cabinet_id
            );
        }

        if ($dto->measurement_unit_id && $dto->measurement_unit_id != $product->measurement_unit_id) {
            $this->authService->validateRelationAccess(
                'measurement_units',
                $dto->measurement_unit_id,
                $product->cabinet_id
            );
        }

        if ($dto->product_siz_name_id) {
            $this->authService->validateRelationAccess(
                'product_siz_names',
                $dto->product_siz_name_id,
            );
        }

        if ($dto->product_siz_type_id) {
            $this->authService->validateRelationAccess(
                'product_siz_types',
                $dto->product_siz_type_id,
            );
        }

        if ($dto->brand_id && $dto->brand_id != $product->brand_id) {
            $this->authService->validateRelationAccess(
                'brands',
                $dto->brand_id,
                $product->cabinet_id
            );
        }

        if ($dto->tnwed_id && $dto->tnwed_id != $product->tnwed_id) {
            $this->authService->validateRelationAccess(
                'tnwed_full',
                $dto->tnwed_id
            );
        }

        if ($dto->tax_id && $dto->tax_id != $product->tax_id) {
            $this->authService->validateRelationAccess(
                'profit_tax_rates',
                $dto->tax_id
            );
        }

        if ($dto->product_type_id && $dto->product_type_id != $product->product_type_id) {
            $this->authService->validateRelationAccess(
                'product_types',
                $dto->product_type_id
            );
        }

        if ($dto->product_group_id && $dto->product_group_id != $product->product_group_id) {
            $this->authService->validateRelationAccess(
                'product_groups',
                $dto->product_group_id
            );
        }

        if ($dto->sale_price) {

            $sale_price = collect($dto->sale_price);

            $this->authService->validateResourcesAccess(
                'cabinet_prices',
                $product->cabinet_id,
                $sale_price->pluck('cabinet_price_id')->filter(function ($value) {
                    return $value !== null;
                })->values()->toArray(),
            );

        }

        if ($dto->attributes) {

            $attributes = collect($dto->attributes);

            $attribute_id = $attributes->pluck('attribute_id')->filter(function ($value) {
                return $value !== null;
            })->values()->toArray();

            $this->authService->validateResourcesAccess(
                'attributes',
                $product->cabinet_id,
                $attribute_id,
            );

            $this->authService->validateResourcesAccess(
                'attribute_values',
                $product->cabinet_id,
                $attributes->pluck('attribute_values_id')->filter(function ($value) {
                    return $value !== null;
                })->values()->toArray(),
                null,
                null,
                [
                    'table' => 'attributes',
                    'children_field' => 'attribute_id',
                    'field' => 'id',
                ]
            );

            $this->authService->validateResourcesAccess(
                'product_attributes',
                $product->cabinet_id,
                $attributes->pluck('id')->filter(function ($value) {
                    return $value !== null;
                })->values()->toArray(),
                null,
                null,
                [
                    'table' => 'attributes',
                    'children_field' => 'attribute_id',
                    'field' => 'id',
                ]
            );

        }


    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'products',
            $resourceId,
            null,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'delete'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('products', $resourceId, null, PermissionNameEnum::GOODS_AND_SERVICES->value, 'view');
    }

}
