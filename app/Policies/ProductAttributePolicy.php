<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Products\ProductAttributePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Goods\Products\ProductAttributesService\DTO\ProductAttributeDto;

readonly class ProductAttributePolicy implements ProductAttributePolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductAttributeDto) {
            return;
        }

        $product = $this->authService->validateRelationAccess('products', $dto->productId);

        $this->authService->validateRelationAccess('attributes', $dto->attributeId);

        $this->authService->validateRelationAccess(
            'attribute_values',
            $dto->attributeValuesId,
            $product->cabinet_id,
            null,
            null,
            $product->cabinet_id,
            [
                'table' => 'attributes',
                'field' => 'attribute_id',
                'value' => $dto->attributeValuesId
            ]
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductAttributeDto) {
            return;
        }

        $productAttribute = $this->authService->validateRelationAccess(
            'product_attributes',
            $dto->resourceId,
            null,
            null,
            null,
            null,
            [
                'table' => 'products',
                'field' => 'product_id',
                'value' => $dto->resourceId
            ]
        );

        $this->authService->validateRelationAccess('attributes', $dto->attributeId);

        $this->authService->validateRelationAccess(
            'attribute_values',
            $dto->attributeValuesId,
            $productAttribute->cabinet_id,
            null,
            null,
            $productAttribute->cabinet_id,
            [
                'table' => 'attributes',
                'field' => 'attribute_id',
                'value' => $dto->attributeValuesId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'product_attributes',
            $resourceId,
            null,
            null,
            null,
            null,
            [
                'table' => 'products',
                'field' => 'product_id',
                'value' => $resourceId
            ]
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'product_attributes',
            $resourceId,
            null,
            null,
            null,
            null,
            [
                'table' => 'products',
                'field' => 'product_id',
                'value' => $resourceId
            ]
        );
    }

    public function index(string $resourceId): void
    {
        $this->authService->validateRelationAccess('products', $resourceId);
    }

    public function getByProductId(string $resourceId): void
    {
        $this->authService->validateRelationAccess('products', $resourceId);
    }

}
