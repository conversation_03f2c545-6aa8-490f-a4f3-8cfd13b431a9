<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Finances\OutgoingPaymentPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\DTO\OutgoingPaymentDTO;
use App\Traits\HasEmployeeAndDepartment;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class OutgoingPaymentPolicy implements OutgoingPaymentPolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof OutgoingPaymentDTO) {
            return;
        }

        $requiredPermissions = [
            PermissionNameEnum::OUTGOING_PAYMENTS->value => 'create'
        ];

        if ($dto->held) {
            $requiredPermissions[PermissionNameEnum::OUTGOING_PAYMENTS->value] = ['create', 'held'];
        }

        $this->authService->hasAccessToCabinet($dto->cabinet_id, $requiredPermissions);

        if ($dto->status_id) {
            $this->authService->validateRelationAccess('statuses', $dto->status_id, $dto->cabinet_id);
        }

        if ($dto->sales_channel_id) {
            $salesChannel = $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->sales_channel_id,
                $dto->cabinet_id
            );
            if ($salesChannel->archived_at) {
                throw new AccessDeniedException('Sales channel is archived');
            }
        }

        $currency = $this->authService->validateRelationAccess(
            'cabinet_currencies',
            $dto->currency_id,
            $dto->cabinet_id,
            PermissionNameEnum::CURRENCIES->value,
            'view'
        );
        if ($currency->archived_at) {
            throw new AccessDeniedException('Currency is archived');
        }

        $legal = $this->authService->validateRelationAccess(
            'legal_entities',
            $dto->legal_entity_id,
            $dto->cabinet_id
        );
        if ($legal->archived_at) {
            throw new AccessDeniedException('Legal is archived');
        }
        $contractor = $this->authService->validateRelationAccess(
            'contractors',
            $dto->contractor_id,
            $dto->cabinet_id
        );
        if ($contractor->archived_at) {
            throw new AccessDeniedException('ContractorEntity is archived');
        }

        if ($dto->files) {
            $this->authService->validateResourcesAccess('files', $dto->cabinet_id, $dto->files);
        }
        $this->checkEmployeeAndDepartmentIds(
            $dto->employee_id,
            $dto->department_id,
            $dto->cabinet_id
        );
    }

    /**
     * @throws AccessDeniedException
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof OutgoingPaymentDTO) {
            return;
        }

        $requiredPermissions = [
            PermissionNameEnum::OUTGOING_PAYMENTS->value => 'update',
        ];

        $payment = $this->authService->validateRelationAccess(
            'outgoing_payments',
            $dto->id,
        );

        if ($payment->held != $dto->held) {
            $requiredPermissions[PermissionNameEnum::OUTGOING_PAYMENTS->value] .= 'held';
        }
        if ($payment->currency_id != $dto->currency_id) {
            $requiredPermissions[PermissionNameEnum::EDIT_DOCUMENT_CURRENCY->value] = 'all';
        }

        $this->authService->hasAccessToCabinet($payment->cabinet_id, $requiredPermissions);
        $this->authService->hasEntityPermission($payment, PermissionNameEnum::OUTGOING_PAYMENTS->value, 'update');

        if ($dto->status_id && $payment->status_id != $dto->status_id) {
            $this->authService->validateRelationAccess('statuses', $dto->status_id, $payment->cabinet_id);
        }

        if ($payment->legal_entity_id != $dto->legal_entity_id) {
            $legal = $this->authService->validateRelationAccess(
                'legal_entities',
                $dto->legal_entity_id,
                $payment->cabinet_id
            );
            if ($legal->archived_at) {
                throw new AccessDeniedException('Legal is archived');
            }
        }

        if ($payment->contractor_id != $dto->contractor_id) {
            $contractor = $this->authService->validateRelationAccess(
                'contractors',
                $dto->contractor_id,
                $payment->cabinet_id
            );
            if ($contractor->archived_at) {
                throw new AccessDeniedException('ContractorEntity is archived');
            }
        }

        if ($dto->sales_channel_id && $payment->sales_channel_id != $dto->sales_channel_id) {
            $salesChannel = $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->sales_channel_id,
                $dto->cabinet_id
            );
            if ($salesChannel->archived_at) {
                throw new AccessDeniedException('Sales channel is archived');
            }
        }

        if ($payment->currency_id != $dto->currency_id) {
            $currency = $this->authService->validateRelationAccess(
                'cabinet_currencies',
                $dto->currency_id,
                $dto->cabinet_id,
                PermissionNameEnum::CURRENCIES->value,
                'view'
            );
            if ($currency->archived_at) {
                throw new AccessDeniedException('Currency is archived');
            }
        }

        if ($dto->files) {
            $this->authService->validateResourcesAccess('files', $payment->cabinet_id, $dto->files);
        }

        if ($dto->employee_id != $payment->employee_id || $dto->department_id != $payment->department_id) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employee_id,
                $dto->department_id,
                $payment->cabinet_id
            );
        }
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('outgoing_payments', $resourceId, null, PermissionNameEnum::OUTGOING_PAYMENTS->value, 'delete');

        $node = DB::table('documents')
            ->select(['lft', 'rgt', 'tree_id'])
            ->where('documentable_id', $resourceId)
            ->first();

        if (!$node) {
            return;
        }

        // Проверяем, что это листовой узел (разница между rgt и lft должна быть 1)
        if ($node->rgt - $node->lft !== 1) {
            throw new RuntimeException('Cannot delete node with children');
        }
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'outgoing_payments',
            $resourceId,
            null,
            PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'view'
        );
    }
}
