<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\FilePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Files\FilesService\DTO\FileDTO;
use RuntimeException;

readonly class FilePolicy implements FilePolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof FileDTO) {
            throw new RuntimeException('Incorrect DTO');
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);
    }

    public function update(User $user, DtoContract $dto): void
    {
        //
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'files',
            $resourceId,
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'files',
            $resourceId
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
