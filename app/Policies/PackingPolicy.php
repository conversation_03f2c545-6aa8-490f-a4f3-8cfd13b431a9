<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\PackingPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\References\PackingsService\DTO\PackingDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class PackingPolicy implements PackingPolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'packings',
            $resourceId,
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'packings',
            $resourceId,
        );
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof PackingDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);

        $this->authService->validateRelationAccess(
            'measurement_units',
            $dto->measurementUnitVolumeId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'measurement_units',
            $dto->measurementUnitWeightId,
            $dto->cabinetId
        );

        $this->authService->validateRelationAccess(
            'measurement_units',
            $dto->measurementUnitSizeId,
            $dto->cabinetId
        );

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof PackingDTO) {
            return;
        }

        $packing = $this->authService->validateRelationAccess(
            'packings',
            $dto->resourceId
        );

        if ($packing->measurement_unit_volume_id != $dto->measurementUnitVolumeId) {
            $this->authService->validateRelationAccess(
                'measurement_units',
                $dto->measurementUnitVolumeId,
                $packing->cabinet_id
            );
        }

        if ($packing->measurement_unit_weight_id != $dto->measurementUnitWeightId) {
            $this->authService->validateRelationAccess(
                'measurement_units',
                $dto->measurementUnitWeightId,
                $packing->cabinet_id
            );
        }

        if ($packing->measurement_unit_size_id != $dto->measurementUnitSizeId) {
            $this->authService->validateRelationAccess(
                'measurement_units',
                $dto->measurementUnitSizeId,
                $packing->cabinet_id
            );
        }

        if ($packing->employee_id != $dto->employeeId || $packing->department_id != $dto->departmentId) {
            $this->checkEmployeeAndDepartmentIds(
                $dto->employeeId,
                $dto->departmentId,
                $packing->cabinet_id
            );
        }
    }
}
