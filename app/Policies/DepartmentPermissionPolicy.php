<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Permissions\DepartmentPermissionPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DTO\DepartmentPermissionDTO;

readonly class DepartmentPermissionPolicy implements DepartmentPermissionPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DepartmentPermissionDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            entity: 'departments',
            entityId: $dto->departmentId,
        );

    }
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DepartmentPermissionDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            entity: 'departments',
            entityId: $dto->departmentId,
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        //
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'departments',
            entityId: $resourceId
        );

    }
}
