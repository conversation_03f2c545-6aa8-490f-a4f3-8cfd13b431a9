<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Cabinet\DepartmentPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Workspace\DepartmentsService\DTO\DepartmentDTO;

readonly class DepartmentPolicy implements DepartmentPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DepartmentDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);

        if ($dto->salesChannelId) {
            $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->salesChannelId,
                $dto->cabinetId,
                PermissionNameEnum::SALE_CHANNELS->value,
                'view'
            );
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof DepartmentDTO) {
            return;
        }

        $department = $this->authService->validateRelationAccess(
            'departments',
            $dto->resourceId
        );

        if ($dto->salesChannelId && $department->sales_channel_id !== $dto->salesChannelId) {
            $this->authService->validateRelationAccess(
                'sales_channels',
                $dto->salesChannelId,
                $department->cabinet_id,
                PermissionNameEnum::SALE_CHANNELS->value,
                'view'
            );
        }
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('departments', $resourceId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('departments', $resourceId);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
