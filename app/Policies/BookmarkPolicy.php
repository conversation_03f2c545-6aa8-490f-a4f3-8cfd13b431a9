<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\BookmarksPolicyContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Exceptions\NotFoundException;
use App\Models\User;
use App\Services\Api\Internal\Workspace\BookmarksService\DTO\BookmarkDto;

readonly class BookmarkPolicy implements BookmarksPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService,
        private EmployeeRepositoryContract $employeeRepository
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof BookmarkDto) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);
    }

    /**
     * @throws NotFoundException
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof BookmarkDto) {
            return;
        }

        $bookmark = $this->authService->validateRelationAccess(
            'bookmarks',
            $dto->resourceId,
        );

        $this->checkEmployeeAccess($user, $bookmark);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    /**
     * @throws NotFoundException
     */
    public function view(User $user, string $resourceId): void
    {
        $bookmark = $this->authService->validateRelationAccess(
            'bookmarks',
            $resourceId
        );
        $this->checkEmployeeAccess($user, $bookmark);
    }

    /**
     * @throws NotFoundException
     */
    public function delete(User $user, string $resourceId): void
    {
        $bookmark = $this->authService->validateRelationAccess(
            'bookmarks',
            $resourceId
        );
        $this->checkEmployeeAccess($user, $bookmark);
    }

    /**
     * @param User $user
     * @param object $bookmark
     * @return void
     * @throws NotFoundException
     */
    public function checkEmployeeAccess(User $user, object $bookmark): void
    {
        $employee = $this->employeeRepository->getByUserIdAndCabinet($user->id, $bookmark->cabinet_id);
        if (!$employee || $employee->id !== $bookmark->employee_id) {
            throw new NotFoundException();
        }
    }
}
