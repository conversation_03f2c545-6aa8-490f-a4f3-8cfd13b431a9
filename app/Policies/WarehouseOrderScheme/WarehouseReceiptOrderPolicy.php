<?php

namespace App\Policies\WarehouseOrderScheme;

use App\Contracts\DtoContract;
use App\Contracts\Policies\WarehouseOrderScheme\WarehouseReceiptOrderPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderStoreDTO;
use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderUpdateDTO;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Traits\HasEmployeeAndDepartment;

readonly class WarehouseReceiptOrderPolicy implements WarehouseReceiptOrderPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseReceiptOrderStoreDTO) {
            return;
        }

        // Проверяем доступ к кабинету с правами на создание складских документов
        $this->authService->hasAccessToCabinet($dto->cabinetId, [
            PermissionNameEnum::WAREHOUSES->value => 'create'
        ]);

        // Проверяем доступ к складу
        $this->authService->validateRelationAccess(
            'warehouses',
            $dto->warehouseId,
            $dto->cabinetId
        );

        // Проверяем сотрудника и подразделение
        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        // Проверяем статус если указан
        if ($dto->statusId) {
            $this->authService->validateRelationAccess(
                'statuses',
                $dto->statusId,
                $dto->cabinetId
            );
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseReceiptOrderUpdateDTO) {
            return;
        }

        // Получаем приходный ордер и проверяем доступ
        $order = $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $dto->resourceId ?? '',
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'update'
        );

        // Проверяем статус если изменяется
        if ($dto->statusId && $dto->statusId !== $order->status_id) {
            $this->authService->validateRelationAccess(
                'statuses',
                $dto->statusId,
                $order->cabinet_id
            );
        }
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $resourceId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'view'
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $resourceId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'delete'
        );
    }

    public function hold(string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'warehouse_receipt_orders',
            $resourceId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'update'
        );
    }
}
