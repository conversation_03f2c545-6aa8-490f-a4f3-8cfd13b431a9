<?php

namespace App\Enums\Api\Internal;

enum ProductThresholdsEnum: int
{
    case TOTAL_IN_ALL_WAREHOUSES   = 1; // В сумме на всех складах
    case SAME_IN_ALL_WAREHOUSES    = 2; // Одинаковый на всех складах
    case SET_FOR_EACH_WAREHOUSE    = 3; // Задать для каждого склада

    public function getValue(): array
    {
        return match($this) {
            self::TOTAL_IN_ALL_WAREHOUSES   =>  __('В сумме на всех складах'),
            self::SAME_IN_ALL_WAREHOUSES    =>  __('Одинаковый на всех складах'),
            self::SET_FOR_EACH_WAREHOUSE    =>  __('Задать для каждого склада'),
        };
    }

}
