<?php

namespace App\Enums\Api\Internal;

enum WarehouseTransactionOperationTypeEnum: string
{
    case RECEIPT = 'receipt';
    case ISSUE = 'issue';
    case TRANSFER_OUT = 'transfer_out';
    case TRANSFER_IN = 'transfer_in';
    case RESERVE = 'reserve';
    case UNRESERVE = 'unreserve';
    case ADJUSTMENT_PLUS = 'adjustment_plus';
    case ADJUSTMENT_MINUS = 'adjustment_minus';
    case QUALITY_HOLD = 'quality_hold';
    case QUALITY_RELEASE = 'quality_release';

    public function getDescription(): string
    {
        return match ($this) {
            self::RECEIPT => 'Поступление товаров',
            self::ISSUE => 'Расход товаров',
            self::TRANSFER_OUT => 'Списание при перемещении',
            self::TRANSFER_IN => 'Оприходование при перемещении',
            self::RESERVE => 'Резервирование',
            self::UNRESERVE => 'Снятие резерва',
            self::ADJUSTMENT_PLUS => 'Дооприходование при инвентаризации',
            self::ADJUSTMENT_MINUS => 'Списание при инвентаризации',
            self::QUALITY_HOLD => 'Блокировка на контроль качества',
            self::QUALITY_RELEASE => 'Освобождение после контроля качества',
        };
    }

    public function isIncoming(): bool
    {
        return in_array($this, [
            self::RECEIPT,
            self::TRANSFER_IN,
            self::ADJUSTMENT_PLUS,
            self::QUALITY_RELEASE
        ]);
    }

    public function isOutgoing(): bool
    {
        return in_array($this, [
            self::ISSUE,
            self::TRANSFER_OUT,
            self::ADJUSTMENT_MINUS,
            self::QUALITY_HOLD
        ]);
    }

    public function isReservationRelated(): bool
    {
        return in_array($this, [
            self::RESERVE,
            self::UNRESERVE
        ]);
    }
}
