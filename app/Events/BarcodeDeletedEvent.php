<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BarcodeDeletedEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public string $barcodable_id;

    /**
    * @param string $barcodable_id
    */
    public function __construct(string $barcodable_id)
    {
        $this->barcodable_id = $barcodable_id;
    }
}
