<?php

namespace App\Notifications;

use Random\RandomException;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class VerifyEmail extends Notification implements ShouldQueue
{
    use Queueable;

    protected string $code;

    /**
     * @throws RandomException
     */
    protected function generateVerificationCode(mixed $notifiable): string
    {
        $code = str_pad((string)random_int(1, 9999), 4, '0', STR_PAD_LEFT);

        DB::table('email_verification_codes')->updateOrInsert(
            ['user_id' => $notifiable->id],
            [
                'code' => $code,
                'expires_at' => now()->addMinutes($this->codeExpirationMinutes()),
                'updated_at' => now(),
            ]
        );

        return $code;
    }

    public function toMail(mixed $notifiable): MailMessage
    {
        $this->code = $this->generateVerificationCode($notifiable);

        return (new MailMessage())
            ->subject('Verify Your Email Address')
            ->line('Your verification code is:')
            ->line($this->code)
            ->line('This code is valid for ' . $this->codeExpirationMinutes() . ' minutes.');
    }

    protected function codeExpirationMinutes(): int
    {
        return config('auth.verification.expire', 60);
    }

    public function via(mixed $notifiable): array
    {
        return ['mail'];
    }
}
