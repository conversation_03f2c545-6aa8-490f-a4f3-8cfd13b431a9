# Устранение неполадок: Ордерная схема складского учета

## Общие проблемы и решения

### 1. Ордерная схема не активируется

**Проблема:** API возвращает `is_active: false` для склада, который должен работать в ордерной схеме.

**Возможные причины:**
- Не включен детальный учет партий в настройках склада
- Отключен контроль сроков годности
- Не активирован контроль качества товаров

**Решение:**
1. Проверьте настройки склада в админ-панели
2. Убедитесь, что включены все необходимые опции:
   - ✅ Детальный учет партий
   - ✅ Контроль сроков годности  
   - ✅ Контроль качества товаров
3. Проверьте через API:
```http
GET /api/internal/warehouse-order-scheme/detection/is-active?warehouse_id=uuid&operation_type=shipments
```

### 2. Ошибка создания приходного ордера

**Ошибка:** `422 Склад не работает в ордерной схеме для приемок`

**Решение:**
1. Проверьте активность ордерной схемы для приемок:
```http
GET /api/internal/warehouse-order-scheme/detection/is-active?warehouse_id=uuid&operation_type=receipts
```
2. Если схема не активна, проверьте настройки склада
3. Убедитесь, что дата документа корректна

### 3. Ошибка резервирования товаров

**Ошибка:** `422 Недостаточно доступного товара для резервирования`

**Диагностика:**
1. Проверьте доступность товара:
```http
GET /api/internal/warehouse-order-scheme/warehouse-reservations/availability?product_id=uuid&warehouse_id=uuid
```
2. Проверьте существующие резервы:
```http
GET /api/internal/warehouse-order-scheme/warehouse-reservations?filters[warehouse_item_id]=uuid
```

**Решение:**
- Убедитесь, что товар есть в наличии
- Проверьте, не зарезервирован ли товар полностью
- Проверьте статус качества товара (должен быть `good`)

### 4. Ошибка валидации отгрузки

**Ошибка:** `insufficient_reservation - Недостаточно зарезервированного товара`

**Решение:**
1. Создайте резерв перед отгрузкой:
```http
POST /api/internal/warehouse-order-scheme/warehouse-reservations
```
2. Или проверьте существующие резервы и их статус
3. Убедитесь, что резервы не истекли

### 5. Проблемы с правами доступа

**Ошибка:** `403 Доступ запрещен`

**Проверьте:**
- Права пользователя на работу со складами
- Доступ к конкретному кабинету
- Права на создание/изменение складских документов
- Корректность токена авторизации

### 6. Ошибки при работе с просроченными товарами

**Проблема:** Система блокирует операции с просроченными товарами

**Решение:**
1. Для списания просроченных товаров используйте расходный ордер:
```json
{
  "write_off_reason": "expired",
  "reason_description": "Истек срок годности"
}
```
2. Проверьте отчет по срокам годности:
```http
GET /api/internal/warehouse-order-scheme/reports/inventory?warehouse_id=uuid&include_expired=true
```

## Диагностические запросы

### Проверка состояния склада

```http
GET /api/internal/warehouse-order-scheme/detection/is-active?warehouse_id=uuid&operation_type=shipments
```

### Проверка остатков товара

```http
GET /api/internal/warehouse-order-scheme/reports/inventory?warehouse_id=uuid&product_id=uuid
```

### Проверка резервов товара

```http
GET /api/internal/warehouse-order-scheme/warehouse-reservations?filters[warehouse_item_id]=uuid
```

### Проверка движений товара

```http
GET /api/internal/warehouse-order-scheme/reports/stock-movement?warehouse_id=uuid&product_id=uuid&date_from=2024-01-01
```

## Логирование и отладка

### Включение детального логирования

В файле `.env` добавьте:
```
LOG_LEVEL=debug
LOG_CHANNEL=daily
```

### Ключевые места для логирования

1. **Определение режима склада:**
   - `WarehouseOrderSchemeDetectionService`
   - Логи: активация/деактивация ордерной схемы

2. **Валидация операций:**
   - `ValidationHandler`
   - Логи: ошибки валидации, предупреждения

3. **Резервирование:**
   - `ReservationValidationHandler`
   - Логи: создание/отмена резервов

4. **Создание документов:**
   - `WarehouseReceiptOrdersCreateHandler`
   - `WarehouseIssueOrdersCreateHandler`
   - Логи: создание и проведение документов

### Проверка логов

```bash
# Логи приложения
tail -f storage/logs/laravel.log

# Логи конкретного дня
tail -f storage/logs/laravel-2024-01-15.log

# Поиск ошибок ордерной схемы
grep -i "warehouse.*order.*scheme" storage/logs/laravel.log
```

## Производительность

### Медленные запросы

**Проблема:** Отчеты работают медленно

**Решение:**
1. Проверьте индексы в базе данных:
```sql
-- Индексы для warehouse_transactions
CREATE INDEX idx_warehouse_transactions_warehouse_date ON warehouse_transactions(warehouse_id, transaction_date);
CREATE INDEX idx_warehouse_transactions_product ON warehouse_transactions(product_id);

-- Индексы для warehouse_reservations  
CREATE INDEX idx_warehouse_reservations_item_status ON warehouse_reservations(warehouse_item_id, status);
CREATE INDEX idx_warehouse_reservations_expires ON warehouse_reservations(expires_at, status);
```

2. Используйте фильтры в отчетах:
   - Ограничивайте период запроса
   - Фильтруйте по конкретным товарам
   - Используйте пагинацию

### Оптимизация запросов

1. **Для больших складов** используйте фильтры по датам
2. **Для отчетов** кешируйте результаты на уровне приложения
3. **Для аналитики** рассмотрите создание материализованных представлений

## Мониторинг

### Ключевые метрики для отслеживания

1. **Количество активных резервов**
2. **Процент истекших резервов**
3. **Время выполнения отчетов**
4. **Количество ошибок валидации**
5. **Использование ордерной схемы по складам**

### Алерты

Настройте уведомления для:
- Большого количества истекших резервов
- Ошибок при создании документов
- Медленных запросов отчетов
- Проблем с определением режима склада

## Восстановление данных

### Проверка целостности данных

```sql
-- Проверка резервов без соответствующих товаров
SELECT wr.* FROM warehouse_reservations wr
LEFT JOIN warehouse_items wi ON wr.warehouse_item_id = wi.id
WHERE wi.id IS NULL;

-- Проверка транзакций без документов
SELECT wt.* FROM warehouse_transactions wt
WHERE wt.document_id IS NOT NULL 
AND NOT EXISTS (
  SELECT 1 FROM warehouse_receipt_orders wro WHERE wro.id = wt.document_id
  UNION
  SELECT 1 FROM warehouse_issue_orders wio WHERE wio.id = wt.document_id
);
```

### Исправление данных

1. **Освобождение зависших резервов:**
```sql
UPDATE warehouse_reservations 
SET status = 'cancelled' 
WHERE status = 'reserved' 
AND expires_at < NOW() - INTERVAL 1 DAY;
```

2. **Очистка некорректных транзакций:**
```sql
-- Только после тщательной проверки!
DELETE FROM warehouse_transactions 
WHERE document_id IS NOT NULL 
AND document_type NOT IN ('warehouse_receipt_order', 'warehouse_issue_order');
```

## Контакты технической поддержки

При возникновении проблем, которые не удается решить самостоятельно:

1. **Подготовьте информацию:**
   - Описание проблемы
   - Шаги для воспроизведения
   - Логи ошибок
   - UUID склада и товаров
   - Время возникновения проблемы

2. **Приложите диагностические данные:**
   - Результат проверки активности ордерной схемы
   - Настройки склада
   - Примеры запросов и ответов API

3. **Обратитесь в техподдержку** с полным описанием проблемы и собранной информацией.
