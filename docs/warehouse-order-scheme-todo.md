# TODO: Реализация гибридной системы складского учета (обычная + ордерная схема)

## Обзор

Цель проекта - добавить поддержку ордерной схемы складского учета как дополнительного режима работы, сохранив
существующую систему. Каждый склад сможет работать в одном из двух режимов:

1. **Обычная схема** (текущая) - простой учет остатков без формальных ордеров
2. **Ордерная схема** (новая) - полный контроль движения товаров через формальные документы

### Принципы гибридной системы

**Ключевые требования:**

- Обратная совместимость - существующие склады продолжают работать без изменений
- Возможность переключения режимов на уровне склада
- Единая база данных для обеих схем с дополнительными таблицами для ордерной схемы
- Общие API с автоматическим определением режима работы склада

### Текущее состояние системы

**Что уже реализовано:**

- Базовая структура `warehouse_order_schemes` с настройками
- FIFO логика списания товаров через `warehouse_items` → `shipment_warehouse_items`
- Простое резервирование через поле `reserve` в заказах
- Основные складские документы: приемки, отгрузки, перемещения
- Система документооборота через таблицу `documents`

**Что нужно добавить для ордерной схемы:**

- Приходные и расходные ордера как отдельные документы
- Детальное резервирование конкретных партий товаров
- Инвентаризации с автоматической корректировкой остатков
- Система переключения между режимами работы
- Валидация операций в рамках ордерной схемы

### Различия между схемами

| Аспект            | Обычная схема             | Ордерная схема                    |
|-------------------|---------------------------|-----------------------------------|
| Резервирование    | Простой флаг в заказе     | Детальное резервирование партий   |
| Движение товаров  | Прямое изменение остатков | Через формальные ордера           |
| Контроль операций | Минимальный               | Строгий контроль всех операций    |
| Инвентаризация    | Ручная корректировка      | Автоматические ордера на разности |
| Отчетность        | Базовые остатки           | Детальная аналитика движений      |
| Сложность         | Простая                   | Высокая, но более точная          |

## Этап 1: Сервис определения режима работы ✅ ВЫПОЛНЕНО

### 1.1 Использование существующей таблицы warehouse_order_schemes ⏭️ ПРОПУЩЕНО

**Причина пропуска:** Текущие настройки ордерной схемы уже достаточны для реализации

**Текущие поля в warehouse_order_schemes (используем как есть):**

- `on_coming_from` (date) - дата начала ордерной схемы для приемок
- `on_shipment_from` (date) - дата начала ордерной схемы для отгрузок
- `control_operational_balances` (boolean) - контроль операционных остатков

**Логика определения режима работы:**

- Если `on_coming_from` и `on_shipment_from` заполнены - полная ордерная схема
- Если заполнено только `on_coming_from` - ордерная схема только для приемок
- Если заполнено только `on_shipment_from` - ордерная схема только для отгрузок
- Если оба поля NULL - обычная схема работы

### 1.2 Сервис определения режима работы ✅ ВЫПОЛНЕНО

**Созданные файлы:**

```
✅ app/Contracts/Services/Internal/WarehouseOrderSchemeDetectionServiceContract.php
✅ app/Services/Api/Internal/WarehouseOrderScheme/WarehouseOrderSchemeDetectionService.php
✅ app/Services/Api/Internal/WarehouseOrderScheme/Handlers/SchemeDetectionHandler.php
```

**Реализованная функциональность:**

- ✅ Определение активности ордерной схемы по датам `on_coming_from` и `on_shipment_from`
- ✅ Проверка какие операции (приемки/отгрузки) должны работать в ордерной схеме
- ✅ Получение настроек ордерной схемы для склада

**Реализованные методы сервиса:**

- ✅ `isOrderSchemeActiveForReceipts(warehouse_id, date)` - активна ли ордерная схема для приемок
- ✅ `isOrderSchemeActiveForShipments(warehouse_id, date)` - активна ли ордерная схема для отгрузок
- ✅ `getOrderSchemeMode(warehouse_id)` - получить режим работы склада
- ✅ `getOrderSchemeSettings(warehouse_id)` - получить настройки ордерной схемы
- ✅ `isOperationalBalanceControlEnabled(warehouse_id)` - проверить контроль остатков

## Этап 2: Создание документов для ордерной схемы ✅ ВЫПОЛНЕНО

### 2.1 Приходные ордера (только для ордерной схемы) ✅ ВЫПОЛНЕНО

**Цель:** Формализация оприходования товаров на складах с ордерной схемой

**Созданные файлы:**

```
✅ database/migrations/2024_12_02_000002_create_warehouse_receipt_orders_table.php
✅ database/migrations/2024_12_02_000003_create_warehouse_receipt_order_items_table.php
✅ app/Models/WarehouseReceiptOrder.php
✅ app/Models/WarehouseReceiptOrderItem.php
✅ app/Entities/WarehouseReceiptOrderEntity.php
✅ app/Entities/WarehouseReceiptOrderItemEntity.php
✅ app/Repositories/Warehouses/WarehouseReceiptOrdersRepository.php
✅ app/Repositories/Warehouses/WarehouseReceiptOrderItemsRepository.php
✅ app/Contracts/Repositories/WarehouseReceiptOrdersRepositoryContract.php
✅ app/Contracts/Repositories/WarehouseReceiptOrderItemsRepositoryContract.php
```

**Реализованная функциональность:**

- ✅ Структура таблиц для приходных ордеров и их позиций
- ✅ Поддержка партионного учета (batch_number, lot_number, expiry_date)
- ✅ Контроль качества товаров (quality_status)
- ✅ Связь с документами-основаниями
- ✅ Полный CRUD через репозитории

**Важно:** Эти документы создаются и используются ТОЛЬКО для складов с ордерной схемой

**Структура таблицы warehouse_receipt_orders:**

- `id` (UUID) - первичный ключ
- `cabinet_id` (UUID) - кабинет
- `employee_id` (UUID) - ответственный сотрудник
- `department_id` (UUID) - подразделение
- `warehouse_id` (UUID) - склад
- `number` (string) - номер документа
- `date_from` (datetime) - дата документа
- `status_id` (UUID) - статус документа
- `held` (boolean) - проведен ли документ
- `document_basis_type` (string) - тип документа-основания
- `document_basis_id` (UUID) - ID документа-основания
- `reason` (text) - основание для оприходования
- `total_quantity` (integer) - общее количество
- `comment` (text) - комментарий

**Когда создается (только для ордерной схемы):**

- При оприходовании излишков после инвентаризации
- При возврате товаров от клиентов
- При оприходовании товаров собственного производства
- При исправлении ошибок учета

### 2.2 Расходные ордера (только для ордерной схемы) ✅ ВЫПОЛНЕНО

**Цель:** Формализация списания товаров со складов с ордерной схемой

**Созданные файлы:**

```
✅ database/migrations/2024_12_02_000004_create_warehouse_issue_orders_table.php
✅ database/migrations/2024_12_02_000005_create_warehouse_issue_order_items_table.php
✅ app/Models/WarehouseIssueOrder.php
✅ app/Models/WarehouseIssueOrderItem.php
✅ app/Entities/WarehouseIssueOrderEntity.php
✅ app/Entities/WarehouseIssueOrderItemEntity.php
✅ app/Repositories/Warehouses/WarehouseIssueOrdersRepository.php
✅ app/Repositories/Warehouses/WarehouseIssueOrderItemsRepository.php
✅ app/Contracts/Repositories/WarehouseIssueOrdersRepositoryContract.php
✅ app/Contracts/Repositories/WarehouseIssueOrderItemsRepositoryContract.php
```

**Реализованная функциональность:**

- ✅ Структура таблиц для расходных ордеров и их позиций
- ✅ Связь с конкретными партиями товаров (warehouse_item_id)
- ✅ Классификация причин списания (defective, expired, shortage, etc.)
- ✅ Связь с документами-основаниями
- ✅ Полный CRUD через репозитории

**Когда создается (только для ордерной схемы):**

- При списании брака и порчи
- При списании недостач после инвентаризации
- При списании на внутренние нужды
- При возврате товаров поставщикам

### 1.3 Инвентаризации

**Цель:** Сверка фактических и учетных остатков с автоматической корректировкой

**Файлы для создания:**

```
database/migrations/2024_12_02_000005_create_warehouse_stocktakes_table.php
database/migrations/2024_12_02_000006_create_warehouse_stocktake_items_table.php
app/Models/WarehouseStocktake.php
app/Models/WarehouseStocktakeItem.php
app/Services/Api/Internal/WarehouseStocktakeService/
app/Jobs/ProcessStocktakeResultsJob.php
```

**Структура таблицы warehouse_stocktakes:**

- `id`, `cabinet_id`, `employee_id`, `warehouse_id` - стандартные поля
- `stocktake_date` (datetime) - дата проведения инвентаризации
- `freeze_date` (datetime) - дата фиксации остатков
- `status` (enum) - статус: planned, in_progress, completed, cancelled
- `stocktake_type` (enum) - тип: full, partial, cycle
- `reason` (text) - причина проведения

**Структура таблицы warehouse_stocktake_items:**

- `stocktake_id` (UUID) - ссылка на инвентаризацию
- `product_id` (UUID) - товар
- `warehouse_item_id` (UUID) - конкретная партия
- `book_quantity` (integer) - учетное количество
- `actual_quantity` (integer) - фактическое количество
- `difference` (integer) - разность (actual - book)
- `unit_price` (string) - цена за единицу
- `total_difference_cost` (string) - стоимость разности

**Процесс инвентаризации:**

1. Создание документа инвентаризации
2. Фиксация учетных остатков на дату (`freeze_date`)
3. Блокировка операций с товарами на складе
4. Ввод фактических остатков
5. Расчет разностей
6. Автоматическое создание приходных/расходных ордеров
7. Разблокировка операций

## Этап 2: Система детального резервирования ✅ ВЫПОЛНЕНО

### 2.1 Расширение системы резервов ✅ ВЫПОЛНЕНО

**Созданные файлы:**

```
✅ database/migrations/2024_12_02_000006_create_warehouse_reservations_table.php
✅ app/Models/WarehouseReservation.php
✅ app/Entities/WarehouseReservationEntity.php
✅ app/Repositories/Warehouses/WarehouseReservationsRepository.php
✅ app/Contracts/Repositories/WarehouseReservationsRepositoryContract.php
```

**Реализованная функциональность:**

- ✅ Полная структура таблицы warehouse_reservations
- ✅ Поддержка различных типов резервирования (order, production, transfer, marketing, quality)
- ✅ Система приоритетов резервов (1-10)
- ✅ Автоматическое истечение резервов с настройкой auto_release
- ✅ Связь с документами-основаниями
- ✅ Полный CRUD через репозиторий

**Реализованные поля в warehouse_reservations:**

- ✅ `reservation_type` (enum) - тип резерва: order, production, transfer, marketing, quality
- ✅ `document_type` (string) - тип документа-основания
- ✅ `document_id` (UUID) - ID документа-основания
- ✅ `priority` (integer) - приоритет резерва (1-10)
- ✅ `expires_at` (datetime) - срок действия резерва
- ✅ `auto_release` (boolean) - автоматически снимать при истечении

### 2.2 Система приоритетов резервирования

**Правила приоритетов:**

1. Приоритет 1-3: Критичные заказы (VIP клиенты, срочные заказы)
2. Приоритет 4-6: Обычные заказы клиентов
3. Приоритет 7-8: Внутренние нужды, перемещения
4. Приоритет 9-10: Маркетинговые акции, прочее

**Логика работы:**

- При нехватке товаров резервы с низким приоритетом автоматически снимаются
- Уведомления о снятии резервов отправляются ответственным
- Возможность ручного управления приоритетами

## Этап 3: Единая система движения товаров ✅ ВЫПОЛНЕНО

### 3.1 Расширение warehouse_transactions ✅ ВЫПОЛНЕНО

**Созданные/модифицированные файлы:**

```
✅ database/migrations/2024_12_02_000007_enhance_warehouse_transactions_table.php
✅ app/Models/WarehouseTransaction.php (расширен)
✅ app/Entities/WarehouseTransactionEntity.php
✅ app/Repositories/Warehouses/WarehouseTransactionsRepository.php
✅ app/Contracts/Repositories/WarehouseTransactionsRepositoryContract.php
✅ app/Services/Api/Internal/WarehouseTransactionService/WarehouseTransactionService.php
✅ app/Services/Api/Internal/WarehouseTransactionService/Handlers/TransactionCreateHandler.php
✅ app/Services/Api/Internal/WarehouseTransactionService/Handlers/TransactionBulkCreateHandler.php
✅ app/Services/Api/Internal/WarehouseTransactionService/Handlers/TransactionReportHandler.php
✅ app/Contracts/Services/Internal/WarehouseTransactionServiceContract.php
✅ app/Enums/Api/Internal/WarehouseTransactionOperationTypeEnum.php
```

**Реализованная функциональность:**

- ✅ Расширение таблицы warehouse_transactions новыми полями
- ✅ Поддержка различных типов операций (receipt, issue, transfer, reserve, etc.)
- ✅ Связь с документами-основаниями и резервами
- ✅ Партионный учет (batch_number, lot_number, expiry_date)
- ✅ Контроль качества товаров
- ✅ Полный сервис для работы с транзакциями
- ✅ Отчеты по движению товаров

**Типы операций (operation_type):**

- `receipt` - поступление товаров
- `issue` - расход товаров
- `transfer_out` - списание при перемещении
- `transfer_in` - оприходование при перемещении
- `reserve` - резервирование
- `unreserve` - снятие резерва
- `adjustment_plus` - дооприходование при инвентаризации
- `adjustment_minus` - списание при инвентаризации
- `quality_hold` - блокировка на контроль качества
- `quality_release` - освобождение после контроля качества

### 3.2 Партионный учет ✅ ВЫПОЛНЕНО

**Созданные файлы:**

```
✅ database/migrations/2024_12_02_000008_enhance_warehouse_items_table.php
```

**Реализованная функциональность:**

- ✅ `lot_number` (string) - номер лота/серии
- ✅ `expiry_date` (date) - срок годности
- ✅ `supplier_batch` (string) - номер партии поставщика
- ✅ `quality_status` (enum) - статус качества: good, defective, quarantine, expired
- ✅ `reserved_quantity` (integer) - количество в резерве
- ✅ `available_quantity` (integer) - доступное количество (quantity - reserved_quantity)
- ✅ `last_movement_date` (datetime) - дата последнего движения
- ✅ `storage_location` (string) - место хранения (ячейка, стеллаж)
- ✅ Индексы для быстрого поиска по партиям, срокам годности и качеству





## Этап 4: Адаптация существующих документов для работы в двух режимах ✅ ВЫПОЛНЕНО



### 4.1 Создание условной логики в существующих обработчиках ✅ ВЫПОЛНЕНО

**Цель:** Добавить проверки ордерной схемы в существующие обработчики документов

**Реализованный принцип работы:**
В каждом обработчике документа добавлена проверка:

1. ✅ Определяется склад операции
2. ✅ Проверяется активность ордерной схемы для данного типа операции и даты
3. ✅ Выбирается соответствующая логика обработки

**Модифицированные файлы:**

```
✅ app/Services/Api/Internal/Procurement/AcceptanceItemsService/Handlers/AcceptanceItemsCreateHandler.php
✅ app/Services/Api/Internal/Sales/Shipments/ShipmentsService/Handlers/ShipmentsBulkHeldHandler.php
```

**Реализованная логика в обработчиках:**

```php
// Проверка активности ордерной схемы
$isOrderSchemeActive = $this->orderSchemeDetectionService
    ->isOrderSchemeActiveForReceipts($warehouseId, $dateFrom);

if ($isOrderSchemeActive) {
    // ✅ Логика для ордерной схемы - создание ордеров и транзакций
    $this->processWithOrderScheme($data);
} else {
    // ✅ Существующая логика (без изменений)
    $this->processStandard($data);
}
```

### 4.2 Модификация приемок (Acceptances) ✅ ВЫПОЛНЕНО

**Модифицированные файлы:**

```
✅ app/Services/Api/Internal/Procurement/AcceptanceItemsService/Handlers/AcceptanceItemsCreateHandler.php
```

**Реализованная логика работы в зависимости от режима склада:**

**Обычная схема (без изменений):**

- ✅ Создание warehouse_items напрямую
- ✅ Простое обновление остатков
- ✅ Базовая FIFO логика
- ✅ Добавлены новые поля для партионного учета

**Ордерная схема (новая логика):**

- ✅ При создании позиции приемки автоматически создается приходный ордер
- ✅ Товары создаются с расширенными полями партионного учета
- ✅ Автоматическое создание warehouse_transactions записей
- ✅ Связь с документом-основанием (приемка)
- ✅ Контроль качества товаров (статус good по умолчанию)

### 4.3 Модификация отгрузок (Shipments) ✅ ВЫПОЛНЕНО

**Модифицированные файлы:**

```
✅ app/Services/Api/Internal/Sales/Shipments/ShipmentsService/Handlers/ShipmentsBulkHeldHandler.php
```

**Реализованная логика работы в зависимости от режима склада:**

**Обычная схема (без изменений):**

- ✅ Прямое списание товаров через FIFO
- ✅ Простое обновление остатков
- ✅ Стандартная логика BulkHandleFifoJob

**Ордерная схема (новая логика):**

- ✅ Автоматическое создание расходного ордера при проведении отгрузки
- ✅ Создание транзакций движения товаров для каждой позиции
- ✅ Связь с документом-основанием (отгрузка)
- ✅ Классификация причины списания (shipment)
- ✅ Сохранение стандартной FIFO логики после создания ордеров

### 4.4 Модификация заказов (Customer Orders) ✅ ВЫПОЛНЕНО

**Модифицированные файлы:**

```
✅ app/Services/Api/Internal/Sales/CustomerOrders/CustomerOrdersService/Handlers/CustomerOrderBulkReserveHandler.php
✅ app/Services/Api/Internal/Sales/CustomerOrders/CustomerOrdersService/Handlers/CustomerOrderBulkUnreserveHandler.php
```

**Реализованная логика работы в зависимости от режима склада:**

**Обычная схема (без изменений):**

- ✅ Простой флаг резервирования (reserve=true/false)
- ✅ Проверка остатков при создании заказа

**Ордерная схема (новая логика):**

- ✅ Автоматическое детальное резервирование конкретных партий товаров (FIFO)
- ✅ Создание записей в warehouse_reservations с привязкой к позициям заказа
- ✅ Обновление available_quantity и reserved_quantity в warehouse_items
- ✅ Возможность частичного резервирования с логированием недостач
- ✅ Автоматическое снятие детальных резервов при отмене резервирования
- ✅ Возврат товаров в доступные остатки при снятии резервов

### 4.5 Модификация перемещений (Goods Transfers)

**Логика работы в зависимости от режимов складов:**

**Обычная схема (без изменений):**

1. Прямое списание с одного склада и оприходование на другой
2. Простая FIFO логика

**Ордерная схема (новая логика):**

1. Резервирование товаров на складе-отправителе
2. Создание расходного ордера на складе-отправителе
3. Создание приходного ордера на складе-получателе
4. Контроль качества при перемещении

**Смешанные режимы:**

- Если склад-отправитель в ордерной схеме, а получатель в обычной - создается только расходный ордер
- Если склад-отправитель в обычной схеме, а получатель в ордерной - создается только приходный ордер

## Этап 5: Система валидации с учетом режимов работы ✅ ВЫПОЛНЕНО

### 5.1 Условная валидация в зависимости от режима ✅ ВЫПОЛНЕНО

**Созданные файлы:**

```
✅ app/Services/Api/Internal/WarehouseOrderScheme/ValidationService.php
✅ app/Services/Api/Internal/WarehouseOrderScheme/Handlers/ValidationHandler.php
✅ app/Services/Api/Internal/WarehouseOrderScheme/Handlers/ReservationValidationHandler.php
✅ app/Services/Api/Internal/WarehouseOrderScheme/Handlers/StockValidationHandler.php
✅ app/Contracts/Services/Internal/WarehouseOrderSchemeValidationServiceContract.php
```

**Реализованный принцип работы:**

- ✅ Валидация применяется только к складам с ордерной схемой
- ✅ Для обычных складов сохраняется текущая логика валидации
- ✅ Автоматическое определение режима и выбор соответствующей валидации

**Реализованные правила валидации для ордерной схемы:**

- ✅ Проверка резервов перед отгрузкой товаров
- ✅ Запрет перемещения зарезервированных под заказы товаров
- ✅ Валидация доступности товаров для резервирования
- ✅ Контроль сроков годности при операциях
- ✅ Проверка приоритетов резервов
- ✅ Валидация качества товаров (good, defective, quarantine, expired)
- ✅ Контроль максимальных сроков резервирования

**Правила для обычной схемы (без изменений):**

- Базовая проверка наличия товаров
- Простая валидация количеств

### 5.2 Система блокировок

**Автоматические блокировки:**

- Блокировка товаров на время инвентаризации
- Блокировка просроченных товаров
- Блокировка товаров на контроле качества
- Блокировка при превышении лимитов резервирования

### 5.3 Система уведомлений

**Файлы для создания:**

```
app/Notifications/WarehouseOrderScheme/
app/Jobs/WarehouseOrderScheme/
```

**Типы уведомлений:**

- Попытка нарушения правил ордерной схемы
- Просроченные резервы
- Товары с истекающим сроком годности
- Критически низкие остатки
- Результаты инвентаризации

## Этап 6: API для новых документов ордерной схемы ✅ ВЫПОЛНЕНО

### 6.1 Контроллеры для документов ордерной схемы ✅ ВЫПОЛНЕНО

**Цель:** Создать API для управления новыми типами документов (приходные/расходные ордера, резервы)

**Созданные файлы:**

```
✅ app/Http/Controllers/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrdersController.php
✅ app/Http/Controllers/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrdersController.php
✅ app/Http/Controllers/Api/Internal/WarehouseOrderScheme/WarehouseReservationsController.php
✅ app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrderStoreRequest.php
✅ app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrderUpdateRequest.php
✅ app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrderStoreRequest.php
✅ app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrderUpdateRequest.php
✅ app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseReservationStoreRequest.php
✅ app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrderResource.php
✅ app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrderCollection.php
✅ app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrderResource.php
✅ app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrderCollection.php
✅ app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseReservationResource.php
✅ app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseReservationCollection.php
```

**Реализованная функциональность контроллеров:**

- ✅ CRUD операции для приходных и расходных ордеров
- ✅ Валидация что склад работает в ордерной схеме
- ✅ Управление резервами (создание, отмена, освобождение истекших)
- ✅ Проведение документов с контролем статуса
- ✅ Получение справочной информации (причины списания, типы резервов)
- ✅ Полная валидация через FormRequest классы
- ✅ Структурированные ответы через API Resources

### 6.2 Requests для валидации новых документов

**Цель:** Валидация данных для документов ордерной схемы с проверкой активности схемы

**Файлы для создания:**

```
app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrderStoreRequest.php
app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrderStoreRequest.php
app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseStocktakeStoreRequest.php
app/Http/Requests/Api/Internal/WarehouseOrderScheme/WarehouseReservationStoreRequest.php
```

**Особенности валидации:**

- Проверка что склад работает в ордерной схеме
- Валидация доступности товаров для резервирования
- Проверка прав доступа к операциям ордерной схемы

### 6.3 Resources для API ответов новых документов

**Цель:** Форматирование ответов API для документов ордерной схемы

**Файлы для создания:**

```
app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseReceiptOrderResource.php
app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseIssueOrderResource.php
app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseStocktakeResource.php
app/Http/Resources/Api/Internal/WarehouseOrderScheme/WarehouseReservationResource.php
```

**Особенности ресурсов:**

- Включение информации о связанных документах-основаниях
- Отображение статусов резервирования и движения товаров
- Детализация по партиям и срокам годности

## Этап 7: Отчетность и аналитика

### 7.1 Складские отчеты

**Файлы для создания:**

```
app/Services/Api/Internal/Reports/WarehouseOrderScheme/
app/Http/Controllers/Api/Internal/Reports/WarehouseOrderSchemeController.php
```

**Отчеты:**

1. **Оборотно-сальдовая ведомость:**
    - Остатки на начало/конец периода по складам и товарам
    - Обороты по приходу/расходу с детализацией по документам
    - Анализ оборачиваемости товаров

2. **Отчет по резервам:**
    - Активные резервы по товарам и документам-основаниям
    - Просроченные резервы с указанием ответственных
    - Анализ эффективности резервирования

3. **Отчет по партиям:**
    - Движение конкретных партий товаров
    - Анализ сроков годности и качества
    - FIFO анализ списания партий

4. **Контрольные отчеты:**
    - Товары с отрицательными остатками
    - Резервы без покрытия товарами
    - Нарушения правил ордерной схемы
    - Товары с истекающим сроком годности

### 7.2 Аналитические дашборды

**Ключевые метрики:**

- Процент товаров под резервом
- Средний срок резервирования
- Количество нарушений ордерной схемы
- Эффективность использования складских площадей
- Оборачиваемость товаров по партиям

## Этап 8: Настройки и конфигурация

### 8.1 Расширение настроек ордерной схемы

**Файлы для модификации:**

```
database/migrations/2024_12_02_000010_enhance_warehouse_order_schemes_table.php
app/Services/Api/Internal/Warehouses/WarehouseService/DTO/WarehouseDTO.php
```

**Новые настройки:**

- `auto_reserve_orders` (boolean) - автоматически резервировать при создании заказов
- `reserve_expiry_days` (integer) - срок действия резервов по умолчанию
- `allow_negative_stock` (boolean) - разрешить отрицательные остатки
- `strict_fifo` (boolean) - строгое соблюдение FIFO
- `quality_control_required` (boolean) - обязательный контроль качества
- `expiry_control_days` (integer) - за сколько дней до истечения блокировать товары
- `stocktake_frequency_days` (integer) - периодичность инвентаризаций
- `auto_create_orders` (boolean) - автоматически создавать ордера при операциях

### 8.2 Настройки по типам товаров

**Файлы для создания:**

```
database/migrations/2024_12_02_000011_create_product_warehouse_settings_table.php
app/Models/ProductWarehouseSetting.php
```

**Настройки на уровне товаров:**

- Требования к контролю качества
- Особенности хранения и резервирования
- Приоритеты при нехватке товаров
- Специальные правила FIFO

## Этап 9: Миграция и поэтапное внедрение

### 9.1 Скрипты для работы с режимами

**Файлы для создания:**

```
app/Console/Commands/WarehouseMode/SwitchWarehouseModeCommand.php
app/Console/Commands/WarehouseMode/MigrateToOrderSchemeCommand.php
app/Console/Commands/WarehouseMode/ValidateDataIntegrityCommand.php
app/Console/Commands/WarehouseMode/RollbackModeCommand.php
```

**Процесс перехода склада на ордерную схему:**

1. Проверка возможности переключения (отсутствие активных операций)
2. Создание снимка текущих остатков
3. Создание начальных резервов для существующих заказов
4. Создание warehouse_transactions для исторических операций
5. Переключение режима работы склада
6. Валидация целостности данных

### 9.2 Стратегия поэтапного внедрения

**Принципы внедрения:**

- Добровольный переход - склады переключаются по мере готовности
- Возможность отката в течение определенного периода
- Параллельная работа складов в разных режимах
- Постепенное обучение пользователей

**План внедрения:**

1. **Разработка и тестирование:** Создание всех компонентов гибридной системы
2. **Тестовая среда:** Полное тестирование переключения режимов
3. **Пилотный склад:** Переключение одного склада с минимальным оборотом
4. **Постепенное расширение:** Переключение складов по мере готовности
5. **Стабилизация:** Работа в смешанном режиме без принуждения к переходу

**Важно:** Никогда не принуждаем к переходу на ордерную схему - это опциональная функциональность

### 9.3 Обучение и документация

**Файлы для создания:**

```
docs/warehouse-order-scheme-user-guide.md
docs/warehouse-order-scheme-api-documentation.md
docs/warehouse-order-scheme-troubleshooting.md
```

## Этап 10: Мониторинг и оптимизация

### 10.1 Система мониторинга

**Файлы для создания:**

```
app/Services/Api/Internal/WarehouseOrderScheme/MonitoringService.php
app/Jobs/WarehouseOrderScheme/HealthCheckJob.php
```

**Метрики для мониторинга:**

- Производительность операций резервирования
- Количество ошибок валидации
- Время выполнения FIFO операций
- Использование памяти при массовых операциях

### 10.2 Оптимизация производительности

**Области оптимизации:**

- Индексы для быстрого поиска резервов и партий
- Кэширование часто запрашиваемых остатков
- Асинхронная обработка массовых операций
- Архивирование старых транзакций

## Дополнительные соображения

### Интеграция с маркетплейсами

**Особенности работы с Ozon/Wildberries:**

- Автоматическое резервирование при синхронизации заказов
- Специальные правила резервирования для FBS/FBO
- Интеграция с системой отгрузок маркетплейсов
- Контроль остатков при загрузке на площадки

### Производительность и масштабирование

**Критические моменты:**

- Операции резервирования должны выполняться атомарно
- Массовые операции (инвентаризации) требуют очередей
- Индексы для быстрого поиска по партиям и резервам
- Партиционирование таблиц транзакций по датам

### Безопасность и права доступа

**Уровни доступа:**

- Кладовщики: создание ордеров, просмотр остатков
- Менеджеры: управление резервами, проведение инвентаризаций
- Администраторы: настройка ордерной схемы, доступ к отчетам
- Аудиторы: только просмотр всех операций

### Интеграция с внешними системами

**Возможные интеграции:**

- 1С для обмена документами
- WMS системы для управления складом
- Системы маркировки товаров
- ERP системы для планирования

## Риски и их митигация

### Технические риски

1. **Производительность при больших объемах:**
    - Риск: Медленная работа при миллионах транзакций
    - Митигация: Индексы, партиционирование, кэширование

2. **Целостность данных:**
    - Риск: Рассинхронизация остатков и резервов
    - Митигация: Транзакции, валидация, регулярные проверки

3. **Сложность миграции:**
    - Риск: Потеря данных при переходе на новую схему
    - Митигация: Поэтапная миграция, резервные копии, откат

### Бизнес-риски

1. **Сопротивление пользователей:**
    - Риск: Отказ от использования новой системы
    - Митигация: Обучение, постепенное внедрение, поддержка

2. **Увеличение сложности процессов:**
    - Риск: Замедление работы из-за дополнительных проверок
    - Митигация: Автоматизация, оптимизация интерфейсов

## Метрики успеха

### KPI для оценки эффективности

1. **Точность учета:**
    - Процент расхождений при инвентаризациях (цель: <1%)
    - Количество ошибок резервирования (цель: <0.1%)

2. **Операционная эффективность:**
    - Время выполнения операций резервирования (цель: <1 сек)
    - Процент автоматизированных операций (цель: >90%)

3. **Удовлетворенность пользователей:**
    - Оценка удобства интерфейса (цель: >4/5)
    - Количество обращений в поддержку (цель: снижение на 50%)

## Заключение

Данный план обеспечивает создание гибридной системы складского учета с поддержкой двух режимов работы:

### Преимущества гибридного подхода:

**Для бизнеса:**

- Нет принуждения к изменению привычных процессов
- Возможность постепенного перехода на ордерную схему
- Сохранение инвестиций в обучение персонала
- Гибкость выбора подходящего режима для каждого склада

**Для разработки:**

- Обратная совместимость с существующим кодом
- Минимальные изменения в текущих процессах
- Возможность тестирования новой функциональности без риска
- Поэтапная разработка и внедрение

**Для пользователей:**

- Привычные интерфейсы продолжают работать
- Новые возможности доступны по желанию
- Обучение новым функциям происходит постепенно
- Возможность отката при необходимости

### Ключевые принципы реализации:

1. **Опциональность** - ордерная схема как дополнительная возможность, а не замена
2. **Совместимость** - единые API и интерфейсы для обеих схем
3. **Гибкость** - возможность переключения режимов на уровне склада
4. **Безопасность** - тщательная валидация и возможность отката
5. **Производительность** - оптимизация для работы в смешанном режиме

Реализация должна проводиться поэтапно с акцентом на сохранение стабильности существующих процессов и предоставление
пользователям выбора наиболее подходящего режима работы для их задач.
