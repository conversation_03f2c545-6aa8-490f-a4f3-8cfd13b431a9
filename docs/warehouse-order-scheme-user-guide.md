# Руководство пользователя: Ордерная схема складского учета

## Введение

Ордерная схема складского учета - это расширенная система управления складскими операциями, которая обеспечивает:

- **Детальный партионный учет** товаров с контролем качества
- **Систему резервирования** товаров под заказы
- **Строгий контроль** движения товаров через документы-ордера
- **Отслеживание сроков годности** и качества товаров
- **Детальную аналитику** и отчетность

## Как определить режим работы склада

Система автоматически определяет режим работы склада на основе существующих настроек:

### Ордерная схема активна, если:
- ✅ Склад настроен на **детальный учет партий**
- ✅ Включен **контроль сроков годности**
- ✅ Активирован **контроль качества товаров**

### Обычная схема используется, если:
- ❌ Склад работает без детального учета партий
- ❌ Не требуется контроль сроков годности
- ❌ Упрощенный учет без контроля качества

## Основные отличия схем

| Функция | Обычная схема | Ордерная схема |
|---------|---------------|----------------|
| **Учет товаров** | По общим остаткам | По партиям с детализацией |
| **Резервирование** | Простое | С приоритетами и сроками |
| **Документы** | Стандартные | Приходные/расходные ордера |
| **Контроль качества** | Базовый | Детальный (good/defective/quarantine/expired) |
| **Сроки годности** | Опционально | Обязательный контроль |
| **Отчетность** | Стандартная | Расширенная аналитика |

## Работа с документами ордерной схемы

### Приходные ордера

**Назначение:** Оформление поступления товаров на склад

**Основные поля:**
- **Склад** - на который поступает товар
- **Сотрудник** - ответственный за приемку
- **Дата** - дата поступления
- **Основание** - документ-основание (накладная, счет)
- **Позиции товаров** с указанием:
  - Количество
  - Цена за единицу
  - Номер партии
  - Срок годности
  - Статус качества (хорошее/брак/карантин)

**Статусы:**
- 🟡 **Черновик** - документ создан, но не проведен
- 🟢 **Проведен** - товары оприходованы на склад

### Расходные ордера

**Назначение:** Оформление списания товаров со склада

**Основные поля:**
- **Склад** - с которого списывается товар
- **Причина списания**:
  - `defective` - Брак
  - `expired` - Истек срок годности
  - `shortage` - Недостача
  - `internal_use` - Внутренние нужды
  - `return_to_supplier` - Возврат поставщику
  - `damage` - Повреждение
  - `other` - Прочее
- **Позиции товаров** с указанием конкретных партий

**Важно:** В ордерной схеме нельзя списать товар, который зарезервирован под заказы клиентов.

## Система резервирования

### Типы резервирования

1. **order** - Под заказы клиентов (высший приоритет)
2. **production** - Под производственные нужды
3. **transfer** - Под перемещения между складами
4. **marketing** - Под маркетинговые акции
5. **quality** - Под контроль качества

### Приоритеты резервов

Резервы имеют приоритеты от 1 (высший) до 10 (низший):
- **1-3** - Критически важные заказы
- **4-6** - Обычные заказы (по умолчанию 5)
- **7-10** - Низкоприоритетные резервы

### Сроки действия резервов

- **По умолчанию:** 30 дней
- **Максимум:** 90 дней
- **Автоосвобождение:** Истекшие резервы автоматически освобождаются

## Контроль качества товаров

### Статусы качества

- **good** - Товар в хорошем состоянии, готов к отгрузке
- **defective** - Бракованный товар, требует списания
- **quarantine** - Товар на карантине, требует проверки
- **expired** - Товар с истекшим сроком годности

### Контроль сроков годности

Система автоматически отслеживает:
- ⚠️ **Товары, истекающие в течение 7 дней** - предупреждения
- ❌ **Просроченные товары** - блокировка отгрузки
- 📊 **Аналитика по срокам** - в отчетах

## Отчеты и аналитика

### Отчет по движению товаров

**Путь:** `GET /warehouse-order-scheme/reports/stock-movement`

**Показывает:**
- Все поступления и списания за период
- Детализация по партиям (в ордерной схеме)
- Сводка по товарам
- Чистое движение (приход - расход)

### Отчет по резервированию

**Путь:** `GET /warehouse-order-scheme/reports/reservations`

**Показывает:**
- Все резервы за период
- Группировка по типам резервирования
- Статистика выполнения резервов
- Анализ эффективности резервирования

### Аналитика ордерной схемы

**Путь:** `GET /warehouse-order-scheme/reports/analytics`

**Включает:**
- Статистику документов (приходные/расходные ордера)
- Эффективность резервирования
- Оборачиваемость товаров
- Контроль качества
- Управление сроками годности

### Отчет по остаткам

**Путь:** `GET /warehouse-order-scheme/reports/inventory`

**Показывает:**
- Детальные остатки по партиям
- Доступные и зарезервированные количества
- Группировка по товарам и качеству
- Анализ сроков годности

## Практические рекомендации

### Для менеджеров складов

1. **Регулярно проверяйте** отчет по срокам годности
2. **Контролируйте** эффективность резервирования
3. **Анализируйте** причины списаний в расходных ордерах
4. **Следите** за оборачиваемостью товаров

### Для операторов склада

1. **Всегда указывайте** корректные сроки годности при приемке
2. **Проверяйте качество** товаров и устанавливайте правильный статус
3. **Соблюдайте FIFO** - отгружайте товары с более ранними сроками
4. **Не списывайте** зарезервированные товары без согласования

### Для разработчиков

1. **Используйте API** с правильными политиками авторизации
2. **Проверяйте** режим работы склада перед операциями
3. **Обрабатывайте** ошибки валидации ордерной схемы
4. **Учитывайте** различия в логике для разных схем

## Часто задаваемые вопросы

**Q: Можно ли переключить склад с обычной схемы на ордерную?**
A: Да, система автоматически определит режим при изменении настроек склада.

**Q: Что происходит с существующими данными при переключении?**
A: Данные сохраняются, но новые операции будут работать по ордерной схеме.

**Q: Можно ли работать со складами в разных режимах одновременно?**
A: Да, система поддерживает гибридную работу - разные склады могут работать в разных режимах.

**Q: Как отменить резерв товара?**
A: Используйте API `POST /warehouse-reservations/{id}/cancel` или измените статус резерва.

**Q: Что делать с просроченными товарами?**
A: Создайте расходный ордер с причиной "expired" для списания просроченных товаров.

## Техническая поддержка

При возникновении проблем:

1. **Проверьте** настройки склада
2. **Убедитесь** в корректности данных
3. **Изучите** сообщения об ошибках в API
4. **Обратитесь** к документации по устранению неполадок
5. **Свяжитесь** с технической поддержкой
