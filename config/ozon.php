<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Ozon System API Configuration
    |--------------------------------------------------------------------------
    |
    | Настройки для системного API Ozon для синхронизации складов
    |
    */

    'system_api' => [
        'client_id' => env('OZON_SYSTEM_CLIENT_ID'),
        'api_key' => env('OZON_SYSTEM_API_KEY'),
        'enabled' => env('OZON_SYSTEM_SYNC_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | System Warehouses Configuration
    |--------------------------------------------------------------------------
    |
    | Настройки для синхронизации системных складов
    |
    */
    
    'system_warehouses' => [
        'sync_enabled' => env('OZON_SYSTEM_WAREHOUSES_SYNC_ENABLED', true),
        'root_group_name' => 'OZON',
        'sync_schedule' => '0 */6 * * *', // Каждые 6 часов
    ],
];