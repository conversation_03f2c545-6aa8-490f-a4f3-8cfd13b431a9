<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\VatRate;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use App\Models\WarehouseIssueOrder;
use App\Models\WarehouseIssueOrderItem;
use App\Models\WarehouseOrderScheme;
use App\Models\WarehouseOrderScheme;
use App\Models\WarehouseReservation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseIssueOrderItemsTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $employee;
    protected $cabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $vatRate;
    protected $warehouseItem;
    protected $issueOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем ордерную схему отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'available_quantity' => 100,
            'quality_status' => 'good',
            'batch_number' => 'BATCH001',
        ]);

        $this->issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => false,
        ]);
    }

    public function test_creates_issue_order_items_with_order(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-001',
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'reason_description' => 'Обнаружен брак',
            'total_quantity' => 30,
            'total_cost' => '4500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 20,
                    'unit_price' => '150.00',
                    'total_price' => '3000.00',
                    'batch_number' => 'BATCH001',
                    'lot_number' => 'LOT001',
                    'expiry_date' => '2024-12-31',
                    'vat_rate_id' => $this->vatRate->id,
                ],
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                    'batch_number' => 'BATCH001',
                    'vat_rate_id' => $this->vatRate->id,
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(201);

        $issueOrder = WarehouseIssueOrder::where('number', 'РО-001')->first();
        $this->assertNotNull($issueOrder);

        // Проверяем, что создались позиции
        $items = WarehouseIssueOrderItem::where('issue_order_id', $issueOrder->id)->get();
        $this->assertCount(2, $items);

        // Проверяем первую позицию
        $firstItem = $items->where('quantity', 20)->first();
        $this->assertEquals($this->product->id, $firstItem->product_id);
        $this->assertEquals($this->warehouseItem->id, $firstItem->warehouse_item_id);
        $this->assertEquals(20, $firstItem->quantity);
        $this->assertEquals('150.00', $firstItem->unit_price);
        $this->assertEquals('3000.00', $firstItem->total_price);
        $this->assertEquals('BATCH001', $firstItem->batch_number);

        // Проверяем вторую позицию
        $secondItem = $items->where('quantity', 10)->first();
        $this->assertEquals(10, $secondItem->quantity);
        $this->assertEquals('1500.00', $secondItem->total_price);
    }

    public function test_validates_required_item_fields(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    // warehouse_item_id отсутствует
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.warehouse_item_id']);
    }

    public function test_validates_warehouse_item_belongs_to_warehouse(): void
    {
        // Arrange - создаем товар на другом складе
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $otherWarehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $otherWarehouse->id,
            'product_id' => $this->product->id,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $otherWarehouseItem->id, // Товар с другого склада
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_sufficient_quantity_available(): void
    {
        // Arrange - товар с недостаточным количеством
        $lowStockItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 5,
            'available_quantity' => 5,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $lowStockItem->id,
                    'quantity' => 10, // Больше чем доступно
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_cannot_write_off_reserved_items(): void
    {
        // Arrange - создаем резерв
        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 50,
            'status' => 'reserved',
        ]);

        // Обновляем доступное количество
        $this->warehouseItem->update([
            'available_quantity' => 50,
            'reserved_quantity' => 50,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 60,
            'total_cost' => '9000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 60, // Больше чем доступно (50)
                    'unit_price' => '150.00',
                    'total_price' => '9000.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_updates_warehouse_items_when_order_is_held(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-HOLD',
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 20,
            'total_cost' => '3000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 20,
                    'unit_price' => '150.00',
                    'total_price' => '3000.00',
                ]
            ]
        ];

        // Act - создаем ордер
        $createResponse = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);
        $createResponse->assertStatus(201);

        $issueOrder = WarehouseIssueOrder::where('number', 'РО-HOLD')->first();

        // Act - проводим ордер
        $holdResponse = $this->postJson("/api/internal/warehouse-order-scheme/warehouse-issue-orders/{$issueOrder->id}/hold");

        // Assert
        $holdResponse->assertStatus(200);

        // Проверяем, что количество уменьшилось
        $this->warehouseItem->refresh();
        $this->assertEquals(80, $this->warehouseItem->quantity);
        $this->assertEquals(80, $this->warehouseItem->available_quantity);

        // Проверяем, что создались транзакции
        $this->assertDatabaseHas('warehouse_transactions', [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'transaction_type' => 'out',
            'quantity' => 20,
            'document_type' => 'warehouse_issue_order',
            'document_id' => $issueOrder->id,
        ]);
    }

    public function test_cannot_modify_items_of_held_order(): void
    {
        // Arrange - создаем проведенный ордер
        $heldOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        WarehouseIssueOrderItem::factory()->create([
            'issue_order_id' => $heldOrder->id,
            'product_id' => $this->product->id,
            'warehouse_item_id' => $this->warehouseItem->id,
        ]);

        $updateData = [
            'number' => 'UPDATED',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 50, // Пытаемся изменить количество
                    'unit_price' => '150.00',
                    'total_price' => '7500.00',
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouse-order-scheme/warehouse-issue-orders/{$heldOrder->id}", $updateData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_positive_quantities(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => -5, // Отрицательное количество
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quantity']);
    }

    public function test_validates_warehouse_item_exists(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => 'non-existent-uuid',
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.warehouse_item_id']);
    }
}
