<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\WarehouseCellSize;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehouseCellSizeTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_warehouse_cell_sizes_list(): void
    {
        // Arrange
        // Создаем 3 размера ячеек для нашего кабинета
        WarehouseCellSize::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем размер для другого кабинета, чтобы убедиться что не получим его
        WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/sizes?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'name',
                        'cabinet_id',
                        'unlimited_size',
                        'height',
                        'width',
                        'length',
                        'measurement_unit_size_id',
                        'volume',
                        'measurement_unit_volume_id',
                        'unlimited_load_capacity',
                        'load_capacity',
                        'measurement_unit_load_capacity_id',
                ],
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_warehouse_cell_sizes(): void
    {
        // Arrange
        WarehouseCellSize::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/sizes?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertForbidden();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/warehouses/cells/sizes?' . http_build_query([
            'cabinet_id' => 'not-a-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
            'sortField' => 'invalid_field',
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
                'sortField',
                'fields.0',
                'fields.1'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Arrange
        WarehouseCellSize::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/sizes?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'name', 'cabinet_id']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'cabinet_id'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(2, 'data');
    }

    public function test_index_returns_empty_collection_when_no_sizes(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/sizes?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/sizes');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_can_create_warehouse_cell_size(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Size',
            'unlimited_size' => false,
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'unlimited_load_capacity' => false,
            'load_capacity' => 1000
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/sizes', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'unlimited_size' => $data['unlimited_size'],
            'height' => $data['height'],
            'width' => $data['width'],
            'length' => $data['length'],
            'volume' => $data['volume'],
            'unlimited_load_capacity' => $data['unlimited_load_capacity'],
            'load_capacity' => $data['load_capacity']
        ]);
    }

    public function test_can_create_unlimited_size_warehouse_cell_size(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Unlimited Size',
            'unlimited_size' => true,
            'unlimited_load_capacity' => true
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/sizes', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'unlimited_size' => true,
            'height' => null,
            'width' => null,
            'length' => null,
            'volume' => null,
            'unlimited_load_capacity' => true,
            'load_capacity' => null
        ]);
    }

    public function test_cannot_create_warehouse_cell_size_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Size',
            'unlimited_size' => false,
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'unlimited_load_capacity' => false,
            'load_capacity' => 1000
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/sizes', $data);

        // Assert
        $response->assertForbidden();
    }

    public function test_store_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/sizes', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'height',
                'width',
                'length',
                'volume',
                'load_capacity'
            ]);
    }

    public function test_store_conditional_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Size',
            'unlimited_size' => false,
            // Отсутствуют обязательные поля для unlimited_size = false
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/sizes', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'height',
                'width',
                'length',
                'volume'
            ]);

        // Проверяем для unlimited_load_capacity
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Size',
            'unlimited_load_capacity' => false,
            // Отсутствует обязательное поле load_capacity
        ];

        $response = $this->postJson('/api/internal/warehouses/cells/sizes', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['load_capacity']);
    }

    public function test_can_create_warehouse_cell_size_with_minimal_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Minimal Size',
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'load_capacity' => 1000
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/sizes', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'unlimited_size' => false, // дефолтное значение
            'height' => $data['height'],
            'width' => $data['width'],
            'length' => $data['length'],
            'volume' => $data['volume'],
            'unlimited_load_capacity' => false, // дефолтное значение
            'load_capacity' => $data['load_capacity'],
            'measurement_unit_size_id' => null,
            'measurement_unit_volume_id' => null,
            'measurement_unit_load_capacity_id' => null
        ]);
    }

    public function test_can_update_warehouse_cell_size(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'unlimited_size' => false,
            'unlimited_load_capacity' => false
        ]);

        $data = [
            'name' => 'Updated Size',
            'height' => 150,
            'width' => 250,
            'length' => 350,
            'volume' => 13125000,
            'load_capacity' => 2000
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $cellSize->id,
            'cabinet_id' => $cellSize->cabinet_id,
            'name' => $data['name'],
            'height' => $data['height'],
            'width' => $data['width'],
            'length' => $data['length'],
            'volume' => $data['volume'],
            'load_capacity' => $data['load_capacity']
        ]);
    }

    public function test_can_update_to_unlimited_size(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'unlimited_size' => false,
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'unlimited_load_capacity' => false,
            'load_capacity' => 1000
        ]);

        $data = [
            'name' => 'Unlimited Size',
            'unlimited_size' => true,
            'unlimited_load_capacity' => true
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $cellSize->id,
            'name' => $data['name'],
            'unlimited_size' => true,
            'height' => null,
            'width' => null,
            'length' => null,
            'volume' => null,
            'unlimited_load_capacity' => true,
            'load_capacity' => null
        ]);
    }

    public function test_cannot_update_warehouse_cell_size_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'Updated Size',
            'height' => 150,
            'width' => 250,
            'length' => 350,
            'volume' => 13125000,
            'load_capacity' => 2000
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$otherCabinetCellSize->id}", $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_update_required_fields(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'height',
                'width',
                'length',
                'volume',
                'load_capacity'
            ]);
    }

    public function test_update_conditional_required_fields(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => 'Test Size',
            'unlimited_size' => false
            // Отсутствуют обязательные поля для unlimited_size = false
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'height',
                'width',
                'length',
                'volume'
            ]);

        // Проверяем для unlimited_load_capacity
        $data = [
            'name' => 'Test Size',
            'unlimited_load_capacity' => false
            // Отсутствует обязательное поле load_capacity
        ];

        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}", $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['load_capacity']);
    }

    public function test_cannot_update_non_existent_warehouse_cell_size(): void
    {
        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/" . $this->faker->uuid(), [
            'name' => 'Test Size',
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'load_capacity' => 1000
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_warehouse_cell_size_with_minimal_fields(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Size',
            'unlimited_size' => false,
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'unlimited_load_capacity' => false,
            'load_capacity' => 1000
        ]);

        $data = [
            'name' => 'Updated Minimal Size',
            'height' => 150,
            'width' => 250,
            'length' => 350,
            'volume' => 13125000,
            'load_capacity' => 2000
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $cellSize->id,
            'cabinet_id' => $cellSize->cabinet_id,
            'name' => $data['name'],
            'unlimited_size' => false, // остается прежним
            'height' => $data['height'],
            'width' => $data['width'],
            'length' => $data['length'],
            'volume' => $data['volume'],
            'unlimited_load_capacity' => false, // остается прежним
            'load_capacity' => $data['load_capacity'],
            'measurement_unit_size_id' => null, // сбрасывается в null
            'measurement_unit_volume_id' => null, // сбрасывается в null
            'measurement_unit_load_capacity_id' => null // сбрасывается в null
        ]);
    }

    public function test_can_show_warehouse_cell_size(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Size',
            'unlimited_size' => false,
            'height' => 100,
            'width' => 200,
            'length' => 300,
            'volume' => 6000000,
            'unlimited_load_capacity' => false,
            'load_capacity' => 1000
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'name',
                'cabinet_id',
                'unlimited_size',
                'height',
                'width',
                'length',
                'measurement_unit_size_id',
                'volume',
                'measurement_unit_volume_id',
                'unlimited_load_capacity',
                'load_capacity',
                'measurement_unit_load_capacity_id'
            ])
            ->assertJson([
                'id' => $cellSize->id,
                'name' => $cellSize->name,
                'cabinet_id' => $cellSize->cabinet_id,
                'unlimited_size' => false,
                'height' => $cellSize->height,
                'width' => $cellSize->width,
                'length' => $cellSize->length,
                'volume' => $cellSize->volume,
                'unlimited_load_capacity' => false,
                'load_capacity' => $cellSize->load_capacity
            ]);
    }

    public function test_cannot_show_warehouse_cell_size_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/sizes/{$otherCabinetCellSize->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_warehouse_cell_size(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/sizes/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_warehouse_cell_size(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/sizes/{$cellSize->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_cell_sizes', [
            'id' => $cellSize->id
        ]);
    }

    public function test_cannot_delete_warehouse_cell_size_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetCellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/sizes/{$otherCabinetCellSize->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('warehouse_cell_sizes', [
            'id' => $otherCabinetCellSize->id
        ]);
    }

    public function test_cannot_delete_non_existent_warehouse_cell_size(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/sizes/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
