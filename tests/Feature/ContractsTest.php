<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Bookmark;
use App\Models\Contract;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\LegalEntity;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\CabinetCurrency;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Enums\Api\Internal\ContractTypeEnum;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class ContractsTest extends TestCase
{
    use WithFaker;
    use DatabaseTransactions;

    private Employee $employee;
    private Cabinet $cabinet;
    private Bookmark $bookmark;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_contracts(): void
    {
        Contract::factory()->count(10)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        Contract::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Contract::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/contracts?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'data',
                'meta'
            ])
            ->assertJsonCount(10, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_cannot_get_contracts_in_other_cabinet(): void
    {
        Contract::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/contracts?' . http_build_query([
                'cabinet_id' => $this->otherCabinet->id,
            ]));

        $response->assertForbidden()
            ->assertJsonMissing(['data']);
    }

    public function test_can_view_contract(): void
    {
        $contract = Contract::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $response = $this->getJson("/api/internal/contracts/{$contract->id}");

        $response->assertOk()
            ->assertJson([
                'id' => $contract->id,
                'cabinet_id' => $contract->cabinet_id,
                'employee_id' => $contract->employee_id,
                'number' => $contract->number,
                'contractor_id' => $contract->contractor_id,
            ]);
    }

    public function test_cant_view_bookmark_in_other_cabinet(): void
    {
        $bookmark = Contract::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson("/api/internal/contracts/{$bookmark->id}");

        $response->assertNotFound();
    }

    public function test_can_store_contract(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'number' => $this->faker()->word,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'code' => $this->faker()->word,
            'amount' => (string)random_int(1, 1000),
            'comment' => $this->faker()->text,
            'type' => $this->faker()->randomElement(ContractTypeEnum::cases())->value,
            'date_from' => now()->format('Y-m-d H:i:s')
        ];

        $response = $this->postJson("/api/internal/contracts", $data);

        $response->assertCreated();

        $this->assertDatabaseHas(
            'contracts',
            [
                'id' => $response->json('id'),
                'cabinet_id' => $data['cabinet_id'],
                'employee_id' => $data['employee_id'],
                'contractor_id' => $data['contractor_id'],
                'legal_entity_id' => $data['legal_entity_id'],
                'currency_id' => $data['currency_id'],
                'code' => $data['code'],
            ]
        );
    }

    public function test_cant_store_contract_in_other_cabinet(): void
    {
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'number' => $this->faker()->word,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'code' => $this->faker()->word,
            'amount' => (string)random_int(1, 1000),
            'comment' => $this->faker()->text,
            'type' => $this->faker()->randomElement(ContractTypeEnum::cases())->value,
            'date_from' => now()->format('Y-m-d H:i:s')
        ];

        $response = $this->postJson("/api/internal/contracts", $data);

        $response->assertForbidden();
    }

    public function test_can_update_contract(): void
    {
        $contract = Contract::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $data = [
            'number' => $this->faker()->word,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'type' => $this->faker()->randomElement(ContractTypeEnum::cases())->value,
            'date_from' => now()->format('Y-m-d H:i:s')
        ];

        $response = $this->putJson("/api/internal/contracts/{$contract->id}", $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('contracts', [
            'id' => $contract->id,
            'cabinet_id' => $contract->cabinet_id,
            'employee_id' => $contract->employee_id,
            'number' => $data['number'],
        ]);
    }

    public function test_cant_update_contract_in_other_cabinet(): void
    {
        $otherCabinetContract = Contract::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $data = [
            'number' => $this->faker()->word,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'type' => $this->faker()->randomElement(ContractTypeEnum::cases())->value,
            'date_from' => now()->format('Y-m-d H:i:s')
        ];

        $response = $this->putJson("/api/internal/contracts/{$otherCabinetContract->id}", $data);

        $response->assertNotFound();

        $this->assertDatabaseHas(
            'contracts',
            [
                'id' => $otherCabinetContract->id,
                'number' => $otherCabinetContract->number,
            ]
        );
    }

    public function test_can_delete_contracts(): void
    {
        $contract = Contract::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $response = $this->deleteJson("/api/internal/contracts/{$contract->id}");

        $response->assertNoContent();

        $this->assertDatabaseMissing('contracts', ['id' => $contract->id]);
    }

    public function test_cant_delete_bookmark_in_other_cabinet(): void
    {
        $contracts = Contract::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->deleteJson("/api/internal/contracts/{$contracts->id}");

        $response->assertNotFound();

        $this->assertDatabaseHas('contracts', ['id' => $contracts->id]);
    }
}
