<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\WarehouseStorageArea;
use App\Models\WarehouseCell;
use App\Models\Product;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehouseStorageAreaTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;
    private Warehouse $warehouse;
    private Warehouse $otherWarehouse;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
        ]);

        $this->otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
    }

    public function test_can_get_storage_areas_list(): void
    {
        // Arrange
        // Создаем 3 зоны хранения для нашего склада
        WarehouseStorageArea::factory()->count(3)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем зону хранения для другого склада
        WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'warehouse_id',
                        'name',
                        'description',
                        'temperature_from',
                        'temperature_to',
                        'warehouse_id',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего склада
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->warehouse->id, $item['warehouse_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_warehouse_storage_areas(): void
    {
        // Arrange
        // Создаем зоны хранения для склада другого кабинета
        WarehouseStorageArea::factory()->count(2)->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas?' . http_build_query([
            'warehouse_id' => $this->otherWarehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'warehouse_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_without_required_warehouse_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['warehouse_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_returns_empty_collection_when_no_storage_areas(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/storage-areas?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_can_create_storage_area(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Test Storage Area',
            'description' => 'Test Description',
            'temperature_from' => 15,
            'temperature_to' => 25
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_storage_areas', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'name' => $data['name'],
            'description' => $data['description'],
            'temperature_from' => $data['temperature_from'],
            'temperature_to' => $data['temperature_to']
        ]);
    }

    public function test_can_create_storage_area_with_cells(): void
    {
        // Arrange
        $cells = WarehouseCell::factory()->count(3)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Storage Area with Cells',
            'description' => 'Test Description',
            'cells_id' => $cells->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertStatus(201);

        // Проверяем связи с ячейками
        foreach ($cells as $cell) {
            $this->assertDatabaseHas('warehouse_storage_area_cells', [
                'storage_area_id' => $response->json('id'),
                'cell_id' => $cell->id
            ]);
        }
    }

    public function test_can_create_storage_area_with_products(): void
    {
        // Arrange
        $products = Product::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Storage Area with Products',
            'description' => 'Test Description',
            'products_id' => $products->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertStatus(201);

        // Проверяем связи с продуктами
        foreach ($products as $product) {
            $this->assertDatabaseHas('warehouse_storage_area_products', [
                'storage_area_id' => $response->json('id'),
                'product_id' => $product->id
            ]);
        }
    }

    public function test_cannot_create_storage_area_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'warehouse_id' => $this->otherWarehouse->id,
            'name' => 'Test Storage Area',
            'description' => 'Test Description'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertForbidden();

        $this->assertDatabaseMissing('warehouse_storage_areas', [
            'warehouse_id' => $data['warehouse_id'],
            'name' => $data['name']
        ]);
    }

    public function test_cannot_create_storage_area_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'warehouse_id' => 'not-a-uuid',
            'name' => '',
            'temperature_from' => 'not-a-number',
            'temperature_to' => 'not-a-number',
            'cells_id' => 'not-an-array',
            'products_id' => 'not-an-array'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'warehouse_id',
                'name',
                'temperature_from',
                'temperature_to',
                'cells_id',
                'products_id'
            ]);
    }

    public function test_cannot_create_storage_area_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'warehouse_id',
                'name'
            ]);
    }

    public function test_cannot_create_storage_area_with_cells_from_other_warehouse(): void
    {
        // Arrange
        $otherWarehouseCells = WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Test Storage Area',
            'cells_id' => $otherWarehouseCells->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertNotFound();

        // Проверяем что связи не были созданы
        foreach ($otherWarehouseCells as $cell) {
            $this->assertDatabaseMissing('warehouse_storage_area_cells', [
                'cell_id' => $cell->id
            ]);
        }
    }

    public function test_cannot_create_storage_area_with_products_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetProducts = Product::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Test Storage Area',
            'products_id' => $otherCabinetProducts->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertNotFound();

        // Проверяем что связи не были созданы
        foreach ($otherCabinetProducts as $product) {
            $this->assertDatabaseMissing('warehouse_storage_area_products', [
                'product_id' => $product->id
            ]);
        }
    }

    public function test_can_create_storage_area_with_minimal_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Minimal Storage Area'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_storage_areas', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'name' => $data['name'],
            'description' => null,
            'temperature_from' => null,
            'temperature_to' => null
        ]);
    }

    public function test_cannot_create_storage_area_with_invalid_temperature_range(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Test Storage Area',
            'temperature_from' => 25,
            'temperature_to' => 15 // температура "до" меньше чем "от"
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/storage-areas', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'temperature_to'
            ]);
    }

    public function test_can_update_storage_area(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'name' => 'Updated Storage Area',
            'description' => 'Updated Description',
            'temperature_from' => 20,
            'temperature_to' => 30,
            'warehouse_id' => $this->warehouse->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_storage_areas', [
            'id' => $storageArea->id,
            'name' => $data['name'],
            'description' => $data['description'],
            'temperature_from' => $data['temperature_from'],
            'temperature_to' => $data['temperature_to'],
            'warehouse_id' => $data['warehouse_id']
        ]);
    }

    public function test_can_update_storage_area_cells(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем старые ячейки
        $oldCells = WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        DB::table('warehouse_storage_area_cells')->insert(
            $oldCells->map(fn ($cell) => [
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ])->toArray()
        );

        // Создаем новые ячейки
        $newCells = WarehouseCell::factory()->count(3)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->warehouse->id,
            'cells_id' => $newCells->pluck('id')->toArray()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем что старые связи удалены
        foreach ($oldCells as $cell) {
            $this->assertDatabaseMissing('warehouse_storage_area_cells', [
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ]);
        }

        // Проверяем что новые связи созданы
        foreach ($newCells as $cell) {
            $this->assertDatabaseHas('warehouse_storage_area_cells', [
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ]);
        }
    }

    public function test_can_update_storage_area_products(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем старые продукты
        $oldProducts = Product::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        DB::table('warehouse_storage_area_products')->insert(
            $oldProducts->map(fn ($product) => [
                'storage_area_id' => $storageArea->id,
                'product_id' => $product->id
            ])->toArray()
        );

        // Создаем новые продукты
        $newProducts = Product::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->warehouse->id,
            'products_id' => $newProducts->pluck('id')->toArray()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем что старые связи удалены
        foreach ($oldProducts as $product) {
            $this->assertDatabaseMissing('warehouse_storage_area_products', [
                'storage_area_id' => $storageArea->id,
                'product_id' => $product->id
            ]);
        }

        // Проверяем что новые связи созданы
        foreach ($newProducts as $product) {
            $this->assertDatabaseHas('warehouse_storage_area_products', [
                'storage_area_id' => $storageArea->id,
                'product_id' => $product->id
            ]);
        }
    }

    public function test_cannot_update_storage_area_in_other_cabinet(): void
    {
        // Arrange
        $otherStorageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->otherWarehouse->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$otherStorageArea->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('warehouse_storage_areas', [
            'id' => $otherStorageArea->id,
            'name' => $data['name']
        ]);
    }

    public function test_cannot_update_storage_area_with_invalid_data(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'name' => '',
            'warehouse_id' => 'not-a-uuid',
            'temperature_from' => 'not-a-number',
            'temperature_to' => 'not-a-number',
            'cells_id' => 'not-an-array',
            'products_id' => 'not-an-array'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'temperature_from',
                'temperature_to',
                'cells_id',
                'products_id'
            ]);
    }

    public function test_cannot_update_storage_area_without_required_fields(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
            ]);
    }

    public function test_cannot_update_non_existent_storage_area(): void
    {
        // Arrange
        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->warehouse->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/" . $this->faker->uuid(), $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_storage_area_with_cells_from_other_warehouse(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $otherWarehouseCells = WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->warehouse->id,
            'cells_id' => $otherWarehouseCells->pluck('id')->toArray()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertNotFound();

        // Проверяем что связи не были созданы
        foreach ($otherWarehouseCells as $cell) {
            $this->assertDatabaseMissing('warehouse_storage_area_cells', [
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ]);
        }
    }

    public function test_cannot_update_storage_area_with_products_from_other_cabinet(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $otherCabinetProducts = Product::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->warehouse->id,
            'products_id' => $otherCabinetProducts->pluck('id')->toArray()
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertNotFound();

        // Проверяем что связи не были созданы
        foreach ($otherCabinetProducts as $product) {
            $this->assertDatabaseMissing('warehouse_storage_area_products', [
                'storage_area_id' => $storageArea->id,
                'product_id' => $product->id
            ]);
        }
    }

    public function test_can_clear_storage_area_relations(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем существующие связи
        $cells = WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        DB::table('warehouse_storage_area_cells')->insert(
            $cells->map(fn ($cell) => [
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ])->toArray()
        );

        $products = Product::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        DB::table('warehouse_storage_area_products')->insert(
            $products->map(fn ($product) => [
                'storage_area_id' => $storageArea->id,
                'product_id' => $product->id
            ])->toArray()
        );

        $data = [
            'name' => 'Updated Storage Area',
            'warehouse_id' => $this->warehouse->id,
            'cells_id' => [],
            'products_id' => []
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/storage-areas/{$storageArea->id}", $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем что все связи удалены
        $this->assertDatabaseCount('warehouse_storage_area_cells', 0);
        $this->assertDatabaseCount('warehouse_storage_area_products', 0);
    }

    public function test_can_show_storage_area(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'name' => 'Test Storage Area',
            'description' => 'Test Description',
            'temperature_from' => 15,
            'temperature_to' => 25
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/storage-areas/{$storageArea->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'warehouse_id',
                'name',
                'description',
                'temperature_from',
                'temperature_to',
                'warehouse_name',
                'warehouse_cells',
                'warehouse_products'
            ])
            ->assertJson([
                'id' => $storageArea->id,
                'warehouse_id' => $this->warehouse->id,
                'name' => 'Test Storage Area',
                'description' => 'Test Description',
                'temperature_from' => 15,
                'temperature_to' => 25,
                'warehouse_name' => $this->warehouse->name,
                'warehouse_cells' => [],
                'warehouse_products' => []
            ]);
    }

    public function test_cannot_show_storage_area_from_other_cabinet(): void
    {
        // Arrange
        $otherStorageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/storage-areas/{$otherStorageArea->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_storage_area(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/storage-areas/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_storage_area(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/storage-areas/{$storageArea->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_storage_areas', [
            'id' => $storageArea->id
        ]);
    }

    public function test_can_delete_storage_area_with_relations(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем связанные ячейки
        $cells = WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        DB::table('warehouse_storage_area_cells')->insert(
            $cells->map(fn ($cell) => [
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ])->toArray()
        );

        // Создаем связанные продукты
        $products = Product::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        DB::table('warehouse_storage_area_products')->insert(
            $products->map(fn ($product) => [
                'storage_area_id' => $storageArea->id,
                'product_id' => $product->id
            ])->toArray()
        );

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/storage-areas/{$storageArea->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем удаление основной записи
        $this->assertDatabaseMissing('warehouse_storage_areas', [
            'id' => $storageArea->id
        ]);

        // Проверяем удаление связей с ячейками
        $this->assertDatabaseMissing('warehouse_storage_area_cells', [
            'storage_area_id' => $storageArea->id
        ]);

        // Проверяем удаление связей с продуктами
        $this->assertDatabaseMissing('warehouse_storage_area_products', [
            'storage_area_id' => $storageArea->id
        ]);

        // Проверяем что сами ячейки и продукты остались
        foreach ($cells as $cell) {
            $this->assertDatabaseHas('warehouse_cells', [
                'id' => $cell->id
            ]);
        }

        foreach ($products as $product) {
            $this->assertDatabaseHas('products', [
                'id' => $product->id
            ]);
        }
    }

    public function test_cannot_delete_storage_area_from_other_cabinet(): void
    {
        // Arrange
        $otherStorageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/storage-areas/{$otherStorageArea->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('warehouse_storage_areas', [
            'id' => $otherStorageArea->id
        ]);
    }

    public function test_cannot_delete_non_existent_storage_area(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/storage-areas/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
