<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function test_view_profile(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create([
            'user_id' => $user->id
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/profile');

        $response->assertStatus(200);
    }

    public function test_view_profile_no_auth(): void
    {
        $response = $this->get('/api/internal/profile');

        $response->assertStatus(401);
    }
}
