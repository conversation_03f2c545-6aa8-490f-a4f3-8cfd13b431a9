<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseOrderSchemeDetectionTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $detectionService;
    protected $employee;
    protected $cabinet;
    protected $department;
    protected $orderSchemeWarehouse;
    protected $regularWarehouse;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->detectionService = $this->app->make(WarehouseOrderSchemeDetectionServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем склад с настройками ордерной схемы
        $this->orderSchemeWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'detailed_batch_tracking' => true,
            'expiry_control' => true,
            'quality_control' => true,
        ]);

        // Создаем обычный склад
        $this->regularWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'detailed_batch_tracking' => false,
            'expiry_control' => false,
            'quality_control' => false,
        ]);
    }

    public function test_can_check_order_scheme_activation_via_api(): void
    {
        // Arrange
        $params = [
            'warehouse_id' => $this->orderSchemeWarehouse->id,
            'operation_type' => 'receipts',
            'date' => '2024-01-15'
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query($params));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'is_active',
            'warehouse_id',
            'operation_type',
            'date',
            'settings' => [
                'detailed_batch_tracking',
                'expiry_control',
                'quality_control'
            ]
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['is_active']);
        $this->assertEquals($this->orderSchemeWarehouse->id, $responseData['warehouse_id']);
        $this->assertEquals('receipts', $responseData['operation_type']);
        $this->assertTrue($responseData['settings']['detailed_batch_tracking']);
        $this->assertTrue($responseData['settings']['expiry_control']);
        $this->assertTrue($responseData['settings']['quality_control']);
    }

    public function test_detects_order_scheme_for_receipts(): void
    {
        // Act
        $isActive = $this->detectionService->isOrderSchemeActiveForReceipts(
            $this->orderSchemeWarehouse->id,
            '2024-01-15'
        );

        // Assert
        $this->assertTrue($isActive);
    }

    public function test_detects_order_scheme_for_shipments(): void
    {
        // Act
        $isActive = $this->detectionService->isOrderSchemeActiveForShipments(
            $this->orderSchemeWarehouse->id,
            '2024-01-15'
        );

        // Assert
        $this->assertTrue($isActive);
    }

    public function test_detects_regular_scheme_for_receipts(): void
    {
        // Act
        $isActive = $this->detectionService->isOrderSchemeActiveForReceipts(
            $this->regularWarehouse->id,
            '2024-01-15'
        );

        // Assert
        $this->assertFalse($isActive);
    }

    public function test_detects_regular_scheme_for_shipments(): void
    {
        // Act
        $isActive = $this->detectionService->isOrderSchemeActiveForShipments(
            $this->regularWarehouse->id,
            '2024-01-15'
        );

        // Assert
        $this->assertFalse($isActive);
    }

    public function test_api_returns_false_for_regular_warehouse(): void
    {
        // Arrange
        $params = [
            'warehouse_id' => $this->regularWarehouse->id,
            'operation_type' => 'shipments',
            'date' => '2024-01-15'
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query($params));

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertFalse($responseData['is_active']);
        $this->assertFalse($responseData['settings']['detailed_batch_tracking']);
        $this->assertFalse($responseData['settings']['expiry_control']);
        $this->assertFalse($responseData['settings']['quality_control']);
    }

    public function test_handles_partial_order_scheme_settings(): void
    {
        // Arrange - создаем склад с частичными настройками ордерной схемы
        $partialWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'detailed_batch_tracking' => true,
            'expiry_control' => false, // Отключен контроль сроков
            'quality_control' => true,
        ]);

        // Act
        $isActiveForReceipts = $this->detectionService->isOrderSchemeActiveForReceipts(
            $partialWarehouse->id,
            '2024-01-15'
        );

        $isActiveForShipments = $this->detectionService->isOrderSchemeActiveForShipments(
            $partialWarehouse->id,
            '2024-01-15'
        );

        // Assert - должно быть false, так как не все настройки включены
        $this->assertFalse($isActiveForReceipts);
        $this->assertFalse($isActiveForShipments);
    }

    public function test_api_validates_required_parameters(): void
    {
        // Act - запрос без обязательных параметров
        $response = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active');

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['warehouse_id', 'operation_type']);
    }

    public function test_api_validates_operation_type(): void
    {
        // Arrange
        $params = [
            'warehouse_id' => $this->orderSchemeWarehouse->id,
            'operation_type' => 'invalid_type',
            'date' => '2024-01-15'
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query($params));

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['operation_type']);
    }

    public function test_api_validates_warehouse_exists(): void
    {
        // Arrange
        $params = [
            'warehouse_id' => 'non-existent-uuid',
            'operation_type' => 'receipts',
            'date' => '2024-01-15'
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query($params));

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['warehouse_id']);
    }

    public function test_api_uses_current_date_when_not_provided(): void
    {
        // Arrange
        $params = [
            'warehouse_id' => $this->orderSchemeWarehouse->id,
            'operation_type' => 'receipts',
            // date не указана
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query($params));

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertTrue($responseData['is_active']);
        $this->assertEquals(now()->toDateString(), $responseData['date']);
    }

    public function test_service_handles_non_existent_warehouse(): void
    {
        // Act & Assert
        $this->expectException(\Exception::class);
        
        $this->detectionService->isOrderSchemeActiveForReceipts(
            'non-existent-uuid',
            '2024-01-15'
        );
    }

    public function test_different_operation_types_return_same_result(): void
    {
        // Act
        $receiptsActive = $this->detectionService->isOrderSchemeActiveForReceipts(
            $this->orderSchemeWarehouse->id,
            '2024-01-15'
        );

        $shipmentsActive = $this->detectionService->isOrderSchemeActiveForShipments(
            $this->orderSchemeWarehouse->id,
            '2024-01-15'
        );

        // Assert - для одного склада результат должен быть одинаковым
        $this->assertEquals($receiptsActive, $shipmentsActive);
        $this->assertTrue($receiptsActive);
        $this->assertTrue($shipmentsActive);
    }

    public function test_api_returns_consistent_results_for_different_operation_types(): void
    {
        // Arrange & Act
        $receiptsResponse = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query([
            'warehouse_id' => $this->orderSchemeWarehouse->id,
            'operation_type' => 'receipts',
            'date' => '2024-01-15'
        ]));

        $shipmentsResponse = $this->getJson('/api/internal/warehouse-order-scheme/detection/is-active?' . http_build_query([
            'warehouse_id' => $this->orderSchemeWarehouse->id,
            'operation_type' => 'shipments',
            'date' => '2024-01-15'
        ]));

        // Assert
        $receiptsResponse->assertStatus(200);
        $shipmentsResponse->assertStatus(200);

        $receiptsData = $receiptsResponse->json();
        $shipmentsData = $shipmentsResponse->json();

        $this->assertEquals($receiptsData['is_active'], $shipmentsData['is_active']);
        $this->assertTrue($receiptsData['is_active']);
        $this->assertTrue($shipmentsData['is_active']);
    }
}
