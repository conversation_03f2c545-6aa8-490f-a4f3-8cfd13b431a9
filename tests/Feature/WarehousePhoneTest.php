<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\WarehousePhone;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehousePhoneTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_warehouse_phones_list(): void
    {
        // Arrange
        // Создаем 3 телефона для нашего кабинета
        WarehousePhone::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем телефон для другого кабинета
        WarehousePhone::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'phone',
                        'comment'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_warehouse_phones(): void
    {
        // Arrange
        // Создаем телефоны для другого кабинета
        WarehousePhone::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertForbidden();
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/phones');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_returns_empty_collection_when_no_phones(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_index_with_selected_fields(): void
    {
        // Arrange
        WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'phone']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'phone'
                    ]
                ]
            ]);

        // Проверяем что в ответе только запрошенные поля
        $responseData = $response->json('data.0');
        $this->assertCount(2, array_keys($responseData));
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        // Создаем телефоны с разными номерами
        $phone1 = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '1111111111'
        ]);
        $phone2 = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '2222222222'
        ]);
        $phone3 = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '3333333333'
        ]);

        // Act - получаем отсортированный по номеру телефона список
        $response = $this->getJson('/api/internal/warehouses/phones?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'phone',
            'sortDirection' => 'desc',
            'fields' => ['id', 'phone']
        ]));

        // Assert
        $response->assertStatus(200);

        $phones = collect($response->json('data'))->pluck('phone')->values();
        $expectedPhones = collect([$phone3->phone, $phone2->phone, $phone1->phone]);

        $this->assertEquals($expectedPhones, $phones);
    }

    public function test_can_create_warehouse_phone(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'phone' => $this->faker->e164PhoneNumber,
            'comment' => $this->faker->text(100)
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/phones', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'phone' => $data['phone'],
            'comment' => $data['comment']
        ]);
    }

    public function test_can_create_warehouse_phone_with_minimal_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'phone' => $this->faker->e164PhoneNumber
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/phones', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'phone' => $data['phone'],
            'comment' => null
        ]);
    }

    public function test_cannot_create_warehouse_phone_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses/phones', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'phone'
            ]);
    }

    public function test_cannot_create_warehouse_phone_with_invalid_data(): void
    {
        // Arrange
        $invalidData = [
            'cabinet_id' => 'not-a-uuid',
            'phone' => 'invalid-phone-number',
            'comment' => ['invalid-type']
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/phones', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'phone',
                'comment'
            ]);
    }

    public function test_cannot_create_warehouse_phone_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'phone' => $this->faker->e164PhoneNumber
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/phones', $data);

        // Assert
        $response->assertForbidden();

        $this->assertDatabaseMissing('warehouse_phones', [
            'cabinet_id' => $this->otherCabinet->id,
            'phone' => $data['phone']
        ]);
    }

    public function test_can_create_same_phone_in_different_cabinets(): void
    {
        // Arrange
        $phone = $this->faker->e164PhoneNumber;

        // Создаем телефон в другом кабинете
        WarehousePhone::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'phone' => $phone
        ]);

        // Пытаемся создать такой же номер в своем кабинете
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'phone' => $phone
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/phones', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        // Проверяем что в базе две записи с этим номером
        $this->assertEquals(2, WarehousePhone::where('phone', $phone)->count());
    }

    public function test_cannot_create_warehouse_phone_with_invalid_phone_formats(): void
    {
        $invalidPhones = [
            '12345678901234567890', // слишком длинный
            'abc-def-ghij', // буквы вместо цифр
            '+++123456789', // неверный формат
            '(123)456789', // без пробелов
        ];

        foreach ($invalidPhones as $phone) {
            $data = [
                'cabinet_id' => $this->cabinet->id,
                'phone' => $phone
            ];

            $response = $this->postJson('/api/internal/warehouses/phones', $data);

            $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone']);
        }
    }

    public function test_can_update_warehouse_phone(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => $this->faker->e164PhoneNumber,
            'comment' => 'Old comment'
        ]);

        $updateData = [
            'phone' => $this->faker->e164PhoneNumber,
            'comment' => 'New comment'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/phones/{$phone->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $phone->id,
            'cabinet_id' => $this->cabinet->id,
            'phone' => $updateData['phone'],
            'comment' => $updateData['comment']
        ]);
    }

    public function test_can_update_warehouse_phone_partial(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => $this->faker->e164PhoneNumber,
            'comment' => 'Old comment'
        ]);

        $updateData = [
            'phone' => $this->faker->e164PhoneNumber
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/phones/{$phone->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $phone->id,
            'cabinet_id' => $this->cabinet->id,
            'phone' => $updateData['phone'],
        ]);
    }

    public function test_cannot_update_warehouse_phone_without_required_fields(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/warehouses/phones/{$phone->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone']);
    }

    public function test_cannot_update_warehouse_phone_with_invalid_data(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'phone' => 'invalid-phone-number',
            'comment' => ['invalid-type']
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/phones/{$phone->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'phone',
                'comment'
            ]);

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $phone->id,
            'phone' => $phone->phone,
            'comment' => $phone->comment
        ]);
    }

    public function test_cannot_update_non_existent_warehouse_phone(): void
    {
        // Act
        $response = $this->putJson("/api/internal/warehouses/phones/" . $this->faker->uuid(), [
            'phone' => $this->faker->e164PhoneNumber
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_warehouse_phone_from_other_cabinet(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'phone' => $this->faker->e164PhoneNumber
        ]);

        $updateData = [
            'phone' => $this->faker->e164PhoneNumber
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/phones/{$phone->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $phone->id,
            'phone' => $phone->phone
        ]);
    }

    public function test_cannot_update_warehouse_phone_with_invalid_phone_formats(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidPhones = [
            '12345678901234567890', // слишком длинный
            'abc-def-ghij', // буквы вместо цифр
            '+++123456789', // неверный формат
            '(123)456789', // без пробелов
        ];

        foreach ($invalidPhones as $invalidPhone) {
            $updateData = [
                'phone' => $invalidPhone
            ];

            // Act
            $response = $this->putJson("/api/internal/warehouses/phones/{$phone->id}", $updateData);

            // Assert
            $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone']);

            // Проверяем что данные не изменились
            $this->assertDatabaseHas('warehouse_phones', [
                'id' => $phone->id,
                'phone' => $phone->phone
            ]);
        }
    }

    public function test_can_show_warehouse_phone(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => $this->faker->e164PhoneNumber,
            'comment' => 'Test comment'
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/phones/{$phone->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'phone',
                'comment'
            ])
            ->assertJson([
                'id' => $phone->id,
                'cabinet_id' => $this->cabinet->id,
                'phone' => $phone->phone,
                'comment' => $phone->comment
            ]);
    }

    public function test_cannot_show_non_existent_warehouse_phone(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/phones/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_warehouse_phone_from_other_cabinet(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/phones/{$phone->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_warehouse_phone(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/phones/{$phone->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_phones', [
            'id' => $phone->id
        ]);
    }

    public function test_cannot_delete_non_existent_warehouse_phone(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/phones/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_delete_warehouse_phone_from_other_cabinet(): void
    {
        // Arrange
        $phone = WarehousePhone::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/phones/{$phone->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись осталась в базе
        $this->assertDatabaseHas('warehouse_phones', [
            'id' => $phone->id
        ]);
    }
}
