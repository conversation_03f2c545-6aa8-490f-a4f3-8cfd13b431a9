<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\ProfitTaxRate;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProfitTaxRateTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Employee $employee;
    private Department $department;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем другой кабинет для проверки мультитенантности
        $this->otherCabinet = Cabinet::factory()->create();
    }

    public function test_can_get_profit_tax_rates_list(): void
    {
        // Arrange
        ProfitTaxRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/tax_rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'rate',
                        'description',
                        'is_default'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_can_get_empty_profit_tax_rates_list(): void
    {
        // Act
        $response = $this->getJson('/api/internal/tax_rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEmpty($response->json('data'));
        $this->assertEquals(0, $response->json('meta.total'));
    }

    public function test_cannot_get_profit_tax_rates_list_with_invalid_data(): void
    {
        // Act
        $response = $this->getJson('/api/internal/tax_rates?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_can_get_profit_tax_rates_list_with_selected_fields(): void
    {
        // Arrange
        ProfitTaxRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/tax_rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id','rate']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'rate'
                    ]
                ],
                'meta'
            ]);

        $this->assertCount(3, $response->json('data'));
        $this->assertArrayNotHasKey('created_at', $response->json('data.0'));
        $this->assertArrayNotHasKey('updated_at', $response->json('data.0'));
    }

    public function test_cannot_get_profit_tax_rates_list_from_other_cabinet(): void
    {
        // Arrange
        ProfitTaxRate::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/tax_rates?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_cannot_get_profit_tax_rates_list_for_non_existent_cabinet(): void
    {
        // Act
        $response = $this->getJson('/api/internal/tax_rates?' . http_build_query([
            'cabinet_id' => $this->faker->uuid()
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_create_profit_tax_rate(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 20,
            'description' => 'Test rate'
        ];

        // Act
        $response = $this->postJson('/api/internal/tax_rates', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('profit_tax_rates', [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 20,
            'description' => 'Test rate'
        ]);
    }

    public function test_cannot_create_profit_tax_rate_with_invalid_data(): void
    {
        // Act
        $response = $this->postJson('/api/internal/tax_rates', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'employee_id',
                'department_id',
                'rate'
            ]);
    }

    public function test_cannot_create_profit_tax_rate_with_invalid_rate(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 101,
            'description' => 'Test rate'
        ];

        // Act
        $response = $this->postJson('/api/internal/tax_rates', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['rate']);
    }

    public function test_cannot_create_profit_tax_rate_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 20,
            'description' => 'Test rate'
        ];

        // Act
        $response = $this->postJson('/api/internal/tax_rates', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_create_profit_tax_rate_without_description(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 20
        ];

        // Act
        $response = $this->postJson('/api/internal/tax_rates', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('profit_tax_rates', [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 20,
            'description' => null
        ]);
    }

    public function test_can_update_profit_tax_rate(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'rate' => 25,
            'description' => 'Updated rate',
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ];

        // Act
        $response = $this->putJson("/api/internal/tax_rates/{$profitTaxRate->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('profit_tax_rates', [
            'id' => $profitTaxRate->id,
            'rate' => 25,
            'description' => 'Updated rate',
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ]);
    }

    public function test_cannot_update_profit_tax_rate_with_invalid_data(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/tax_rates/{$profitTaxRate->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'rate',
                'department_id',
                'employee_id'
            ]);
    }

    public function test_cannot_update_profit_tax_rate_with_invalid_rate(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'rate' => 101,
            'description' => 'Updated rate',
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ];

        // Act
        $response = $this->putJson("/api/internal/tax_rates/{$profitTaxRate->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['rate']);
    }

    public function test_cannot_update_profit_tax_rate_from_other_cabinet(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'rate' => 25,
            'description' => 'Updated rate',
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ];

        // Act
        $response = $this->putJson("/api/internal/tax_rates/{$profitTaxRate->id}", $updateData);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('profit_tax_rates', [
            'id' => $profitTaxRate->id,
            'rate' => $profitTaxRate->rate,
            'description' => $profitTaxRate->description
        ]);
    }

    public function test_cannot_update_non_existent_profit_tax_rate(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid();
        $updateData = [
            'rate' => 25,
            'description' => 'Updated rate',
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ];

        // Act
        $response = $this->putJson("/api/internal/tax_rates/{$nonExistentId}", $updateData);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_profit_tax_rate_without_description(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'description' => 'Old description'
        ]);

        $updateData = [
            'rate' => 25,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ];

        // Act
        $response = $this->putJson("/api/internal/tax_rates/{$profitTaxRate->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('profit_tax_rates', [
            'id' => $profitTaxRate->id,
            'rate' => 25,
            'description' => null
        ]);
    }

    public function test_can_show_profit_tax_rate(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'rate' => 20,
            'description' => 'Test rate'
        ]);

        // Act
        $response = $this->getJson("/api/internal/tax_rates/{$profitTaxRate->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'employee_id',
                'department_id',
                'rate',
                'description',
                'is_default'
            ])
            ->assertJson([
                'id' => $profitTaxRate->id,
                'cabinet_id' => $this->cabinet->id,
                'employee_id' => $this->employee->id,
                'department_id' => $this->department->id,
                'rate' => 20,
                'description' => 'Test rate'
            ]);
    }

    public function test_cannot_show_profit_tax_rate_from_other_cabinet(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/tax_rates/{$profitTaxRate->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_profit_tax_rate(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid();

        // Act
        $response = $this->getJson("/api/internal/tax_rates/{$nonExistentId}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_profit_tax_rate(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/tax_rates/{$profitTaxRate->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('profit_tax_rates', [
            'id' => $profitTaxRate->id
        ]);
    }

    public function test_cannot_delete_profit_tax_rate_from_other_cabinet(): void
    {
        // Arrange
        $profitTaxRate = ProfitTaxRate::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/tax_rates/{$profitTaxRate->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('profit_tax_rates', [
            'id' => $profitTaxRate->id
        ]);
    }

    public function test_cannot_delete_non_existent_profit_tax_rate(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid();

        // Act
        $response = $this->deleteJson("/api/internal/tax_rates/{$nonExistentId}");

        // Assert
        $response->assertStatus(404);
    }
}
