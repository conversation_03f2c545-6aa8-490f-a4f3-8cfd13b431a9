<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\WarehouseOrderSchemeValidationServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use App\Models\WarehouseOrderScheme;
use App\Models\WarehouseReservation;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseOrderSchemeValidationTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $validationService;
    protected $employee;
    protected $cabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $warehouseItem;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->validationService = $this->app->make(WarehouseOrderSchemeValidationServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'available_quantity' => 80,
            'reserved_quantity' => 20,
            'quality_status' => 'good',
        ]);
    }

    public function test_can_validate_shipment_via_api(): void
    {
        // Arrange - создаем ордерную схему отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        // Создаем резерв
        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 15,
            'status' => 'reserved',
        ]);

        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 10
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'valid',
            'errors',
            'warnings'
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['valid']);
        $this->assertEmpty($responseData['errors']);
    }

    public function test_validates_insufficient_reservation_in_order_scheme(): void
    {
        // Arrange - создаем ордерную схему отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        // Создаем недостаточный резерв
        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 5,
            'status' => 'reserved',
        ]);

        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 10 // Больше чем зарезервировано
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertFalse($responseData['valid']);
        $this->assertNotEmpty($responseData['errors']);
        
        $error = $responseData['errors'][0];
        $this->assertEquals('insufficient_reservation', $error['type']);
        $this->assertEquals($this->product->id, $error['product_id']);
        $this->assertEquals(10, $error['required']);
        $this->assertEquals(5, $error['available']);
    }

    public function test_validates_expiry_warnings(): void
    {
        // Arrange - создаем ордерную схему отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        // Создаем товар с истекающим сроком годности
        $expiringItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'available_quantity' => 50,
            'expiry_date' => now()->addDays(3), // Истекает через 3 дня
            'quality_status' => 'good',
        ]);

        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $expiringItem->id,
            'reserved_quantity' => 10,
            'status' => 'reserved',
        ]);

        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 10
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertTrue($responseData['valid']);
        $this->assertNotEmpty($responseData['warnings']);
        
        $warning = $responseData['warnings'][0];
        $this->assertEquals('expiry_warning', $warning['type']);
        $this->assertEquals($this->product->id, $warning['product_id']);
    }

    public function test_allows_shipment_in_regular_scheme(): void
    {
        // Arrange - склад без ордерной схемы отгрузок
        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 10
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertTrue($responseData['valid']);
        $this->assertEmpty($responseData['errors']);
    }

    public function test_validates_defective_items(): void
    {
        // Arrange - создаем ордерную схему отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        // Создаем бракованный товар
        $defectiveItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 30,
            'available_quantity' => 30,
            'quality_status' => 'defective',
        ]);

        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 5
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        
        // Должно быть предупреждение о бракованных товарах
        $this->assertNotEmpty($responseData['warnings']);
        $warning = collect($responseData['warnings'])->firstWhere('type', 'quality_warning');
        $this->assertNotNull($warning);
    }

    public function test_validates_multiple_products(): void
    {
        // Arrange
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $warehouseItem2 = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $product2->id,
            'quantity' => 50,
            'available_quantity' => 50,
            'quality_status' => 'good',
        ]);

        // Создаем резервы
        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 15,
            'status' => 'reserved',
        ]);

        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $warehouseItem2->id,
            'reserved_quantity' => 25,
            'status' => 'reserved',
        ]);

        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 10
                ],
                [
                    'product_id' => $product2->id,
                    'quantity' => 20
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertTrue($responseData['valid']);
        $this->assertEmpty($responseData['errors']);
    }

    public function test_validates_required_fields(): void
    {
        // Act - запрос без обязательных полей
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', []);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['warehouse_id', 'items']);
    }

    public function test_validates_warehouse_exists(): void
    {
        // Arrange
        $shipmentData = [
            'warehouse_id' => 'non-existent-uuid',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 10
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['warehouse_id']);
    }

    public function test_validates_items_structure(): void
    {
        // Arrange
        $shipmentData = [
            'warehouse_id' => $this->warehouse->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    // quantity отсутствует
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/validation/shipment', $shipmentData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quantity']);
    }
}
