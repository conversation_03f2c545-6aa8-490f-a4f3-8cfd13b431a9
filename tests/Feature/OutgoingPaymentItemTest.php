<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Document;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\OutgoingPayment;
use App\Models\OutgoingPaymentItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class OutgoingPaymentItemTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Department $department;
    protected Employee $employee;
    protected OutgoingPayment $outgoingPayment;
    protected OutgoingPayment $otherCabinetOutgoingPayment;
    protected Document $document;
    protected LegalEntity $legalEntity;
    protected Contractor $contractor;
    protected CabinetCurrency $currency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $this->outgoingPayment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id
        ]);

        $this->document = Document::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'documentable_type' => 'acceptances'
        ]);

        $this->otherCabinetOutgoingPayment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);
    }

    public function test_can_get_outgoing_payment_items_list(): void
    {
        // Arrange
        OutgoingPaymentItem::factory()->count(3)->create([
            'outgoing_payment_id' => $this->outgoingPayment->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/items?outgoing_payment_id={$this->outgoingPayment->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');
    }

    public function test_cannot_get_outgoing_payment_items_without_outgoing_payment_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/outgoing-payments/items');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['outgoing_payment_id']);
    }

    public function test_cannot_get_outgoing_payment_items_with_invalid_outgoing_payment_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/outgoing-payments/items?outgoing_payment_id=invalid-uuid');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['outgoing_payment_id']);
    }

    public function test_cannot_get_outgoing_payment_items_from_other_cabinet(): void
    {
        // Arrange
        OutgoingPaymentItem::factory()->count(3)->create([
            'outgoing_payment_id' => $this->otherCabinetOutgoingPayment->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/items?outgoing_payment_id={$this->otherCabinetOutgoingPayment->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_create_outgoing_payment_item_with_minimal_required_fields(): void
    {
        // Arrange
        $data = [
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('outgoing_payment_items', [
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);
    }

    public function test_can_create_outgoing_payment_item_with_all_fields(): void
    {
        // Arrange
        $data = [
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('outgoing_payment_items', [
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50,
        ]);
    }

    public function test_cannot_create_outgoing_payment_item_without_required_fields(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments/items', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'outgoing_payment_id',
                'document_id',
            ]);
    }

    public function test_cannot_create_outgoing_payment_item_with_invalid_paid_in(): void
    {
        // Arrange
        $data = [
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 'invalid'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments/items', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['paid_in']);
    }

    public function test_cannot_create_outgoing_payment_item_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'outgoing_payment_id' => $this->otherCabinetOutgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments/items', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_create_outgoing_payment_item_with_document_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetDocument = Document::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'documentable_type' => 'acceptances'
        ]);

        $data = [
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $otherCabinetDocument->documentable_id,
            'paid_in' => 100.50
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments/items', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_outgoing_payment_item_with_minimal_fields(): void
    {
        // Arrange
        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        $data = [
            'paid_in' => 200.75
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/items/{$item->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('outgoing_payment_items', [
            'id' => $item->id,
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 200.75
        ]);
    }

    public function test_cannot_update_outgoing_payment_item_with_invalid_paid_in(): void
    {
        // Arrange
        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        $data = [
            'paid_in' => 'invalid'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/items/{$item->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['paid_in']);
    }

    public function test_cannot_update_outgoing_payment_item_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetPayment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $otherCabinetPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        $data = [
            'paid_in' => 200.75
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/items/{$item->id}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_non_existent_outgoing_payment_item(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid;
        $data = [
            'paid_in' => 200.75
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/items/{$nonExistentId}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_outgoing_payment_item(): void
    {
        // Arrange
        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'outgoing_payment_id',
                'document_id',
                'paid_in',
            ])
            ->assertJson([
                'id' => $item->id,
                'outgoing_payment_id' => $this->outgoingPayment->id,
                'document_id' => $this->document->documentable_id,
                'paid_in' => 100.50
            ]);
    }

    public function test_cannot_show_outgoing_payment_item_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetPayment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $otherCabinetPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_outgoing_payment_item(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid;

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/items/{$nonExistentId}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_outgoing_payment_item(): void
    {
        // Arrange
        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $this->outgoingPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/outgoing-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('outgoing_payment_items', [
            'id' => $item->id
        ]);
    }

    public function test_cannot_delete_outgoing_payment_item_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetPayment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $item = OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $otherCabinetPayment->id,
            'document_id' => $this->document->documentable_id,
            'paid_in' => 100.50
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/outgoing-payments/items/{$item->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('outgoing_payment_items', [
            'id' => $item->id
        ]);
    }

    public function test_cannot_delete_non_existent_outgoing_payment_item(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid;

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/outgoing-payments/items/{$nonExistentId}");

        // Assert
        $response->assertStatus(404);
    }
}
