<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RelatedDocumentTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Employee $employee;
    private Department $department;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->otherCabinet = Cabinet::factory()->create();

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_related_documents_tree(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();

        // Создаем тестовые данные в таблице documents
        $this->insertTestDocuments($documentId);

        // Act
        $response = $this->getJson('/api/internal/related_documents?' . http_build_query([
            'document_id' => $documentId
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'documentable_id',
                        'documentable_type',
                        'tree_id',
                        'lft',
                        'rgt',
                        'parent_id',
                        'children' => [
                            '*' => [
                                'documentable_id',
                                'documentable_type',
                                'tree_id',
                                'lft',
                                'rgt',
                                'parent_id',
                                'children' => [
                                    '*' => [
                                        'documentable_id',
                                        'documentable_type',
                                        'tree_id',
                                        'lft',
                                        'rgt',
                                        'parent_id'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]);

        $data = $response->json('data');
        $this->assertCount(2, $data); // Два корневых документа
        $this->assertCount(2, $data[0]['children']); // У первого корневого документа два потомка
        $this->assertCount(1, $data[1]['children']); // У второго корневого документа один потомок
    }

    public function test_can_get_empty_related_documents_tree(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();

        // Act
        $response = $this->getJson('/api/internal/related_documents?' . http_build_query([
            'document_id' => $documentId
        ]));

        // Assert
        $response->assertStatus(404)
            ->assertJson([]);
    }

    public function test_cannot_get_related_documents_without_document_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/related_documents');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['document_id']);
    }

    public function test_cannot_get_related_documents_with_invalid_document_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/related_documents?' . http_build_query([
            'document_id' => 'invalid-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['document_id']);
    }

    public function test_can_show_related_document(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $this->insertTestDocuments($documentId);

        // Act
        $response = $this->getJson("/api/internal/related_documents/{$documentId}");

        // Assert
        $response->assertStatus(200);
    }

    public function test_cannot_show_non_existent_related_document(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid();

        // Act
        $response = $this->getJson("/api/internal/related_documents/{$nonExistentId}");

        // Assert
        $response->assertStatus(404)
            ->assertJson([]);
    }

    public function test_can_show_related_document_without_children(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем документ без потомков
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'App\Models\CustomerOrder',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->getJson("/api/internal/related_documents/{$documentId}");

        // Assert
        $response->assertStatus(200);
    }

    public function test_can_store_related_document(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем родительский документ (CustomerOrder)
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'acceptances',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Создаем дочерний документ (VendorOrder)
        \DB::table('documents')->insert([
            'documentable_id' => $bindedDocumentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $this->faker->uuid(),
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(201)
            ->assertJson(['id' => 'success']);

        // Проверяем, что связь создана
        $this->assertDatabaseHas('documents', [
            'documentable_id' => $bindedDocumentId,
            'parent_id' => $documentId
        ]);
    }

    public function test_cannot_store_related_document_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/related_documents', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['document_id', 'binded_document_id']);
    }

    public function test_cannot_store_related_document_with_invalid_uuids(): void
    {
        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => 'invalid-uuid',
            'binded_document_id' => 'invalid-uuid'
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['document_id', 'binded_document_id']);
    }

    public function test_cannot_store_related_document_with_non_existent_documents(): void
    {
        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $this->faker->uuid(),
            'binded_document_id' => $this->faker->uuid()
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_store_related_document_with_existing_relation(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем родительский документ
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 4,
            'parent_id' => null
        ]);

        // Создаем дочерний документ с существующей связью
        \DB::table('documents')->insert([
            'documentable_id' => $bindedDocumentId,
            'documentable_type' => 'vendor_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 2,
            'rgt' => 3,
            'parent_id' => $documentId
        ]);

        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(500)
            ->assertJson(['error' => 'An error occurred while processing your request. Documents already related']);
    }

    public function test_cannot_store_related_document_with_incompatible_types(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем документы несовместимых типов
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'incomin_payments',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        \DB::table('documents')->insert([
            'documentable_id' => $bindedDocumentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $this->faker->uuid(),
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(500)
            ->assertJson(['error' => 'An error occurred while processing your request. Can relate document incomin_payments with customer_orders']);
    }

    public function test_can_store_related_document_with_automatic_parent_determination(): void
    {
        // Arrange
        $customerOrderId = $this->faker->uuid();
        $vendorOrderId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем CustomerOrder
        \DB::table('documents')->insert([
            'documentable_id' => $customerOrderId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Создаем VendorOrder
        \DB::table('documents')->insert([
            'documentable_id' => $vendorOrderId,
            'documentable_type' => 'vendor_orders',
            'tree_id' => $this->faker->uuid(),
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act - пытаемся связать в обратном порядке
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $vendorOrderId,
            'binded_document_id' => $customerOrderId
        ]);

        // Assert
        $response->assertStatus(201);

        // Проверяем, что CustomerOrder стал родителем
        $this->assertDatabaseHas('documents', [
            'documentable_id' => $vendorOrderId,
            'parent_id' => $customerOrderId
        ]);
    }

    public function test_cannot_store_related_document_from_other_cabinet(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем документы в другом кабинете
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->otherCabinet->id, // Другой cabinet_id
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        \DB::table('documents')->insert([
            'documentable_id' => $bindedDocumentId,
            'documentable_type' => 'vendor_orders',
            'tree_id' => $this->faker->uuid(),
            'cabinet_id' => $this->otherCabinet->id, // Другой cabinet_id
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_store_related_document_with_documents_from_different_cabinets(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем первый документ в нашем кабинете
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Создаем второй документ в другом кабинете
        \DB::table('documents')->insert([
            'documentable_id' => $bindedDocumentId,
            'documentable_type' => 'vendor_orders',
            'tree_id' => $this->faker->uuid(),
            'cabinet_id' => $this->otherCabinet->id, // Другой cabinet_id
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_store_related_document_with_non_existent_document_id(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем только один документ
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act - пытаемся связать с несуществующим документом
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_store_related_document_with_non_existent_binded_document_id(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $bindedDocumentId = $this->faker->uuid();
        $treeId = $this->faker->uuid();

        // Создаем только один документ
        \DB::table('documents')->insert([
            'documentable_id' => $bindedDocumentId,
            'documentable_type' => 'vendor_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act - пытаемся связать с несуществующим документом
        $response = $this->postJson('/api/internal/related_documents', [
            'document_id' => $documentId,
            'binded_document_id' => $bindedDocumentId
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_get_related_documents_from_other_cabinet(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $otherCabinetId = $this->otherCabinet->id;
        $treeId = $this->faker->uuid();

        // Создаем документ в другом кабинете
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $otherCabinetId,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/related_documents?' . http_build_query([
            'document_id' => $documentId
        ]));

        // Assert
        $response->assertStatus(404)
            ->assertJson([]);
    }

    public function test_cannot_show_related_document_from_other_cabinet(): void
    {
        // Arrange
        $documentId = $this->faker->uuid();
        $otherCabinetId = $this->otherCabinet->id;
        $treeId = $this->faker->uuid();

        // Создаем документ в другом кабинете
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'customer_orders',
            'tree_id' => $treeId,
            'cabinet_id' => $otherCabinetId,
            'lft' => 1,
            'rgt' => 2,
            'parent_id' => null
        ]);

        // Act
        $response = $this->getJson("/api/internal/related_documents/{$documentId}");

        // Assert
        $response->assertStatus(404)
            ->assertJson([]);
    }

    private function insertTestDocuments(string $documentId): void
    {
        // Создаем тестовое дерево документов
        $treeId = $this->faker->uuid();

        // Корневой документ
        \DB::table('documents')->insert([
            'documentable_id' => $documentId,
            'documentable_type' => 'App\Models\CustomerOrder',
            'tree_id' => $treeId,
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1,
            'rgt' => 8,
            'parent_id' => null
        ]);

        // Первый потомок корневого документа
        $firstChildId = $this->faker->uuid();
        \DB::table('documents')->insert([
            'documentable_id' => $firstChildId,
            'documentable_type' => 'App\Models\VendorOrder',
            'tree_id' => $treeId,
            'lft' => 2,
            'rgt' => 5,
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $documentId
        ]);

        // Второй потомок корневого документа
        $secondChildId = $this->faker->uuid();
        \DB::table('documents')->insert([
            'documentable_id' => $secondChildId,
            'documentable_type' => 'App\Models\Acceptance',
            'tree_id' => $treeId,
            'lft' => 6,
            'rgt' => 7,
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $documentId
        ]);

        // Потомок первого потомка
        \DB::table('documents')->insert([
            'documentable_id' => $this->faker->uuid(),
            'documentable_type' => 'App\Models\VendorOrder',
            'tree_id' => $treeId,
            'lft' => 3,
            'rgt' => 4,
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $firstChildId
        ]);

        // Второй корневой документ
        $secondRootId = $this->faker->uuid();
        \DB::table('documents')->insert([
            'documentable_id' => $secondRootId,
            'documentable_type' => 'App\Models\CustomerOrder',
            'tree_id' => $treeId,
            'lft' => 9,
            'rgt' => 10,
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => null
        ]);

        // Потомок второго корневого документа
        \DB::table('documents')->insert([
            'documentable_id' => $this->faker->uuid(),
            'documentable_type' => 'App\Models\VendorOrder',
            'tree_id' => $treeId,
            'lft' => 10,
            'rgt' => 11,
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $secondRootId
        ]);
    }
}
