<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Permission;
use App\Models\Department;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Enums\Api\Internal\PermissionScopeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DepartmentPermissionTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;
    private Department $otherCabinetDepartment;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем пользователя и аутентифицируем его
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        // Создаем основной кабинет
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем другой кабинет для тестов мультитенантности
        $this->otherCabinet = Cabinet::factory()->create();

        // Создаем сотрудника
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем департамент в текущем кабинете
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем департамент в другом кабинете
        $this->otherCabinetDepartment = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
    }

    public function test_can_store_department_permissions(): void
    {
        // Создаем тестовые разрешения
        $permissions = Permission::factory()->count(3)->create();

        $data = [
            'department_id' => $this->department->id,
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ];
            })->toArray()
        ];

        $response = $this->postJson('/api/internal/departments/permissions', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        // Проверяем что разрешения сохранились в БД
        foreach ($permissions as $permission) {
            $this->assertDatabaseHas('department_permissions', [
                'department_id' => $this->department->id,
                'permission_id' => $permission->id,
            ]);
        }
    }

    public function test_cannot_store_permissions_for_department_from_other_cabinet(): void
    {
        $permissions = Permission::factory()->count(2)->create();

        $data = [
            'department_id' => $this->otherCabinetDepartment->id,
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ];
            })->toArray()
        ];

        $response = $this->postJson('/api/internal/departments/permissions', $data);

        $response->assertNotFound();

        // Проверяем что разрешения не сохранились
        foreach ($permissions as $permission) {
            $this->assertDatabaseMissing('department_permissions', [
                'department_id' => $this->otherCabinetDepartment->id,
                'permission_id' => $permission->id
            ]);
        }
    }

    public function test_cannot_store_department_permissions_with_invalid_data(): void
    {
        $data = [
            'department_id' => 'invalid-uuid',
            'permissions' => [
                [
                    'id' => 'invalid-uuid',
                    'scope' => 'invalid-scope'
                ]
            ]
        ];

        $response = $this->postJson('/api/internal/departments/permissions', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['department_id', 'permissions.0.id', 'permissions.0.scope']);
    }

    public function test_cannot_store_department_permissions_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/departments/permissions', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['department_id']);
    }

    public function test_can_show_department_permissions(): void
    {
        // Создаем тестовые разрешения и привязываем их к департаменту
        $permissions = Permission::factory()->count(3)->create();

        foreach ($permissions as $permission) {
            DB::table('department_permissions')->insert([
                'id' => Str::uuid(),
                'department_id' => $this->department->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        $response = $this->getJson("/api/internal/departments/{$this->department->id}/permissions/");

        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                'id',
                'department_id',
                'permissions' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'require_scope',
                        'guard_name',
                        'operation',
                        'group_id',
                        'category_id',
                    ]
                ]
                    ]
            ]);
    }

    public function test_cannot_show_permissions_for_department_from_other_cabinet(): void
    {
        $response = $this->getJson("/api/internal/departments/permissions/{$this->otherCabinetDepartment->id}");

        $response->assertStatus(404);
    }

    public function test_cannot_show_permissions_for_non_existent_department(): void
    {
        $response = $this->getJson('/api/internal/departments/permissions/' . Str::uuid());

        $response->assertStatus(404);
    }

    public function test_can_update_department_permissions(): void
    {
        // Создаем начальные разрешения
        foreach (Permission::factory()->count(2)->create() as $permission) {
            DB::table('department_permissions')->insert([
                'id' => Str::uuid(),
                'department_id' => $this->department->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        // Создаем новые разрешения для обновления
        $newPermissions = Permission::factory()->count(2)->create();

        $data = [
            'permissions' => $newPermissions->map(function ($permission) {
                return [
                    'permission_id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_OWN->value
                ];
            })->toArray()
        ];

        $response = $this->putJson("/api/internal/departments/{$this->department->id}/permissions", $data);

        $response->assertStatus(204);

        // Проверяем что новые разрешения сохранены
        foreach ($newPermissions as $permission) {
            $this->assertDatabaseHas('department_permissions', [
                'department_id' => $this->department->id,
                'permission_id' => $permission->id,
            ]);
        }
    }

    public function test_cannot_update_permissions_for_department_from_other_cabinet(): void
    {
        $permissions = Permission::factory()->count(2)->create();

        $data = [
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'permission_id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ];
            })->toArray()
        ];

        $response = $this->putJson("/api/internal/departments/{$this->otherCabinetDepartment->id}/permissions", $data);

        $response->assertNotFound();

        // Проверяем что разрешения не были сохранены
        foreach ($permissions as $permission) {
            $this->assertDatabaseMissing('department_permissions', [
                'department_id' => $this->otherCabinetDepartment->id,
                'permission_id' => $permission->id
            ]);
        }
    }

    public function test_cannot_update_permissions_with_invalid_data(): void
    {
        $data = [
            'permissions' => [
                [
                    'permission_id' => 'invalid-uuid',
                    'scope' => 'invalid-scope'
                ]
            ]
        ];

        $response = $this->putJson("/api/internal/departments/{$this->department->id}/permissions", $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permissions.0.permission_id', 'permissions.0.scope']);
    }

    public function test_cannot_update_permissions_for_non_existent_department(): void
    {
        $permissions = Permission::factory()->count(2)->create();

        $data = [
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'permission_id' => $permission->id,
                    'scope' => PermissionScopeEnum::SCOPE_ALL->value
                ];
            })->toArray()
        ];

        $response = $this->putJson('/api/internal/departments/' . Str::uuid() . '/permissions', $data);

        $response->assertStatus(404);
    }
}
