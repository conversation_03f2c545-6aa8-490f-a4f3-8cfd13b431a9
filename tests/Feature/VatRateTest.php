<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\VatRate;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use Illuminate\Foundation\Testing\WithFaker;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Enums\Api\Internal\EntitiesTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class VatRateTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->anotherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_vat_rates_list(): void
    {
        // Arrange
        // Создаем ставки НДС для текущего кабинета
        VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем системные ставки НДС
        VatRate::factory()->count(2)->create([
            'cabinet_id' => null
        ]);

        // Создаем ставки НДС для другого кабинета
        VatRate::factory()->count(2)->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'rate',
                        'description',
                        'employee_id',
                        'department_id',
                        'archived_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили 5 записей (3 из кабинета + 2 системных)
        $response->assertJsonCount(5, 'data');
    }

    public function test_can_filter_by_type(): void
    {
        // Arrange
        VatRate::factory()->count(2)->create([
            'cabinet_id' => null // системные
        ]);
        VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id // пользовательские
        ]);

        // Act - получаем только системные
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => ['value' => EntitiesTypeEnum::SYSTEM->value]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(2, 'data');
        foreach ($response->json('data') as $vatRate) {
            $this->assertNull($vatRate['cabinet_id']);
        }

        // Act - получаем только пользовательские
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => ['value' => EntitiesTypeEnum::CUSTOM->value]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $vatRate) {
            $this->assertEquals($this->cabinet->id, $vatRate['cabinet_id']);
        }
    }

    public function test_can_filter_by_show_only(): void
    {
        // Arrange
        VatRate::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => Carbon::now()
        ]);
        VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => null
        ]);

        // Act - получаем только архивные
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => ['value' => ShowOnlyEnum::ARCHIVED->value]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(2, 'data');
        foreach ($response->json('data') as $vatRate) {
            $this->assertNotNull($vatRate['archived_at']);
        }

        // Act - получаем только неархивные
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => ['value' => ShowOnlyEnum::COMMON->value]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $vatRate) {
            $this->assertNull($vatRate['archived_at']);
        }
    }

    public function test_can_filter_by_rate_value(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 10
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'rate' => ['value' => 20]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertEquals(20, $response->json('data.0.rate'));
    }

    public function test_can_filter_by_description(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Test description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null
        ]);

        // Act - поиск по значению
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'value' => 'Test',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');

        // Act - поиск пустых значений
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.description'));
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
            'cabinet_id' => 'not-a-uuid',
            'filters' => [
                'updated_at' => [
                    'from' => 'invalid-date',
                    'to' => 'invalid-date'
                ]
            ]
        ]));

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
                'filters.updated_at.from',
                'filters.updated_at.to'
            ]);
    }

    public function test_cannot_access_other_cabinet_vat_rates(): void
    {
        // Arrange
        VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->anotherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertForbidden();
    }

    public function test_can_filter_description_with_in_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Test description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Another description'
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'value' => 'Test',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertStringContainsString('Test', $response->json('data.0.description'));
    }

    public function test_can_filter_description_with_not_in_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Test description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Another description'
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'value' => 'Test',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertStringNotContainsString('Test', $response->json('data.0.description'));
    }

    public function test_can_filter_description_with_empty_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Test description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertNull($response->json('data.0.description'));
    }

    public function test_can_filter_description_with_not_empty_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Test description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertNotNull($response->json('data.0.description'));
    }

    public function test_can_filter_employee_owners_with_in_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($this->employee->id, $response->json('data.0.employee_id'));
    }

    public function test_can_filter_employee_owners_with_not_in_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
    }

    public function test_can_filter_employee_owners_with_empty_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(0, 'data');
    }

    public function test_can_filter_employee_owners_with_not_empty_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(2, 'data');
    }

    public function test_can_filter_by_search_rate(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20,
            'description' => 'Some description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 10,
            'description' => 'Another description'
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => ['value' => '20']
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertEquals(20, $response->json('data.0.rate'));
    }

    public function test_can_filter_by_search_description(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20,
            'description' => 'Test description'
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 10,
            'description' => 'Another description'
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => ['value' => 'Test']
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertStringContainsString('Test', $response->json('data.0.description'));
    }

    public function test_can_filter_department_owners_with_in_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($this->department->id, $response->json('data.0.department_id'));
    }

    public function test_can_filter_department_owners_with_not_in_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(1, 'data');
    }

    public function test_can_filter_department_owners_with_empty_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(0, 'data');
    }

    public function test_can_filter_department_owners_with_not_empty_condition(): void
    {
        // Arrange
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);
        VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/vat-rates?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertOk();
        $response->assertJsonCount(2, 'data');
    }

    public function test_can_create_vat_rate(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Test VAT rate',
            'rate' => 20,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('vat_rates', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'description' => $data['description'],
            'rate' => $data['rate'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id']
        ]);
    }

    public function test_cannot_create_vat_rate_in_another_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->anotherCabinet->id,
            'description' => 'Test VAT rate',
            'rate' => 20,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates', $data);

        // Assert
        $response->assertForbidden();

        $this->assertDatabaseMissing('vat_rates', [
            'cabinet_id' => $data['cabinet_id'],
            'description' => $data['description']
        ]);
    }

    public function test_can_create_vat_rate_without_description(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('vat_rates', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'description' => null,
            'rate' => $data['rate'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id']
        ]);
    }

    public function test_store_validation_errors(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'description' => ['not-a-string'], // неверный тип
            'rate' => 'not-a-number', // неверный тип
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid'
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates', $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'description',
                'rate',
                'employee_id',
                'department_id'
            ]);
    }

    public function test_update_validation_errors(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'description' => ['not-a-string'], // неверный тип
            'rate' => 'not-a-number', // неверный тип
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid'
        ];

        // Act
        $response = $this->putJson("/api/internal/vat-rates/{$vatRate->id}", $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'description',
                'rate',
                'employee_id',
                'department_id'
            ]);
    }

    public function test_can_update_vat_rate(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 10,
            'description' => 'Old description',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $data = [
            'rate' => 20,
            'description' => 'New description',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/vat-rates/{$vatRate->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('vat_rates', [
            'id' => $vatRate->id,
            'rate' => $data['rate'],
            'description' => $data['description'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id']
        ]);
    }

    public function test_cannot_update_vat_rate_from_another_cabinet(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $data = [
            'rate' => 20,
            'description' => 'New description',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/vat-rates/{$vatRate->id}", $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_non_existent_vat_rate(): void
    {
        // Act
        $response = $this->putJson("/api/internal/vat-rates/{$this->faker->uuid()}", [
            'rate' => 20,
            'description' => 'New description',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_vat_rate(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20,
            'description' => 'Test description',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/vat-rates/{$vatRate->id}");

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'deleted_at',
                'cabinet_id',
                'rate',
                'description',
                'employee_id',
                'department_id',
                'archived_at'
            ])
            ->assertJson([
                'id' => $vatRate->id,
                'cabinet_id' => $this->cabinet->id,
                'rate' => 20,
                'description' => 'Test description',
                'employee_id' => $this->employee->id,
                'department_id' => $this->department->id
            ]);
    }

    public function test_cannot_show_vat_rate_from_another_cabinet(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/vat-rates/{$vatRate->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_vat_rate(): void
    {
        // Act
        $response = $this->getJson("/api/internal/vat-rates/{$this->faker->uuid()}");

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_vat_rate(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/vat-rates/{$vatRate->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseMissing('vat_rates', [
            'id' => $vatRate->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_vat_rate_from_another_cabinet(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/vat-rates/{$vatRate->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('vat_rates', [
            'id' => $vatRate->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_vat_rate(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/vat-rates/{$this->faker->uuid()}");

        // Assert
        $response->assertNotFound();
    }

    public function test_bulk_delete_validation_errors(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'ids' => 'not-an-array'
        ];

        // Act
        $response = $this->deleteJson('/api/internal/vat-rates/bulk-delete', $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_can_bulk_delete_vat_rates(): void
    {
        // Arrange
        $vatRates = VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vatRates->pluck('id')->toArray()
        ];

        // Act
        $response = $this->deleteJson('/api/internal/vat-rates/bulk-delete', $data);

        // Assert
        $response->assertNoContent();

        foreach ($vatRates as $vatRate) {
            $this->assertDatabaseMissing('vat_rates', [
                'id' => $vatRate->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_cannot_bulk_delete_vat_rates_from_another_cabinet(): void
    {
        // Arrange
        $vatRates = VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->anotherCabinet->id,
            'ids' => $vatRates->pluck('id')->toArray()
        ];

        // Act
        $response = $this->deleteJson('/api/internal/vat-rates/bulk-delete', $data);

        // Assert
        $response->assertForbidden();

        foreach ($vatRates as $vatRate) {
            $this->assertDatabaseHas('vat_rates', [
                'id' => $vatRate->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_archive_validation_errors(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'ids' => 'not-an-array'
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates/archive', $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_can_archive_vat_rates(): void
    {
        // Arrange
        $vatRates = VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => null
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vatRates->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates/archive', $data);

        // Assert
        $response->assertNoContent();

        foreach ($vatRates as $vatRate) {
            $this->assertDatabaseHas('vat_rates', [
                'id' => $vatRate->id,
            ]);
            $this->assertNotNull(VatRate::find($vatRate->id)->archived_at);
        }
    }

    public function test_cannot_archive_vat_rates_from_another_cabinet(): void
    {
        // Arrange
        $vatRates = VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->anotherCabinet->id,
            'archived_at' => null
        ]);

        $data = [
            'cabinet_id' => $this->anotherCabinet->id,
            'ids' => $vatRates->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates/archive', $data);

        // Assert
        $response->assertForbidden();

        foreach ($vatRates as $vatRate) {
            $this->assertDatabaseHas('vat_rates', [
                'id' => $vatRate->id,
                'archived_at' => null
            ]);
        }
    }

    public function test_unarchive_validation_errors(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'ids' => 'not-an-array'
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates/unarchive', $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_can_unarchive_vat_rates(): void
    {
        // Arrange
        $vatRates = VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => now()
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vatRates->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates/unarchive', $data);

        // Assert
        $response->assertNoContent();

        foreach ($vatRates as $vatRate) {
            $this->assertDatabaseHas('vat_rates', [
                'id' => $vatRate->id,
                'archived_at' => null
            ]);
        }
    }

    public function test_cannot_unarchive_vat_rates_from_another_cabinet(): void
    {
        // Arrange
        $vatRates = VatRate::factory()->count(3)->create([
            'cabinet_id' => $this->anotherCabinet->id,
            'archived_at' => now()
        ]);

        $data = [
            'cabinet_id' => $this->anotherCabinet->id,
            'ids' => $vatRates->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/vat-rates/unarchive', $data);

        // Assert
        $response->assertForbidden();

        foreach ($vatRates as $vatRate) {
            $this->assertNotNull(VatRate::find($vatRate->id)->archived_at);
        }
    }
}
