<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\VatRate;
use App\Models\Warehouse;
use App\Models\WarehouseOrderScheme;
use App\Models\WarehouseReceiptOrder;
use App\Models\WarehouseReceiptOrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseReceiptOrderItemsTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $employee;
    protected $cabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $vatRate;
    protected $receiptOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем ордерную схему приемок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => false,
        ]);
    }

    public function test_creates_receipt_order_items_with_order(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'ПО-001',
            'date_from' => '2024-01-15',
            'reason' => 'Поступление от поставщика',
            'total_quantity' => 150,
            'total_cost' => '22500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 100,
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                    'batch_number' => 'BATCH001',
                    'lot_number' => 'LOT001',
                    'expiry_date' => '2024-12-31',
                    'quality_status' => 'good',
                    'storage_location' => 'A1-01',
                    'vat_rate_id' => $this->vatRate->id,
                ],
                [
                    'product_id' => $this->product->id,
                    'quantity' => 50,
                    'unit_price' => '150.00',
                    'total_price' => '7500.00',
                    'batch_number' => 'BATCH002',
                    'lot_number' => 'LOT002',
                    'expiry_date' => '2025-01-31',
                    'quality_status' => 'good',
                    'storage_location' => 'A1-02',
                    'vat_rate_id' => $this->vatRate->id,
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(201);

        $receiptOrder = WarehouseReceiptOrder::where('number', 'ПО-001')->first();
        $this->assertNotNull($receiptOrder);

        // Проверяем, что создались позиции
        $items = WarehouseReceiptOrderItem::where('receipt_order_id', $receiptOrder->id)->get();
        $this->assertCount(2, $items);

        // Проверяем первую позицию
        $firstItem = $items->where('batch_number', 'BATCH001')->first();
        $this->assertEquals($this->product->id, $firstItem->product_id);
        $this->assertEquals(100, $firstItem->quantity);
        $this->assertEquals('150.00', $firstItem->unit_price);
        $this->assertEquals('15000.00', $firstItem->total_price);
        $this->assertEquals('good', $firstItem->quality_status);
        $this->assertEquals('A1-01', $firstItem->storage_location);

        // Проверяем вторую позицию
        $secondItem = $items->where('batch_number', 'BATCH002')->first();
        $this->assertEquals(50, $secondItem->quantity);
        $this->assertEquals('BATCH002', $secondItem->batch_number);
        $this->assertEquals('2025-01-31', $secondItem->expiry_date);
    }

    public function test_validates_required_item_fields(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    // quantity отсутствует
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quantity']);
    }

    public function test_validates_quality_status(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 100,
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                    'quality_status' => 'invalid_status', // Неверный статус
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quality_status']);
    }

    public function test_validates_expiry_date_format(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 100,
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                    'expiry_date' => 'invalid-date', // Неверный формат даты
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.expiry_date']);
    }

    public function test_validates_numeric_fields(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 'not-a-number', // Не число
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quantity']);
    }

    public function test_validates_positive_quantities(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => -10, // Отрицательное количество
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quantity']);
    }

    public function test_creates_warehouse_items_when_order_is_held(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'ПО-HOLD',
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 100,
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                    'batch_number' => 'BATCH-HOLD',
                    'quality_status' => 'good',
                ]
            ]
        ];

        // Act - создаем ордер
        $createResponse = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);
        $createResponse->assertStatus(201);

        $receiptOrder = WarehouseReceiptOrder::where('number', 'ПО-HOLD')->first();

        // Act - проводим ордер
        $holdResponse = $this->postJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}/hold");

        // Assert
        $holdResponse->assertStatus(200);

        // Проверяем, что создались warehouse_items
        $this->assertDatabaseHas('warehouse_items', [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'batch_number' => 'BATCH-HOLD',
            'quantity' => 100,
            'quality_status' => 'good',
        ]);

        // Проверяем, что создались транзакции
        $this->assertDatabaseHas('warehouse_transactions', [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'transaction_type' => 'in',
            'quantity' => 100,
            'document_type' => 'warehouse_receipt_order',
            'document_id' => $receiptOrder->id,
        ]);
    }

    public function test_cannot_modify_items_of_held_order(): void
    {
        // Arrange - создаем проведенный ордер
        $heldOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        WarehouseReceiptOrderItem::factory()->create([
            'receipt_order_id' => $heldOrder->id,
            'product_id' => $this->product->id,
        ]);

        $updateData = [
            'number' => 'UPDATED',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 200, // Пытаемся изменить количество
                    'unit_price' => '150.00',
                    'total_price' => '30000.00',
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$heldOrder->id}", $updateData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_product_exists(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'items' => [
                [
                    'product_id' => 'non-existent-uuid',
                    'quantity' => 100,
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.product_id']);
    }
}
