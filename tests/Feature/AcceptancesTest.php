<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\Purchases\AcceptancesServiceContract;
use App\Enums\Api\Internal\StatusTypeEnum;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceJob;
use App\Models\Acceptance;
use App\Models\AcceptanceItem;
use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\Status;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Random\RandomException;
use Tests\TestCase;

class AcceptancesTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->acceptanceService = $this->app->make(AcceptancesServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_acceptances_list(): void
    {
        // Arrange
        $cabinetId = $this->cabinet->id;
        $otherCabinetId = $this->otherCabinet->id;

        // Создаем приемки для нашего кабинета
        Acceptance::factory()->count(3)->create([
            'cabinet_id' => $cabinetId
        ]);

        // Создаем приемки для другого кабинета
        Acceptance::factory()->count(2)->create([
            'cabinet_id' => $otherCabinetId
        ]);

        $filters = [
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'cabinet_id' => $cabinetId
        ];

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'number',
                        'date_from',
                        'status_id',
                        'held',
                        'legal_entity_id',
                        'contractor_id',
                        'warehouse_id',
                        'incoming_number',
                        'incoming_date',
                        'currency_id',
                        'currency_value',
                        'comment',
                        'price_includes_vat',
                        'overhead_cost',
                        'total_price'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $acceptance) {
            $this->assertEquals($cabinetId, $acceptance['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_can_create_acceptance(): void
    {
        $legalEntity = LegalEntity::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $contractor = Contractor::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $warehouse = Warehouse::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $currency = CabinetCurrency::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'warehouse_id' => $warehouse->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/acceptances', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('acceptances', $data);
    }

    public function test_can_create_acceptance_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/acceptances');

        $response->assertJsonValidationErrors(
            [
                'cabinet_id',
                'employee_id',
                'department_id',
                'legal_entity_id',
                'contractor_id',
                'warehouse_id',
                'currency_id',
            ]
        );
    }

    /**
     * @throws RandomException
     */
    public function test_can_create_acceptance_with_all_fields(): void
    {
        $legalEntity = LegalEntity::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $contractor = Contractor::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $warehouse = Warehouse::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $currency = CabinetCurrency::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $status = Status::factory()->create(
            [
                'cabinet_id' => $this->cabinet->id,
                'type' => StatusTypeEnum::ACCEPTANCES->value
            ]
        );

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'warehouse_id' => $warehouse->id,
            'number' => $this->faker->shuffleString('asdasdasd'),
            'date_from' => Carbon::parse($this->faker->date())->toDateTimeString(),
            'status_id' => $status->id,
            'held' => $this->faker->boolean(),
            'incoming_number' => $this->faker->shuffleString('asdasdasd'),
            'incoming_date' => $this->faker->date(),
            'currency_value' => $this->faker->randomFloat(2, 0, 100),
            'comment' => $this->faker->text,
            'price_includes_vat' => $this->faker->boolean(),
            'overhead_cost' => random_int(0, 1000),
            'currency_id' => $currency->id
        ];

        $response = $this->postJson('/api/internal/acceptances', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        unset($data['number']);
        $this->assertDatabaseHas('acceptances', $data);
    }

    public function test_can_show_acceptance(): void
    {
        $acceptance = Acceptance::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $response = $this->getJson("/api/internal/acceptances/{$acceptance->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'cabinet_id',
                    'employee_id',
                    'department_id',
                    'is_common',
                    'number',
                    'date_from',
                    'status_id',
                    'held',
                    'legal_entity_id',
                    'contractor_id',
                    'warehouse_id',
                    'incoming_number',
                    'incoming_date',
                    'currency_id',
                    'currency_value',
                    'comment',
                    'price_includes_vat',
                    'overhead_cost',
                    'total_price'
            ]);
    }

    public function test_can_update_acceptance(): void
    {
        $legalEntity = LegalEntity::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $contractor = Contractor::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $warehouse = Warehouse::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $currency = CabinetCurrency::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $acceptance = Acceptance::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $status = Status::factory()->create(
            [
                'cabinet_id' => $this->cabinet->id,
                'type' => StatusTypeEnum::ACCEPTANCES->value
            ]
        );

        $updateData = [
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'warehouse_id' => $warehouse->id,
            'number' => $this->faker->shuffleString('asdasdasd'),
            'date_from' => Carbon::parse($this->faker->date())->toDateTimeString(),
            'status_id' => $status->id,
            'held' => $this->faker->boolean(),
            'incoming_number' => $this->faker->shuffleString('asdasdasd'),
            'incoming_date' => $this->faker->date(),
            'currency_value' => $this->faker->randomFloat(2, 0, 8),
            'comment' => $this->faker->text,
            'price_includes_vat' => $this->faker->boolean(),
            'overhead_cost' => random_int(0, 1000),
            'currency_id' => $currency->id
        ];

        $response = $this->putJson("/api/internal/acceptances/{$acceptance->id}", $updateData);

        $response->assertStatus(204);

        unset($updateData['number']);
        $this->assertDatabaseHas(
            'acceptances',
            array_merge($updateData, ['id' => $acceptance->id])
        );

        Queue::assertPushed(RecalculationAfterUpdateAcceptanceJob::class);
    }

    public function test_can_delete_acceptance(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $acceptanceItem = AcceptanceItem::factory()
            ->create([
                'acceptance_id' => $acceptance->id,
            ]);

        // Act
        $response = $this->deleteJson("/api/internal/acceptances/{$acceptance->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertNotEquals(
            null,
            DB::table('acceptances')
               ->where('id', $acceptance->id)
               ->first()->deleted_at
        );
        Queue::assertPushed(HandleFifoJob::class);
    }

    public function test_can_bulk_delete_acceptances(): void
    {
        // Arrange
        $ids = Acceptance::factory()->count(3)->create(
            ['cabinet_id' => $this->cabinet->id]
        )->pluck('id')->toArray();

        foreach ($ids as $id) {
            AcceptanceItem::factory()->create(
                ['acceptance_id' => $id]
            );
        }

        // Act
        $response = $this->deleteJson('/api/internal/acceptances/bulk-delete', [
            'ids' => $ids,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(204);
        foreach ($ids as $id) {
            $this->assertNotEquals(
                null,
                DB::table('acceptances')
                    ->where('id', $id)
                    ->first()->deleted_at
            );
        }
    }

    public function test_can_bulk_held_acceptances(): void
    {
        // Arrange
        $ids = Acceptance::factory()->count(3)->create(
            ['cabinet_id' => $this->cabinet->id]
        )->pluck('id')->toArray();

        // Act
        $response = $this->patchJson('/api/internal/acceptances/bulk-held', [
            'ids' => $ids,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(204);
        foreach ($ids as $id) {
            $this->assertDatabaseHas('acceptances', [
                'id' => $id,
                'held' => true
            ]);
        }
    }

    public function test_can_bulk_unheld_acceptances(): void
    {
        // Arrange
        $ids = Acceptance::factory()->count(3)->create(
            ['cabinet_id' => $this->cabinet->id, 'held' => true]
        )->pluck('id')->toArray();

        // Act
        $response = $this->patchJson('/api/internal/acceptances/bulk-unheld', [
            'ids' => $ids,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(204);
        foreach ($ids as $id) {
            $this->assertDatabaseHas('acceptances', [
                'id' => $id,
                'held' => false
            ]);
        }
    }

    public function test_can_bulk_copy_acceptances(): void
    {
        $status = Status::factory()
            ->create(
                ['cabinet_id' => $this->cabinet->id, 'name' => 'TEST-UPDATE']
            );
        // Arrange
        $ids = Acceptance::factory()->count(3)->create(
            ['cabinet_id' => $this->cabinet->id, 'status_id' => $status->id]
        )->pluck('id')->toArray();

        // Act
        $response = $this->postJson('/api/internal/acceptances/bulk-copy', [
            'ids' => $ids,
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Assert
        $response->assertStatus(204);
        $this->assertSame(
            DB::table('acceptances')
                ->where('cabinet_id', $this->cabinet->id)
                ->where('status_id', $status->id)
                ->count(),
            6
        );
    }

    public function test_cannot_update_acceptance_with_invalid_data(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $updateData = [
            // Отправляем пустые данные
        ];

        // Act
        $response = $this->putJson("/api/internal/acceptances/{$acceptance->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(
                [
                    'department_id',
                    'employee_id',
                    'legal_entity_id',
                    'contractor_id',
                    'warehouse_id',
                    'currency_id'
                ]
            );
    }

    public function test_cannot_access_non_existent_acceptance(): void
    {
        // Act
        $response = $this->getJson("/api/internal/acceptances/non-existent-id");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_bulk_delete_with_empty_ids(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/acceptances/bulk-delete', [
            'ids' => [],
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_cannot_bulk_held_with_empty_ids(): void
    {
        // Act
        $response = $this->patchJson('/api/internal/acceptances/bulk-held', [
            'ids' => [],
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_cannot_bulk_unheld_with_empty_ids(): void
    {
        // Act
        $response = $this->patchJson('/api/internal/acceptances/bulk-unheld', [
            'ids' => [],
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_cannot_bulk_copy_with_empty_ids(): void
    {
        // Act
        $response = $this->postJson('/api/internal/acceptances/bulk-copy', [
            'ids' => [],
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_index_with_filters(): void
    {
        // Arrange
        $filters = [
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
            'search' => 'test',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31',
            'status' => 'active'
        ];

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta'
            ]);
    }

    public function test_cannot_access_other_cabinet_acceptances(): void
    {
        $legalEntity = LegalEntity::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $contractor = Contractor::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $warehouse = Warehouse::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $currency = CabinetCurrency::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        // Arrange
        $otherCabinetId = $this->otherCabinet->id;

        // Создаем приемку в другом кабинете
        $otherCabinetAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $otherCabinetId
        ]);

        // Пытаемся получить список с чужим cabinet_id
        $listResponse = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $otherCabinetId
        ]));

        // Проверяем что список пустой
        $listResponse->assertStatus(403);

        // Пытаемся получить детали чужой приемки
        $showResponse = $this->getJson("/api/internal/acceptances/{$otherCabinetAcceptance->id}");

        $showResponse->assertNotFound();

        // Пытаемся обновить чужую приемку
        $updateResponse = $this->putJson("/api/internal/acceptances/{$otherCabinetAcceptance->id}", [
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'currency_id' => $currency->id,
            'warehouse_id' => $warehouse->id
        ]);

        // Проверяем что получаем 403 или 404
        $updateResponse->assertNotFound();

        // Пытаемся удалить чужую приемку
        $deleteResponse = $this->deleteJson("/api/internal/acceptances/{$otherCabinetAcceptance->id}");

        // Проверяем что получаем 403 или 404
        $deleteResponse->assertNotFound();

        // Проверяем что запись другого кабинета все еще существует
        $this->assertDatabaseHas('acceptances', [
            'id' => $otherCabinetAcceptance->id,
            'cabinet_id' => $otherCabinetId
        ]);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'filters' => [
                'period' => [
                    'from' => 'invalid-date',
                    'to' => '01.01.2023'
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
                'filters.period.from'
            ]);
    }

    public function test_index_validation_errors_with_conditions(): void
    {
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => 'INVALID_CONDITION',
                    'value' => ['invalid-uuid']
                ],
                'period' => [
                    'from' => 'invalid-date',
                    'to' => '01.01.2024 23:59'
                ],
                'incoming_date' => [
                    'condition' => 'IN',
                    'from' => '02.01.2024 00:00',
                    'to' => '01.01.2024 00:00' // to меньше from
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'filters.warehouses.condition',
                'filters.warehouses.value.0',
                'filters.period.from',
                'filters.incoming_date.to'
            ]);
    }

    public function test_index_with_selected_fields(): void
    {
        // Arrange
        Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'number', 'date_from']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'number',
                        'date_from'
                    ]
                ],
                'meta' => [

                ]
            ])
            ->assertJsonMissing(['created_at'])
            ->assertJsonMissing(['updated_at']);
    }

    public function test_bulk_operations_with_other_cabinet_data(): void
    {
        // Создаем приемки в другом кабинете
        $otherCabinetAcceptances = Acceptance::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);
        $otherCabinetIds = $otherCabinetAcceptances->pluck('id')->toArray();

        // Пытаемся выполнить bulk операции с чужими ID
        $bulkOperations = [
            [
                'method' => 'deleteJson',
                'url' => '/api/internal/acceptances/bulk-delete',
                'data' => ['ids' => $otherCabinetIds, 'cabinet_id' => $this->cabinet->id]
            ],
            [
                'method' => 'patchJson',
                'url' => '/api/internal/acceptances/bulk-held',
                'data' => ['ids' => $otherCabinetIds, 'cabinet_id' => $this->cabinet->id]
            ],
            [
                'method' => 'patchJson',
                'url' => '/api/internal/acceptances/bulk-unheld',
                'data' => ['ids' => $otherCabinetIds, 'cabinet_id' => $this->cabinet->id]
            ],
            [
                'method' => 'postJson',
                'url' => '/api/internal/acceptances/bulk-copy',
                'data' => ['ids' => $otherCabinetIds, 'cabinet_id' => $this->cabinet->id]
            ]
        ];

        foreach ($bulkOperations as $operation) {
            $response = $this->{$operation['method']}($operation['url'], $operation['data']);
            $response->assertStatus(404);

            // Проверяем что записи не были изменены
            foreach ($otherCabinetIds as $id) {
                $this->assertDatabaseHas('acceptances', [
                    'id' => $id,
                    'cabinet_id' => $this->otherCabinet->id
                ]);
            }
        }
    }

    public function test_can_update_acceptance_with_items(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $items = AcceptanceItem::factory()->count(3)->create([
            'acceptance_id' => $acceptance->id
        ]);

        $updateData = [
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'date_from' => now()->format('Y-m-d H:i:s')
        ];

        // Act
        $response = $this->putJson("/api/internal/acceptances/{$acceptance->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        // Проверяем что все связанные items существуют
        foreach ($items as $item) {
            $this->assertDatabaseHas('acceptance_items', ['id' => $item->id]);
        }

        // Проверяем что отправлены нужные jobs
        Queue::assertPushed(RecalculationAfterUpdateAcceptanceJob::class);
    }

    public function test_index_filter_warehouses_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'value' => [$warehouse->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'number',
                        'date_from',
                        'status_id',
                        'held',
                        'legal_entity_id',
                        'contractor_id',
                        'warehouse_id',
                        'incoming_number',
                        'incoming_date',
                        'currency_id',
                        'currency_value',
                        'comment',
                        'price_includes_vat',
                        'overhead_cost',
                        'total_price'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_filter_warehouses_not_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'value' => [$warehouse->id],
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'number',
                        'date_from',
                        'status_id',
                        'held',
                        'legal_entity_id',
                        'contractor_id',
                        'warehouse_id',
                        'incoming_number',
                        'incoming_date',
                        'currency_id',
                        'currency_value',
                        'comment',
                        'price_includes_vat',
                        'overhead_cost',
                        'total_price'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
        $response->assertJsonCount(0, 'data');
    }

    public function test_index_filter_warehouses_empty(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'number',
                        'date_from',
                        'status_id',
                        'held',
                        'legal_entity_id',
                        'contractor_id',
                        'warehouse_id',
                        'incoming_number',
                        'incoming_date',
                        'currency_id',
                        'currency_value',
                        'comment',
                        'price_includes_vat',
                        'overhead_cost',
                        'total_price'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
        $response->assertJsonCount(0, 'data');
    }

    public function test_index_filter_search(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => ['value' => 'SEARCH-TEST']
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
    }

    public function test_index_filter_contractor_owners_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);
        $newEmployee = Employee::factory()->create();
        $contractor = Contractor::factory()
            ->create(
                ['employee_id' => $newEmployee->id, 'cabinet_id' => $this->cabinet->id]
            );

        Acceptance::factory()->create([
            'contractor_id' => $contractor->id,
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'value' => [$newEmployee->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_contractor_owners_not_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => 'NOT_IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_incoming_number_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'incoming_number' => [
                    'value' => 'TEST-001',
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_legal_entity_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legal_entity' => [
                    'value' => [$testData['acceptanceWithAllFields']->legal_entity_id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_employee_owners_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_contractors_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$testData['contractor']->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_department_owners_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_statuses_in(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$testData['status']->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_is_common(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => true
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_is_held(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_held' => [
                    'value' => true
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_period(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'period' => [
                    'from' => '01.01.2024 00:00',
                    'to' => '01.01.2024 23:59'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_updated_at(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'updated_at' => [
                    'from' => '01.01.2024 00:00',
                    'to' => '01.01.2024 23:59'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    public function test_index_filter_incoming_date(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $testData = $this->createTestAcceptances($warehouse);

        // Act
        $response = $this->getJson('/api/internal/acceptances?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'incoming_date' => [
                    'from' => '01.01.2024 00:00',
                    'to' => '01.01.2024 23:59'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertJsonStructureSnapshot($response);
    }

    private function assertJsonStructureSnapshot($response): void
    {
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'cabinet_id',
                    'employee_id',
                    'department_id',
                    'is_common',
                    'number',
                    'date_from',
                    'status_id',
                    'held',
                    'legal_entity_id',
                    'contractor_id',
                    'warehouse_id',
                    'incoming_number',
                    'incoming_date',
                    'currency_id',
                    'currency_value',
                    'comment',
                    'price_includes_vat',
                    'overhead_cost',
                    'total_price'
                ]
            ],
            'meta' => [
                'current_page',
                'per_page',
                'last_page',
                'total'
            ]
        ]);
    }

    private function createTestAcceptances(Warehouse $warehouse): array
    {
        $contractor = Contractor::factory()->create(
            [
                'cabinet_id' => $this->cabinet->id,
                'employee_id' => $this->employee->id,
            ]
        );
        $legalEntity = LegalEntity::factory()->create(
            ['cabinet_id' => $this->cabinet->id, 'employee_id' => $this->employee->id,]
        );
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'type' => StatusTypeEnum::ACCEPTANCES->value
        ]);

        $acceptanceWithAllFields = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse->id,
            'contractor_id' => $contractor->id,
            'legal_entity_id' => $legalEntity->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'status_id' => $status->id,
            'held' => true,
            'is_common' => true,
            'incoming_number' => 'TEST-001',
            'date_from' => '2024-01-01 10:00:00',
            'incoming_date' => '2024-01-01 10:00:00',
            'updated_at' => '2024-01-01 10:00:00',
            'created_at' => '2024-01-01 10:00:00',
            'number' => 'SEARCH-TEST-001',
            'comment' => 'Test comment for search'
        ]);

        $acceptanceWithEmptyFields = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse->id,
            'contractor_id' => $contractor->id,
            'legal_entity_id' => $legalEntity->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'status_id' => null,
            'held' => false,
            'is_common' => false,
            'incoming_number' => null,
            'date_from' => '2024-01-02 10:00:00',
            'incoming_date' => null,
            'updated_at' => '2024-01-02 10:00:00'
        ]);

        return [
            'acceptanceWithAllFields' => $acceptanceWithAllFields,
            'acceptanceWithEmptyFields' => $acceptanceWithEmptyFields,
            'contractor' => $contractor,
            'status' => $status
        ];
    }
}
