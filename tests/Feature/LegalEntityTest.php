<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\VatRate;
use App\Models\Employee;
use App\Models\Department;
use App\Models\LegalEntity;
use App\Models\LegalDetail;
use App\Models\ProfitTaxRate;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\LegalEntityTaxation;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LegalEntityTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_legal_entities_list(): void
    {
        // Arrange
        LegalEntity::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем юр.лицо в другом кабинете
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'deleted_at',
                        'created_at',
                        'updated_at',
                        'archived_at',
                        'cabinet_id',
                        'short_name',
                        'code',
                        'phone',
                        'fax',
                        'email',
                        'discount_card',
                        'employee_id',
                        'department_id',
                        'is_default',
                        'shared_access',
                        'logo_image_id',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только юр.лица нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_get_legal_entities_without_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/legals');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_legal_entities_with_invalid_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => 'invalid-uuid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_access_other_cabinet_legal_entities(): void
    {
        // Arrange
        LegalEntity::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'filters' => [
                'code' => [
                    'condition' => 'invalid_condition'
                ],
                'show_only' => [
                    'value' => 'invalid_value'
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'page',
                'per_page',
                'sortDirection',
                'filters.code.condition',
                'filters.show_only.value'
            ]);
    }

    public function test_index_filter_code_in(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE1'
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE2'
        ]);
        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE3'
        ]);

        // Создаем юр.лицо в другом кабинете с таким же кодом
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'code' => 'CODE1'
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'value' => 'code1',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $this->assertEquals($this->cabinet->id, $response->json('data.0.cabinet_id'));
        $this->assertEquals('CODE1', $response->json('data.0.code'));
    }

    public function test_index_filter_code_not_in(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE1'
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE2'
        ]);
        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE3'
        ]);

        // Создаем юр.лицо в другом кабинете
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'code' => 'CODE4'
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'value' => 'code1',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertNotEquals('CODE1', $item['code']);
            $this->assertNotEquals($this->otherCabinet->id, $item['cabinet_id']);
        }
    }

    public function test_index_filter_code_empty(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE2'
        ]);

        // Создаем юр.лицо в другом кабинете с пустым кодом
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'code' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertContains($entity1->id, $ids);
        $this->assertNotContains($entity2->id, $ids);
    }

    public function test_index_filter_code_not_empty(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE2'
        ]);

        // Создаем юр.лицо в другом кабинете с непустым кодом
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'code' => 'CODE3'
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertNotContains($entity1->id, $ids);
        $this->assertContains($entity2->id, $ids);
    }

    public function test_index_filter_inn_in(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity1->id,
            'inn' =>  '1234567890'
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity2->id,
            'inn' =>  '0987654321'
        ]);
        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity3->id,
            'inn' =>  '5555555555'
        ]);

        // Создаем юр.лицо в другом кабинете с таким же ИНН
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $otherEntity->id,
            'inn' =>  '1234567890'
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'value' => '1234567890',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertContains($entity1->id, $ids);
        $this->assertNotContains($entity3->id, $ids);
        $this->assertNotContains($entity3->id, $ids);
        $this->assertNotContains($otherEntity->id, $ids);
    }

    public function test_index_filter_inn_not_in(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity1->id,
            'inn' =>  '1234567890'
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity2->id,
            'inn' =>  '0987654321'
        ]);
        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity3->id,
            'inn' =>  '5555555555'
        ]);

        // Создаем юр.лицо в другом кабинете
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $otherEntity->id,
            'inn' =>  '9999999999'
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'value' => '1234567890',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertNotContains($entity1->id, $ids);
        $this->assertNotContains($otherEntity->id, $ids);
        $this->assertContains($entity2->id, $ids);
        $this->assertContains($entity3->id, $ids);
    }

    public function test_index_filter_inn_empty(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity1->id,
            'inn' => null
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity2->id,
            'inn' =>  '1234567890'
        ]);

        // Создаем юр.лицо в другом кабинете с пустым ИНН
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $otherEntity->id,
            'inn' =>  null
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertContains($entity1->id, $ids);
        $this->assertNotContains($entity2->id, $ids);
    }

    public function test_index_filter_inn_not_empty(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity1->id,
            'inn' =>  null
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $entity2->id,
            'inn' =>  '1234567890'
        ]);

        // Создаем юр.лицо в другом кабинете с непустым ИНН
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $otherEntity->id,
            'inn' =>  '9999999999'
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertNotContains($entity1->id, $ids);
        $this->assertContains($entity2->id, $ids);
    }

    public function test_index_with_search_filter(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'short_name' => 'Test Company ABC'
        ]);
        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'short_name' => 'Another Company'
        ]);

        // Создаем юр.лицо в другом кабинете с похожими данными
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'short_name' => 'Test Company XYZ'
        ]);

        // Act - поиск по короткому названию
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'Test Company'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertContains($entity1->id, collect($response->json('data'))->pluck('id'));
    }

    public function test_index_filter_employee_owners_in(): void
    {
        // Arrange
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();
        $employee3 = Employee::factory()->create();

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee2->id
        ]);

        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee3->id
        ]);

        // Создаем юр.лицо в другом кабинете с тем же владельцем
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $employee1->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$employee1->id, $employee2->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertContains($entity1->id, $ids);
        $this->assertContains($entity2->id, $ids);
        $this->assertNotContains($entity3->id, $ids);
    }

    public function test_index_filter_employee_owners_not_in(): void
    {
        // Arrange
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();
        $employee3 = Employee::factory()->create();

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee2->id
        ]);

        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee3->id
        ]);

        // Создаем юр.лицо в другом кабинете
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $employee3->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$employee1->id, $employee2->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertNotContains($entity1->id, $ids);
        $this->assertNotContains($entity2->id, $ids);
        $this->assertContains($entity3->id, $ids);
    }

    public function test_index_filter_employee_owners_empty(): void
    {
        // Arrange
        $employee = Employee::factory()->create();

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем юр.лицо в другом кабинете без владельцев
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(0, 'data');
    }

    public function test_index_filter_employee_owners_not_empty(): void
    {
        // Arrange
        $employee = Employee::factory()->create();

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем юр.лицо в другом кабинете с владельцем
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_index_filter_department_owners_in(): void
    {
        // Arrange
        $department1 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department2 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department3 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department1->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department2->id
        ]);

        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department3->id
        ]);

        // Создаем юр.лицо в другом кабинете с тем же отделом
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department1->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$department1->id, $department2->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertContains($entity1->id, $ids);
        $this->assertContains($entity2->id, $ids);
        $this->assertNotContains($entity3->id, $ids);
    }

    public function test_index_filter_department_owners_not_in(): void
    {
        // Arrange
        $department1 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department2 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department3 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department1->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department2->id
        ]);

        $entity3 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department3->id
        ]);

        // Создаем юр.лицо в другом кабинете
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department1->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$department1->id, $department2->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');

        $ids = collect($response->json('data'))->pluck('id')->all();
        $this->assertNotContains($entity1->id, $ids);
        $this->assertNotContains($entity2->id, $ids);
        $this->assertContains($entity3->id, $ids);
    }

    public function test_index_filter_department_owners_empty(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем юр.лицо в другом кабинете без отделов
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(0, 'data');
    }

    public function test_index_filter_department_owners_not_empty(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем юр.лицо в другом кабинете с отделом
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_index_with_show_only_filter(): void
    {
        // Создаем юр. лица с разными статусами
        $active = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => null
        ]);
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'archived_at' => null
        ]);

        $archived = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => now()
        ]);
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'archived_at' => now()
        ]);
        // Тестируем фильтр ARCHIVED
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => [
                    'value' => 'archived'
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($archived->id, $response->json('data.0.id'));
        $this->assertEquals($this->cabinet->id, $response->json('data.0.cabinet_id'));
    }

    public function test_index_with_invalid_show_only_value(): void
    {
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => [
                    'value' => 'INVALID_VALUE'
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['filters.show_only.value']);
    }

    public function test_index_with_shared_access_filter_true(): void
    {
        // Создаем юр. лица с разными значениями shared_access
        $sharedEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'shared_access' => true
        ]);

        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'shared_access' => true
        ]);
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'shared_access' => false
        ]);

        $privateEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'shared_access' => false
        ]);

        // Тестируем фильтр shared_access = true
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'shared_access' => [
                        'value' => true
                    ]
                ]
            ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($sharedEntity->id, $response->json('data.0.id'));

        // Тестируем фильтр shared_access = false
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'shared_access' => [
                        'value' => true
                    ]
                ]
            ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertTrue($response->json('data.0.shared_access'));
        $this->assertEquals($this->cabinet->id, $response->json('data.0.cabinet_id'));
    }

    public function test_index_with_shared_access_filter_false(): void
    {
        // Создаем юр. лица с разными значениями shared_access
        $sharedEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'shared_access' => true
        ]);

        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'shared_access' => true
        ]);
        LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'shared_access' => false
        ]);

        $privateEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'shared_access' => false
        ]);

        // Тестируем фильтр shared_access = false
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'shared_access' => [
                        'value' => false
                    ]
                ]
            ]));

        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertEquals($privateEntity->id, $response->json('data.0.id'));
    }

    public function test_index_with_invalid_shared_access_value(): void
    {
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'shared_access' => [
                    'value' => 'not-a-boolean'
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['filters.shared_access.value']);
    }

    public function test_index_filter_search(): void
    {
        // Arrange
        $entity1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'short_name' => 'Test Company ABC',
        ]);

        $entity2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'short_name' => 'Another Company',
        ]);

        // Создаем юр.лицо в другом кабинете с похожими данными
        $otherEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'short_name' => 'Test Company XYZ',
        ]);

        // Act - поиск по короткому названию
        $response = $this->getJson('/api/internal/legals?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'Test Company',
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $this->assertContains($entity1->id, collect($response->json('data'))->pluck('id'));
    }

    public function test_can_create_legal_entity_with_minimal_required_fields_legal_type(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $this->faker->company(),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'full_name' => 'Общество с ограниченной ответственностью "' . $this->faker->company() . '"',
                'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value,
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/legals', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('legal_entities', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $data['short_name'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id']
        ]);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $response->json('id'),
            'type' => LegalEntityType::LEGAL->value,
            'prefix' => 'ООО',
            'full_name' => $data['detail']['full_name'],
        ]);
    }

    public function test_can_create_legal_entity_with_minimal_required_fields_individual_type(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $this->faker->name(),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => $this->faker->firstName(),
                'lastname' => $this->faker->lastName(),
                'patronymic' => $this->faker->firstName(), // В качестве отчества используем firstName
                'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value,
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/legals', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('legal_entities', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $data['short_name'],
        ]);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $response->json('id'),
            'type' => LegalEntityType::INDIVIDUAL->value,
            'firstname' => $data['detail']['firstname'],
            'lastname' => $data['detail']['lastname'],
            'patronymic' => $data['detail']['patronymic'],
        ]);
    }

    public function test_can_create_legal_entity_with_minimal_required_fields_entrepreneur_type(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $this->faker->company(),
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'detail' => [
                'type' => LegalEntityType::ENTREPRENEUR->value,
                'firstname' => $this->faker->firstName(),
                'lastname' => $this->faker->lastName(),
                'patronymic' => $this->faker->firstName(), // В качестве отчества используем firstName
                'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value,
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/legals', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('legal_entities', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $data['short_name']
        ]);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $response->json('id'),
            'type' => LegalEntityType::ENTREPRENEUR->value,
            'firstname' => $data['detail']['firstname'],
            'lastname' => $data['detail']['lastname'],
            'patronymic' => $data['detail']['patronymic'],
        ]);
    }

    public function test_can_create_legal_entity_with_all_fields(): void
    {
        // Arrange
        $vatRate = VatRate::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $taxRate = ProfitTaxRate::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'short_name' => $this->faker->company(),
            'code' => $this->faker->unique()->regexify('[A-Z0-9]{8}'),
            'phone' => '+' . $this->faker->numberBetween(1, 9) . $this->faker->numerify('#########'),
            'fax' => $this->faker->numerify('###-##-##'),
            'email' => $this->faker->companyEmail(),
            'discount_card' => $this->faker->unique()->regexify('[A-Z0-9]{6}'),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,

            'address' => [
                'postcode' => $this->faker->postcode(),
                'country' => $this->faker->country(),
                'region' => $this->faker->country(),
                'city' => $this->faker->city(),
                'street' => $this->faker->streetName(),
                'house' => $this->faker->buildingNumber(),
                'office' => $this->faker->numerify('###'),
                'other' => $this->faker->text(),
                'comment' => $this->faker->sentence()
            ],

            'head' => [
                'head_name' => $this->faker->name(),
                'head_position' => 'Генеральный директор',
                'accountant_name' => $this->faker->name()
            ],

            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'inn' => $this->faker->numerify('##########'),
                'kpp' => $this->faker->numerify('#########'),
                'ogrn' => $this->faker->numerify('#############'),
                'okpo' => $this->faker->numerify('##########'),
                'full_name' => 'Общество с ограниченной ответственностью "' . $this->faker->company() . '"',
                'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value,
                'tax_rate' => $taxRate->id,
                'vat_rate' => $vatRate->id,

                'address' => [
                    'postcode' => $this->faker->postcode(),
                    'country' => $this->faker->country(),
                    'region' => $this->faker->country(),
                    'city' => $this->faker->city(),
                    'street' => $this->faker->streetName(),
                    'house' => $this->faker->buildingNumber(),
                    'office' => $this->faker->numerify('###'),
                    'other' => $this->faker->text(),
                    'comment' => $this->faker->sentence()
                ]
            ],

            'accounts' => [
                [
                    'bik' => $this->faker->numerify('#########'),
                    'payment_account' => $this->faker->numerify('####################'),
                    'correspondent_account' => $this->faker->numerify('#####################'),
                    'balance' => $this->faker->randomFloat(2, 0, 1000000),
                    'bank' => $this->faker->company() . ' Банк',
                    'address' => $this->faker->address(),
                    'is_main' => true
                ],
                [
                    'bik' => $this->faker->numerify('#########'),
                    'payment_account' => $this->faker->numerify('####################'),
                    'correspondent_account' => $this->faker->numerify('#####################'),
                    'balance' => $this->faker->randomFloat(2, 0, 1000000),
                    'bank' => $this->faker->company() . ' Банк',
                    'address' => $this->faker->address(),
                    'is_main' => false
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/legals', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('legal_entities', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'short_name' => $data['short_name'],
            'code' => $data['code'],
            'phone' => $data['phone'],
            'fax' => $data['fax'],
            'email' => $data['email'],
            'discount_card' => $data['discount_card'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id']
        ]);

        $this->assertDatabaseHas('legal_addresses', [
            'legal_entity_id' => $response->json('id'),
            'postcode' => $data['address']['postcode'],
            'country' => $data['address']['country'],
            'region' => $data['address']['region'],
            'city' => $data['address']['city'],
            'street' => $data['address']['street'],
            'house' => $data['address']['house'],
            'office' => $data['address']['office'],
            'other' => $data['address']['other'],
            'comment' => $data['address']['comment']
        ]);

        $this->assertDatabaseHas('legal_heads', [
            'legal_entity_id' => $response->json('id'),
            'head_name' => $data['head']['head_name'],
            'head_position' => $data['head']['head_position'],
            'accountant_name' => $data['head']['accountant_name']
        ]);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $response->json('id'),
            'type' => $data['detail']['type'],
            'prefix' => $data['detail']['prefix'],
            'inn' => $data['detail']['inn'],
            'kpp' => $data['detail']['kpp'],
            'ogrn' => $data['detail']['ogrn'],
            'okpo' => $data['detail']['okpo'],
            'full_name' => $data['detail']['full_name'],
            'taxation_type' => $data['detail']['taxation_type'],
            'tax_rate' => $data['detail']['tax_rate'],
            'vat_rate' => $data['detail']['vat_rate']
        ]);

        $legalDetail = DB::table('legal_details')
            ->where('legal_entity_id', $response->json('id'))
            ->first();

        $this->assertDatabaseHas('legal_detail_addresses', [
            'legal_detail_id' => $legalDetail->id,
            'postcode' => $data['detail']['address']['postcode'],
            'country' => $data['detail']['address']['country'],
            'region' => $data['detail']['address']['region'],
            'city' => $data['detail']['address']['city'],
            'street' => $data['detail']['address']['street'],
            'house' => $data['detail']['address']['house'],
            'office' => $data['detail']['address']['office'],
            'other' => $data['detail']['address']['other'],
            'comment' => $data['detail']['address']['comment']
        ]);

        foreach ($data['accounts'] as $account) {
            $this->assertDatabaseHas('legal_accounts', [
                'legal_entity_id' => $response->json('id'),
                'bik' => $account['bik'],
                'payment_account' => $account['payment_account'],
                'correspondent_account' => $account['correspondent_account'],
                'balance' => $account['balance'],
                'bank' => $account['bank'],
                'address' => $account['address'],
                'is_main' => $account['is_main']
            ]);
        }
    }

    public function test_cannot_create_legal_entity_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/legals', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'short_name',
                'department_id',
                'employee_id',
                'detail.type',
                'detail.taxation_type'
            ]);
    }

    public function test_cannot_create_legal_entity_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'short_name' => 'Test Company',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'full_name' => 'Общество с ограниченной ответственностью "Тест"',
                'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/legals', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_update_legal_entity_with_minimal_fields(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntity->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        $data = [
            'short_name' => $this->faker->company(),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'full_name' => 'Общество с ограниченной ответственностью "' . $this->faker->company() . '"',
                'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/legals/{$legalEntity->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('legal_entities', [
            'id' => $legalEntity->id,
            'short_name' => $data['short_name'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id']
        ]);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $legalEntity->id,
            'type' => $data['detail']['type'],
            'prefix' => $data['detail']['prefix'],
            'full_name' => $data['detail']['full_name'],
            'taxation_type' => $data['detail']['taxation_type']
        ]);
    }

    public function test_can_update_legal_entity_with_all_fields(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntity->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        $vatRate = VatRate::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $taxRate = ProfitTaxRate::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'short_name' => $this->faker->company(),
            'code' => $this->faker->unique()->regexify('[A-Z0-9]{8}'),
            'phone' => '+' . $this->faker->numberBetween(1, 9) . $this->faker->numerify('#########'),
            'fax' => $this->faker->numerify('###-##-##'),
            'email' => $this->faker->companyEmail(),
            'discount_card' => $this->faker->unique()->regexify('[A-Z0-9]{6}'),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,

            'address' => [
                'postcode' => $this->faker->postcode(),
                'country' => $this->faker->country(),
                'region' => $this->faker->country(),
                'city' => $this->faker->city(),
                'street' => $this->faker->streetName(),
                'house' => $this->faker->buildingNumber(),
                'office' => $this->faker->numerify('###'),
                'other' => $this->faker->text(),
                'comment' => $this->faker->sentence()
            ],

            'head' => [
                'head_name' => $this->faker->name(),
                'head_position' => 'Генеральный директор',
                'accountant_name' => $this->faker->name()
            ],

            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'inn' => $this->faker->numerify('##########'),
                'kpp' => $this->faker->numerify('#########'),
                'ogrn' => $this->faker->numerify('#############'),
                'okpo' => $this->faker->numerify('##########'),
                'full_name' => 'Общество с ограниченной ответственностью "' . $this->faker->company() . '"',
                'taxation_type' => LegalEntityTaxation::osno->value,
                'tax_rate' => $taxRate->id,
                'vat_rate' => $vatRate->id,

                'address' => [
                    'postcode' => $this->faker->postcode(),
                    'country' => $this->faker->country(),
                    'region' => $this->faker->country(),
                    'city' => $this->faker->city(),
                    'street' => $this->faker->streetName(),
                    'house' => $this->faker->buildingNumber(),
                    'office' => $this->faker->numerify('###'),
                    'other' => $this->faker->text(),
                    'comment' => $this->faker->sentence()
                ]
            ],

            'accounts' => [
                [
                    'bik' => $this->faker->numerify('#########'),
                    'payment_account' => $this->faker->numerify('####################'),
                    'correspondent_account' => $this->faker->numerify('#####################'),
                    'balance' => $this->faker->randomFloat(2, 0, 1000000),
                    'bank' => $this->faker->company() . ' Банк',
                    'address' => $this->faker->address(),
                    'is_main' => true
                ],
                [
                    'bik' => $this->faker->numerify('#########'),
                    'payment_account' => $this->faker->numerify('####################'),
                    'correspondent_account' => $this->faker->numerify('#####################'),
                    'balance' => $this->faker->randomFloat(2, 0, 1000000),
                    'bank' => $this->faker->company() . ' Банк',
                    'address' => $this->faker->address(),
                    'is_main' => false
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/legals/{$legalEntity->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('legal_entities', [
            'id' => $legalEntity->id,
            'short_name' => $data['short_name'],
            'code' => $data['code'],
            'phone' => $data['phone'],
            'fax' => $data['fax'],
            'email' => $data['email'],
            'discount_card' => $data['discount_card'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id']
        ]);

        $this->assertDatabaseHas('legal_addresses', [
            'legal_entity_id' => $legalEntity->id,
            'postcode' => $data['address']['postcode'],
            'country' => $data['address']['country'],
            'region' => $data['address']['region'],
            'city' => $data['address']['city'],
            'street' => $data['address']['street'],
            'house' => $data['address']['house'],
            'office' => $data['address']['office'],
            'other' => $data['address']['other'],
            'comment' => $data['address']['comment']
        ]);

        $this->assertDatabaseHas('legal_heads', [
            'legal_entity_id' => $legalEntity->id,
            'head_name' => $data['head']['head_name'],
            'head_position' => $data['head']['head_position'],
            'accountant_name' => $data['head']['accountant_name']
        ]);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $legalEntity->id,
            'type' => $data['detail']['type'],
            'prefix' => $data['detail']['prefix'],
            'inn' => $data['detail']['inn'],
            'kpp' => $data['detail']['kpp'],
            'ogrn' => $data['detail']['ogrn'],
            'okpo' => $data['detail']['okpo'],
            'full_name' => $data['detail']['full_name'],
            'taxation_type' => $data['detail']['taxation_type'],
            'tax_rate' => $data['detail']['tax_rate'],
            'vat_rate' => $data['detail']['vat_rate']
        ]);

        $legalDetail = DB::table('legal_details')
            ->where('legal_entity_id', $legalEntity->id)
            ->first();

        $this->assertDatabaseHas('legal_detail_addresses', [
            'legal_detail_id' => $legalDetail->id,
            'postcode' => $data['detail']['address']['postcode'],
            'country' => $data['detail']['address']['country'],
            'region' => $data['detail']['address']['region'],
            'city' => $data['detail']['address']['city'],
            'street' => $data['detail']['address']['street'],
            'house' => $data['detail']['address']['house'],
            'office' => $data['detail']['address']['office'],
            'other' => $data['detail']['address']['other'],
            'comment' => $data['detail']['address']['comment']
        ]);

        foreach ($data['accounts'] as $account) {
            $this->assertDatabaseHas('legal_accounts', [
                'legal_entity_id' => $legalEntity->id,
                'bik' => $account['bik'],
                'payment_account' => $account['payment_account'],
                'correspondent_account' => $account['correspondent_account'],
                'balance' => $account['balance'],
                'bank' => $account['bank'],
                'address' => $account['address'],
                'is_main' => $account['is_main']
            ]);
        }
    }

    public function test_can_update_legal_entity_type_from_legal_to_individual(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntity->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        $data = [
            'short_name' => $this->faker->name(),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => $this->faker->firstName(),
                'lastname' => $this->faker->lastName(),
                'patronymic' => $this->faker->firstName(),
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/legals/{$legalEntity->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('legal_details', [
            'legal_entity_id' => $legalEntity->id,
            'type' => LegalEntityType::INDIVIDUAL->value,
            'firstname' => $data['detail']['firstname'],
            'lastname' => $data['detail']['lastname'],
            'patronymic' => $data['detail']['patronymic']
        ]);
    }

    public function test_cannot_update_legal_entity_in_other_cabinet(): void
    {
        // Arrange
        $otherLegalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $otherLegalEntity->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        $data = [
            'short_name' => $this->faker->company(),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'full_name' => 'Общество с ограниченной ответственностью "' . $this->faker->company() . '"',
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/legals/{$otherLegalEntity->id}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_legal_entity_with_invalid_data(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntity->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        $data = [
            'short_name' => '', // Пустое значение
            'department_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'phone' => 'invalid-phone',
            'email' => 'invalid-email',
            'detail' => [
                'type' => 'invalid-type',
                'inn' => 'invalid-inn',
                'kpp' => 'invalid-kpp',
                'taxation_type' => 'invalid-taxation'
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/legals/{$legalEntity->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'short_name',
                'department_id',
                'employee_id',
                'phone',
                'email',
                'detail.type',
                'detail.taxation_type'
            ]);
    }

    public function test_cannot_update_non_existent_legal_entity(): void
    {
        // Act
        $response = $this->putJson("/api/internal/legals/" . Str::uuid(), [
            'short_name' => $this->faker->company(),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'prefix' => 'ООО',
                'full_name' => 'Общество с ограниченной ответственностью "' . $this->faker->company() . '"',
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_legal_entity(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ]);

        $legalDetail = LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntity->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/legals/{$legalEntity->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    'id',
                    'cabinet_id',
                    'short_name',
                    'code',
                    'phone',
                    'fax',
                    'email',
                    'discount_card',
                    'department_id',
                    'employee_id',
                    'address',
                    'head',
                    'detail',
                    'accounts'
            ]);

        $this->assertEquals($legalEntity->id, $response->json('id'));
        $this->assertEquals($legalEntity->cabinet_id, $response->json('cabinet_id'));
    }

    public function test_cannot_show_legal_entity_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/legals/{$legalEntity->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_legal_entity(): void
    {
        // Act
        $response = $this->getJson("/api/internal/legals/" . Str::uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_legal_entity(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/legals/{$legalEntity->id}");

        // Assert
        $response->assertStatus(204);
        $this->assertSoftDeleted('legal_entities', ['id' => $legalEntity->id]);
    }

    public function test_cannot_delete_legal_entity_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/legals/{$legalEntity->id}");

        // Assert
        $response->assertNotFound();
        $this->assertDatabaseHas('legal_entities', ['id' => $legalEntity->id]);
    }

    public function test_cannot_delete_non_existent_legal_entity(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/legals/" . Str::uuid());

        // Assert
        $response->assertStatus(404);
    }
}
