<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\WarehouseReceiptOrdersServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\VatRate;
use App\Models\Warehouse;
use App\Models\WarehouseReceiptOrder;
use App\Models\WarehouseReceiptOrderItem;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseReceiptOrdersTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $receiptOrderService;
    protected $employee;
    protected $cabinet;
    protected $otherCabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $vatRate;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->receiptOrderService = $this->app->make(WarehouseReceiptOrdersServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем склад с ордерной схемой
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_receipt_orders_list(): void
    {
        // Arrange
        $cabinetId = $this->cabinet->id;
        $otherCabinetId = $this->otherCabinet->id;

        // Создаем приходные ордера для нашего кабинета
        WarehouseReceiptOrder::factory()->count(3)->create([
            'cabinet_id' => $cabinetId,
            'warehouse_id' => $this->warehouse->id,
        ]);

        // Создаем приходные ордера для другого кабинета
        WarehouseReceiptOrder::factory()->count(2)->create([
            'cabinet_id' => $otherCabinetId
        ]);

        $filters = [
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'cabinet_id' => $cabinetId
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'cabinet_id',
                    'warehouse_id',
                    'employee_id',
                    'department_id',
                    'number',
                    'date_from',
                    'held',
                    'total_quantity',
                    'total_cost',
                ]
            ],
            'meta'
        ]);

        $responseData = $response->json();
        $this->assertCount(3, $responseData['data']);
    }

    public function test_can_create_receipt_order(): void
    {
        // Arrange
        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'ПО-001',
            'date_from' => '2024-01-15',
            'reason' => 'Поступление от поставщика',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
            'comment' => 'Тестовый приходный ордер',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 100,
                    'unit_price' => '150.00',
                    'total_price' => '15000.00',
                    'batch_number' => 'BATCH001',
                    'lot_number' => 'LOT001',
                    'expiry_date' => '2024-12-31',
                    'quality_status' => 'good',
                    'storage_location' => 'A1-01',
                    'vat_rate_id' => $this->vatRate->id,
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'message'
        ]);

        $this->assertDatabaseHas('warehouse_receipt_orders', [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'ПО-001',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
        ]);

        $receiptOrder = WarehouseReceiptOrder::where('number', 'ПО-001')->first();
        $this->assertDatabaseHas('warehouse_receipt_order_items', [
            'receipt_order_id' => $receiptOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'batch_number' => 'BATCH001',
        ]);
    }

    public function test_can_show_receipt_order(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}");

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'id',
            'cabinet_id',
            'warehouse_id',
            'employee_id',
            'department_id',
            'number',
            'date_from',
            'held',
            'total_quantity',
            'total_cost',
        ]);
    }

    public function test_can_update_receipt_order(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => false,
        ]);

        $updateData = [
            'number' => 'ПО-UPDATED',
            'comment' => 'Обновленный комментарий',
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}", $updateData);

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Приходный ордер обновлен'
        ]);

        $this->assertDatabaseHas('warehouse_receipt_orders', [
            'id' => $receiptOrder->id,
            'number' => 'ПО-UPDATED',
            'comment' => 'Обновленный комментарий',
        ]);
    }

    public function test_can_delete_receipt_order(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => false,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}");

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Приходный ордер удален'
        ]);

        $this->assertDatabaseMissing('warehouse_receipt_orders', [
            'id' => $receiptOrder->id,
        ]);
    }

    public function test_can_hold_receipt_order(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => false,
        ]);

        // Act
        $response = $this->postJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}/hold");

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Приходный ордер проведен'
        ]);

        $this->assertDatabaseHas('warehouse_receipt_orders', [
            'id' => $receiptOrder->id,
            'held' => true,
        ]);
    }

    public function test_cannot_update_held_receipt_order(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        $updateData = [
            'number' => 'ПО-UPDATED',
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}", $updateData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_cannot_delete_held_receipt_order(): void
    {
        // Arrange
        $receiptOrder = WarehouseReceiptOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouse-order-scheme/warehouse-receipt-orders/{$receiptOrder->id}");

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_order_scheme_for_warehouse(): void
    {
        // Arrange - создаем склад без ордерной схемы
        $regularWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'detailed_batch_tracking' => false,
            'expiry_control' => false,
            'quality_control' => false,
        ]);

        $receiptOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $regularWarehouse->id,
            'date_from' => '2024-01-15',
            'total_quantity' => 100,
            'total_cost' => '15000.00',
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-receipt-orders', $receiptOrderData);

        // Assert
        $response->assertStatus(422);
    }
}
