<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\File;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Jobs\DeleteFileJob;
use App\Models\Department;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FileTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Storage::fake('s3');
    }

    public function test_can_get_files_list(): void
    {
        // Создаем файлы для текущего кабинета
        $files = [];
        for ($i = 0; $i < 3; $i++) {
            $uploadedFile = UploadedFile::fake()->create("document{$i}.pdf", 100);
            $files[] = File::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => "document{$i}.pdf",
                'path' => $uploadedFile->hashName(),
                'mime_type' => 'application/pdf',
                'size' => $uploadedFile->getSize(),
                'type' => 'document'
            ]);
        }

        // Создаем файл для другого кабинета
        $otherFile = UploadedFile::fake()->create('other.pdf', 100);
        File::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'other.pdf',
            'path' => $otherFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $otherFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->getJson('/api/internal/files?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'path',
                        'size',
                        'mime_type',
                        'type',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 файла
        $response->assertJsonCount(3, 'data');

        // Проверяем что все файлы принадлежат нашему кабинету
        foreach ($response->json('data') as $item) {
            $this->assertDatabaseHas('files', [
                'id' => $item['id'],
                'cabinet_id' => $this->cabinet->id
            ]);
        }
    }

    public function test_cannot_access_other_cabinet_files(): void
    {
        $response = $this->getJson('/api/internal/files?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/files?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_pagination(): void
    {
        // Создаем 15 файлов
        for ($i = 0; $i < 15; $i++) {
            $uploadedFile = UploadedFile::fake()->create("document{$i}.pdf", 100);
            File::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => "document{$i}.pdf",
                'path' => $uploadedFile->hashName(),
                'mime_type' => 'application/pdf',
                'size' => $uploadedFile->getSize(),
                'type' => 'document'
            ]);
        }

        // Запрашиваем первую страницу с 10 элементами
        $response = $this->getJson('/api/internal/files?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 10
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJson([
                'meta' => [
                    'current_page' => 1,
                    'per_page' => 10,
                    'last_page' => 2,
                    'total' => 15
                ]
            ]);

        // Запрашиваем вторую страницу
        $response = $this->getJson('/api/internal/files?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(5, 'data')
            ->assertJson([
                'meta' => [
                    'current_page' => 2,
                    'per_page' => 10,
                    'last_page' => 2,
                    'total' => 15
                ]
            ]);
    }

    public function test_index_with_selected_fields(): void
    {
        // Создаем тестовый файл
        $uploadedFile = UploadedFile::fake()->create('document.pdf', 100);
        File::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $uploadedFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->getJson('/api/internal/files?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'name']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name'
                    ]
                ]
            ]);

        // Проверяем что в ответе только запрошенные поля
        $fileData = $response->json('data.0');
        $this->assertCount(2, array_keys($fileData));
        $this->assertArrayHasKey('id', $fileData);
        $this->assertArrayHasKey('name', $fileData);
    }

    public function test_can_store_public_file(): void
    {
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => $this->cabinet->id,
            'file' => $file,
            'is_private' => false
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $fileId = $response->json('id');

        // Проверяем что файл сохранен в БД
        $this->assertDatabaseHas('files', [
            'id' => $fileId,
            'name' => 'document.pdf',
            'mime_type' => 'application/pdf',
            'size' => $file->getSize(),
            'cabinet_id' => $this->cabinet->id,
            'is_private' => false
        ]);

        // Проверяем что файл был сохранен в хранилище
        Storage::disk('s3-images')->assertExists(
            File::where('id', $fileId)->value('path')
        );
    }

    public function test_can_store_private_file(): void
    {
        $file = UploadedFile::fake()->create('private.pdf', 100);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => $this->cabinet->id,
            'file' => $file,
            'is_private' => true
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $fileId = $response->json('id');

        // Проверяем что файл сохранен в БД
        $this->assertDatabaseHas('files', [
            'id' => $fileId,
            'name' => 'private.pdf',
            'mime_type' => 'application/pdf',
            'size' => $file->getSize(),
            'cabinet_id' => $this->cabinet->id,
            'is_private' => true
        ]);

        // Проверяем что файл был сохранен в приватное хранилище
        Storage::disk('s3-docs')->assertExists(
            File::where('id', $fileId)->value('path')
        );
    }

    public function test_cannot_store_file_in_other_cabinet(): void
    {
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => $this->otherCabinet->id,
            'file' => $file
        ]);

        $response->assertStatus(403);
    }

    public function test_cannot_store_file_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/files', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'file'
            ]);
    }

    public function test_cannot_store_file_with_invalid_cabinet_id(): void
    {
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => 'not-a-uuid',
            'file' => $file
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_store_file_exceeding_size_limit(): void
    {
        // Создаем файл размером 11MB (больше лимита в 10MB)
        $file = UploadedFile::fake()->create('large.pdf', 11264);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => $this->cabinet->id,
            'file' => $file
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['file']);
    }

    public function test_store_file_sets_correct_employee(): void
    {
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => $this->cabinet->id,
            'file' => $file
        ]);

        $response->assertStatus(201);
        $fileId = $response->json('id');

        // Проверяем что файл привязан к правильному сотруднику
        $this->assertDatabaseHas('files', [
            'id' => $fileId,
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_store_file_preserves_original_name(): void
    {
        $originalName = 'Тестовый документ.pdf';
        $file = UploadedFile::fake()->create($originalName, 100);

        $response = $this->postJson('/api/internal/files', [
            'cabinet_id' => $this->cabinet->id,
            'file' => $file
        ]);

        $response->assertStatus(201);
        $fileId = $response->json('id');

        // Проверяем что оригинальное имя файла сохранено
        $this->assertDatabaseHas('files', [
            'id' => $fileId,
            'name' => $originalName
        ]);
    }

    public function test_can_show_file(): void
    {
        // Создаем тестовый файл
        $uploadedFile = UploadedFile::fake()->create('document.pdf', 100);
        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $uploadedFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->getJson("/api/internal/files/{$file->id}");

        $response->assertStatus(200)
            ->assertJson([
                'id' => $file->id,
                'name' => 'document.pdf',
                'mime_type' => 'application/pdf',
                'size' => $uploadedFile->getSize(),
                'type' => 'document'
            ]);
    }

    public function test_cannot_show_file_from_other_cabinet(): void
    {
        // Создаем файл в другом кабинете
        $uploadedFile = UploadedFile::fake()->create('document.pdf', 100);
        $file = File::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $uploadedFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->getJson("/api/internal/files/{$file->id}");

        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_file(): void
    {
        $response = $this->getJson("/api/internal/files/" . $this->faker->uuid());

        $response->assertStatus(404);
    }

    public function test_can_download_public_file(): void
    {
        // Создаем публичный файл
        $content = 'test content';
        $uploadedFile = UploadedFile::fake()->createWithContent('document.pdf', $content);

        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => strlen($content),
            'type' => 'document',
            'is_private' => false
        ]);

        // Сохраняем файл в фейковое хранилище
        Storage::disk('s3-images')->put($file->path, $content);

        $response = $this->get("/api/internal/files/{$file->id}/download");

        $response->assertStatus(200)
            ->assertHeader('Content-Type', 'application/pdf')
            ->assertHeader('Content-Length', strlen($content))
            ->assertHeader('Content-Disposition', 'attachment; filename=document.pdf');
    }

    public function test_can_download_private_file(): void
    {
        // Создаем приватный файл
        $content = 'private content';
        $uploadedFile = UploadedFile::fake()->createWithContent('private.pdf', $content);

        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'private.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => strlen($content),
            'type' => 'document',
            'is_private' => true
        ]);

        // Сохраняем файл в фейковое хранилище
        Storage::disk('s3-docs')->put($file->path, $content);

        $response = $this->get("/api/internal/files/{$file->id}/download");

        $response->assertStatus(200)
            ->assertHeader('Content-Type', 'application/pdf')
            ->assertHeader('Content-Length', strlen($content))
            ->assertHeader('Content-Disposition', 'attachment; filename=private.pdf');
    }

    public function test_cannot_download_file_from_other_cabinet(): void
    {
        // Создаем файл в другом кабинете
        $uploadedFile = UploadedFile::fake()->create('document.pdf', 100);
        $file = File::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $uploadedFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->get("/api/internal/files/{$file->id}/download");

        $response->assertNotFound();
    }

    public function test_cannot_download_non_existent_file(): void
    {
        $response = $this->get("/api/internal/files/" . $this->faker->uuid() . "/download");

        $response->assertStatus(404);
    }

    public function test_can_delete_file(): void
    {
        Queue::fake();
        // Создаем тестовый файл
        $uploadedFile = UploadedFile::fake()->create('document.pdf', 100);
        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $uploadedFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->deleteJson("/api/internal/files/{$file->id}");

        $response->assertStatus(204);

        Queue::assertPushed(DeleteFileJob::class);
    }

    public function test_cannot_delete_file_from_other_cabinet(): void
    {
        // Создаем файл в другом кабинете
        $uploadedFile = UploadedFile::fake()->create('document.pdf', 100);
        $file = File::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'document.pdf',
            'path' => $uploadedFile->hashName(),
            'mime_type' => 'application/pdf',
            'size' => $uploadedFile->getSize(),
            'type' => 'document'
        ]);

        $response = $this->deleteJson("/api/internal/files/{$file->id}");

        $response->assertNotFound();

        // Проверяем что файл не был удален
        $this->assertDatabaseHas('files', ['id' => $file->id]);
    }

    public function test_cannot_delete_non_existent_file(): void
    {
        $response = $this->deleteJson("/api/internal/files/" . $this->faker->uuid());

        $response->assertStatus(404);
    }
}
