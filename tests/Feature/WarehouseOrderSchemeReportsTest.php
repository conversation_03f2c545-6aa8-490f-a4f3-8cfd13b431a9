<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\WarehouseOrderSchemeReportsServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use App\Models\WarehouseReceiptOrder;
use App\Models\WarehouseIssueOrder;
use App\Models\WarehouseReservation;
use App\Models\WarehouseTransaction;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseOrderSchemeReportsTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $reportsService;
    protected $employee;
    protected $cabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $warehouseItem;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->reportsService = $this->app->make(WarehouseOrderSchemeReportsServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем склад с ордерной схемой
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'available_quantity' => 80,
            'reserved_quantity' => 20,
            'quality_status' => 'good',
        ]);

        // Создаем тестовые данные для отчетов
        $this->createTestData();
    }

    private function createTestData(): void
    {
        // Создаем приходные ордера
        WarehouseReceiptOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
            'date_from' => '2024-01-15',
        ]);

        // Создаем расходные ордера
        WarehouseIssueOrder::factory()->count(1)->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
            'date_from' => '2024-01-16',
            'write_off_reason' => 'defective',
        ]);

        // Создаем резервы
        WarehouseReservation::factory()->count(3)->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'status' => 'reserved',
            'reservation_type' => 'order',
            'reserved_at' => '2024-01-15 10:00:00',
        ]);

        // Создаем транзакции
        WarehouseTransaction::factory()->count(5)->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'transaction_date' => '2024-01-15',
        ]);
    }

    public function test_can_get_stock_movement_report(): void
    {
        // Arrange
        $filters = [
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-31',
            'product_id' => $this->product->id,
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/stock-movement?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'movements' => [
                '*' => [
                    'id',
                    'transaction_date',
                    'transaction_type',
                    'document_type',
                    'quantity',
                    'product_name',
                    'product_sku',
                    'batch_number',
                    'quality_status',
                ]
            ],
            'summary' => [
                '*' => [
                    'product_name',
                    'product_sku',
                    'total_in',
                    'total_out',
                    'net_movement',
                ]
            ],
            'period' => [
                'from',
                'to'
            ],
            'warehouse_id',
            'order_scheme_active'
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['order_scheme_active']);
        $this->assertEquals($this->warehouse->id, $responseData['warehouse_id']);
    }

    public function test_can_get_reservation_report(): void
    {
        // Arrange
        $filters = [
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-31',
            'reservation_type' => 'order',
            'status' => 'reserved',
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/reservations?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'reservations' => [
                '*' => [
                    'id',
                    'reserved_at',
                    'status',
                    'reservation_type',
                    'reserved_quantity',
                    'product_name',
                    'product_sku',
                    'batch_number',
                    'quality_status',
                ]
            ],
            'summary' => [
                'by_type' => [
                    '*' => [
                        'type',
                        'total_reservations',
                        'total_quantity',
                        'active_reservations',
                    ]
                ],
                'by_status' => [
                    '*' => [
                        'status',
                        'count',
                        'total_quantity',
                    ]
                ],
                'by_product' => [
                    '*' => [
                        'product_name',
                        'product_sku',
                        'total_reserved',
                        'reservations_count',
                    ]
                ],
                'totals' => [
                    'total_reservations',
                    'total_quantity',
                    'active_quantity',
                ]
            ],
            'warehouse_id',
            'order_scheme_active'
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['order_scheme_active']);
        $this->assertGreaterThan(0, $responseData['summary']['totals']['total_reservations']);
    }

    public function test_can_get_order_scheme_analytics(): void
    {
        // Arrange
        $filters = [
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-31',
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/analytics?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'analytics' => [
                'document_statistics' => [
                    'receipt_orders' => [
                        'total',
                        'held',
                        'quantity',
                        'cost',
                    ],
                    'issue_orders' => [
                        'total',
                        'held',
                        'quantity',
                        'cost',
                    ],
                    'write_off_reasons' => [
                        '*' => [
                            'write_off_reason',
                            'count',
                            'quantity',
                            'cost',
                        ]
                    ]
                ],
                'reservation_efficiency' => [
                    'total_reservations',
                    'active_reservations',
                    'shipped_reservations',
                    'cancelled_reservations',
                    'fulfillment_rate',
                    'cancellation_rate',
                ],
                'inventory_turnover' => [
                    'top_products' => [
                        '*' => [
                            'product_name',
                            'product_sku',
                            'total_in',
                            'total_out',
                        ]
                    ]
                ],
                'quality_control',
                'expiry_management' => [
                    'expired_items',
                    'expiring_week',
                    'expiring_month',
                ]
            ],
            'warehouse_id',
            'order_scheme_active'
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['order_scheme_active']);
        $this->assertGreaterThanOrEqual(2, $responseData['analytics']['document_statistics']['receipt_orders']['total']);
        $this->assertGreaterThanOrEqual(1, $responseData['analytics']['document_statistics']['issue_orders']['total']);
    }

    public function test_can_get_inventory_report(): void
    {
        // Arrange
        $filters = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quality_status' => 'good',
            'include_expired' => false,
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/inventory?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'inventory' => [
                '*' => [
                    'warehouse_item_id',
                    'product_id',
                    'product_name',
                    'product_sku',
                    'quantity',
                    'available_quantity',
                    'reserved_quantity',
                    'batch_number',
                    'quality_status',
                    'expiry_status',
                ]
            ],
            'product_summary' => [
                '*' => [
                    'product_name',
                    'product_sku',
                    'total_quantity',
                    'available_quantity',
                    'reserved_quantity',
                    'batches_count',
                ]
            ],
            'total_summary' => [
                'total_items',
                'total_products',
                'total_quantity',
                'available_quantity',
                'reserved_quantity',
                'total_value',
            ],
            'warehouse_id',
            'order_scheme_active'
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['order_scheme_active']);
        $this->assertGreaterThan(0, $responseData['total_summary']['total_items']);
        $this->assertEquals(100, $responseData['total_summary']['total_quantity']);
        $this->assertEquals(80, $responseData['total_summary']['available_quantity']);
        $this->assertEquals(20, $responseData['total_summary']['reserved_quantity']);
    }

    public function test_reports_handle_non_order_scheme_warehouse(): void
    {
        // Arrange - создаем склад без ордерной схемы
        $regularWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $filters = [
            'warehouse_id' => $regularWarehouse->id,
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-31',
        ];

        // Act - тестируем отчет по движению
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/stock-movement?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertFalse($responseData['order_scheme_active']);

        // Act - тестируем отчет по резервам
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/reservations?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertFalse($responseData['order_scheme_active']);
        $this->assertEmpty($responseData['reservations']);

        // Act - тестируем аналитику
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/analytics?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertFalse($responseData['order_scheme_active']);
    }

    public function test_validates_required_warehouse_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/stock-movement');

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['warehouse_id']);
    }

    public function test_validates_date_range(): void
    {
        // Arrange
        $filters = [
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-31',
            'date_to' => '2024-01-01', // Дата окончания раньше начала
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/stock-movement?' . http_build_query($filters));

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['date_to']);
    }

    public function test_filters_by_product_id(): void
    {
        // Arrange
        $anotherProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $filters = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $anotherProduct->id,
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-31',
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/reports/stock-movement?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $responseData = $response->json();
        
        // Должны получить пустой результат, так как транзакции только для основного товара
        $this->assertEmpty($responseData['movements']);
        $this->assertEmpty($responseData['summary']);
    }
}
