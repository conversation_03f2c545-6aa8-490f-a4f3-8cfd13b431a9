<?php

namespace Tests\Feature;

use App\Events\CabinetCreated;
use App\Listeners\CreateCabinetSettings;
use App\Models\Cabinet;
use App\Models\User;
use App\Traits\HasOrderedUuid;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CabinetCreationCacheTest extends TestCase
{
    use HasOrderedUuid;
    use RefreshDatabase;

    public function test_cache_is_cleared_after_cabinet_creation(): void
    {
        // Создаем пользователя
        $user = User::factory()->create();

        // Создаем кабинет
        $cabinet = Cabinet::factory()->create([
            'user_id' => $user->id
        ]);

        // Устанавливаем кеш для пользователя
        Cache::put("permissions_{$user->id}", ['test' => 'data'], 600);
        Cache::put("employees_{$user->id}", ['test' => 'data'], 600);

        // Проверяем, что кеш существует
        $this->assertTrue(Cache::has("permissions_{$user->id}"));
        $this->assertTrue(Cache::has("employees_{$user->id}"));

        // Фейкаем очередь, чтобы job выполнялся синхронно
        Queue::fake();

        // Создаем событие
        $event = new CabinetCreated($cabinet->id, $user);

        // Создаем слушатель и выполняем его
        $listener = new CreateCabinetSettings();
        $listener->handle($event);

        // Проверяем, что кеш был очищен
        $this->assertFalse(Cache::has("permissions_{$user->id}"));
        $this->assertFalse(Cache::has("employees_{$user->id}"));
    }
}
