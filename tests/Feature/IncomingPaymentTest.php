<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\IncomingPayment;
use App\Models\LegalEntity;
use App\Models\SalesChannel;
use App\Models\Status;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class IncomingPaymentTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_incoming_payments_list(): void
    {
        // Создаем тестовые платежи
        IncomingPayment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем платеж для другого кабинета
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/incoming-payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                    'data' => [
                        '*' => [

                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'status_id',
                        'number',
                        'date_from',
                        'held',
                        'legal_entity_id',
                        'contractor_id',
                        'sales_channel_id',
                        'sum',
                        'included_vat',
                        'comment',
                        'incoming_number',
                        'incoming_date',
                        'currency_id',
                        'currency_value',
                        'bounded_sum',
                        'not_bounded_sum',
                        'is_imported',
                        'employee_id',
                        'department_id',
                        'is_default',
                ],
                        ],
            ]);

        // Проверяем что получили только платежи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $payment) {
            $this->assertEquals($this->cabinet->id, $payment['cabinet_id']);
        }
    }

    public function test_cannot_get_incoming_payments_without_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/incoming-payments');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_incoming_payments_with_invalid_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/incoming-payments?' . http_build_query([
            'cabinet_id' => 'invalid-uuid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_access_other_cabinet_incoming_payments(): void
    {
        // Создаем платежи для другого кабинета
        IncomingPayment::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/incoming-payments?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/incoming-payments?' . http_build_query([
            'cabinet_id' => 'invalid-uuid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id'
            ]);
    }

    public function test_can_create_incoming_payment_with_required_fields(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('incoming_payments', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id'],
            'legal_entity_id' => $data['legal_entity_id'],
            'contractor_id' => $data['contractor_id'],
            'currency_id' => $data['currency_id'],
        ]);

        // Проверяем создание записи в таблице documents
        $this->assertDatabaseHas('documents', [
            'documentable_id' => $response->json('id'),
            'documentable_type' => 'incoming_payments',
            'cabinet_id' => $data['cabinet_id']
        ]);
    }

    public function test_can_create_incoming_payment_with_all_fields(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'number' => $this->faker->unique()->numerify('IP-####'),
            'date_from' => now()->format('Y-m-d'),
            'status_id' => $status->id,
            'held' => true,
            'sales_channel_id' => $salesChannel->id,
            'sum' => $this->faker->randomFloat(2, 100, 10000),
            'included_vat' => $this->faker->randomFloat(2, 10, 1000),
            'comment' => $this->faker->sentence,
            'currency_value' => $this->faker->randomFloat(2, 1, 100),
            'incoming_number' => $this->faker->unique()->numerify('IN-####'),
            'incoming_date' => now()->format('Y-m-d'),
            'is_imported' => false,
            'is_common' => true,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        unset($data['number']);
        $this->assertDatabaseHas(
            'incoming_payments',
            array_merge(['id' => $response->json('id')], $data)
        );
    }

    public function test_cannot_create_incoming_payment_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/incoming-payments', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'department_id',
                'employee_id',
                'legal_entity_id',
                'contractor_id',
                'currency_id',
            ]);
    }

    public function test_cannot_create_incoming_payment_with_invalid_data(): void
    {
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'currency_id' => 'invalid-uuid',
            'sum' => 'not-a-number',
            'included_vat' => 'not-a-number',
            'date_from' => 'invalid-date',
            'incoming_date' => 'invalid-date',
            'currency_value' => 'not-a-decimal',
            'held' => 'not-a-boolean',
            'is_imported' => 'not-a-boolean',
            'is_common' => 'not-a-boolean',
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'department_id',
                'employee_id',
                'legal_entity_id',
                'contractor_id',
                'currency_id',
                'sum',
                'included_vat',
                'date_from',
                'incoming_date',
                'currency_value',
                'held',
                'is_imported',
                'is_common',
            ]);
    }

    public function test_cannot_create_incoming_payment_in_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(403);
    }

    public function test_cannot_create_incoming_payment_with_department_from_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'departments not found.'
            ]);
    }

    public function test_cannot_create_incoming_payment_with_legal_entity_from_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'legal_entities not found.'
            ]);
    }

    public function test_cannot_create_incoming_payment_with_contractor_from_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'contractors not found.'
            ]);
    }

    public function test_cannot_create_incoming_payment_with_currency_from_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'cabinet_currencies not found.'
            ]);
    }

    public function test_cannot_create_incoming_payment_with_sales_channel_from_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'sales_channel_id' => $salesChannel->id
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'sales_channels not found.'
            ]);
    }

    public function test_cannot_create_incoming_payment_with_status_from_other_cabinet(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'status_id' => $status->id
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'statuses not found.'
            ]);
    }

    public function test_creates_incoming_payment_with_auto_generated_number(): void
    {
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/incoming-payments', $data);

        $response->assertStatus(201);

        $payment = IncomingPayment::find($response->json('id'));
        $this->assertNotNull($payment->number);
    }

    public function test_can_update_incoming_payment(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'status_id' => $status->id,
            'held' => true,
            'sales_channel_id' => $salesChannel->id,
            'sum' => $this->faker->randomFloat(2, 100, 10000),
            'included_vat' => $this->faker->randomFloat(2, 10, 1000),
            'comment' => $this->faker->sentence,
            'currency_value' => $this->faker->randomFloat(2, 1, 100),
            'incoming_number' => $this->faker->unique()->numerify('IN-####'),
            'incoming_date' => now()->format('Y-m-d'),
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('incoming_payments', array_merge(
            ['id' => $payment->id],
            $updateData
        ));
    }

    public function test_cannot_update_incoming_payment_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_incoming_payment_with_invalid_data(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $invalidData = [
            'cabinet_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'currency_id' => 'invalid-uuid',
            'sum' => 'not-a-number',
            'included_vat' => 'not-a-number',
            'date_from' => 'invalid-date',
            'incoming_date' => 'invalid-date',
            'currency_value' => 'not-a-decimal',
            'held' => 'not-a-boolean',
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'department_id',
                'employee_id',
                'legal_entity_id',
                'contractor_id',
                'currency_id',
                'sum',
                'included_vat',
                'date_from',
                'incoming_date',
                'currency_value',
                'held',
            ]);
    }

    public function test_cannot_update_non_existent_incoming_payment(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/" . $this->faker->uuid(), $updateData);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_incoming_payment_with_department_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherDepartment = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $otherDepartment->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'departments not found.'
            ]);
    }

    public function test_cannot_update_incoming_payment_with_legal_entity_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $otherLegalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'legal_entities not found.'
            ]);
    }

    public function test_cannot_update_incoming_payment_with_contractor_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherContractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $otherContractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'contractors not found.'
            ]);
    }

    public function test_cannot_update_incoming_payment_with_currency_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherCurrency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $otherCurrency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'cabinet_currencies not found.'
            ]);
    }

    public function test_cannot_update_incoming_payment_with_sales_channel_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherSalesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'sales_channel_id' => $otherSalesChannel->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'sales_channels not found.'
            ]);
    }

    public function test_cannot_update_incoming_payment_with_status_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherStatus = Status::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'status_id' => $otherStatus->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'statuses not found.'
            ]);
    }

    public function test_can_update_incoming_payment_with_minimal_required_fields(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('incoming_payments', array_merge(
            ['id' => $payment->id],
            $updateData
        ));
    }

    public function test_can_update_incoming_payment_with_all_fields(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'status_id' => $status->id,
            'held' => true,
            'sales_channel_id' => $salesChannel->id,
            'sum' => $this->faker->randomFloat(2, 100, 10000),
            'included_vat' => $this->faker->randomFloat(2, 10, 1000),
            'comment' => $this->faker->sentence,
            'currency_value' => $this->faker->randomFloat(2, 1, 100),
            'incoming_number' => $this->faker->unique()->numerify('IN-####'),
            'incoming_date' => now()->format('Y-m-d'),
            'is_common' => true,
            'date_from' => now()->format('Y-m-d H:i:s'),
        ];

        // Act
        $response = $this->putJson("/api/internal/incoming-payments/{$payment->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('incoming_payments', array_merge(
            ['id' => $payment->id],
            $updateData
        ));
    }

    public function test_can_show_incoming_payment(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'status_id' => $status->id,
            'sales_channel_id' => $salesChannel->id,
            'held' => true,
            'sum' => 1000.50,
            'included_vat' => 100.50,
            'comment' => 'Test comment',
            'currency_value' => 1.5,
            'incoming_number' => 'IN-1234',
            'incoming_date' => now()->format('Y-m-d'),
            'is_common' => true,
            'date_from' => now()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->getJson("/api/internal/incoming-payments/{$payment->id}");

        // Assert
        $response->assertStatus(200);
    }

    public function test_cannot_show_incoming_payment_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/incoming-payments/{$payment->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_incoming_payment(): void
    {
        // Act
        $response = $this->getJson("/api/internal/incoming-payments/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_incoming_payment(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/incoming-payments/{$payment->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertSoftDeleted('incoming_payments', [
            'id' => $payment->id
        ]);
    }

    public function test_cannot_delete_incoming_payment_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/incoming-payments/{$payment->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('incoming_payments', [
            'id' => $payment->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_incoming_payment(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/incoming-payments/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }
}
