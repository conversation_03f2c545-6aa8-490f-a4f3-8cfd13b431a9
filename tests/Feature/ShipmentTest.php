<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\FilterConditionEnum;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\File;
use App\Models\LegalEntity;
use App\Models\SalesChannel;
use App\Models\Shipment;
use App\Models\Status;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Str;
use Tests\TestCase;

class ShipmentTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $anotherCabinet;
    private Employee $employee;
    private Department $department;
    private Warehouse $warehouse;
    private Contractor $contractor;
    private LegalEntity $legalEntity;
    private Status $status;
    private SalesChannel $salesChannel;
    private File $file;
    private Shipment $shipment;
    private CabinetCurrency $currency;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->anotherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Добавляем создание currency
        $this->currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_shipments_list(): void
    {
        $shipments = Shipment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'status_id' => $this->status->id,
            'sales_channel_id' => $this->salesChannel->id
        ]);

        $response = $this->getJson("/api/internal/shipments?cabinet_id={$this->cabinet->id}");

        $response->assertOk()
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'data' => [[
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'cabinet_id',
                    'employee_id',
                    'department_id',
                    'is_common',
                    'payment_status',
                    'number',
                    'date_from',
                    'status_id',
                    'held',
                    'legal_entity_id',
                    'contractor_id',
                    'warehouse_id',
                    'sales_channel_id',
                    'currency_id',
                    'currency_value',
                    'consignee_id',
                    'transporter_id',
                    'cargo_name',
                    'shipper_instructions',
                    'venicle',
                    'venicle_number',
                    'total_seats',
                    'goverment_contract_id',
                    'comment',
                    'price_includes_vat',
                    'overhead_cost',
                    'total_cost',
                    'profit',
                    'total_price',
                ]],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
    }

    public function test_cannot_get_shipments_without_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/shipments');

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_shipments_with_invalid_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/shipments?cabinet_id=invalid-uuid');

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_shipments_from_other_cabinet(): void
    {
        Shipment::factory()->count(3)->create([
            'cabinet_id' => $this->anotherCabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?cabinet_id={$this->anotherCabinet->id}");

        $response->assertForbidden();
    }

    public function test_index_filter_by_warehouses(): void
    {
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $otherWarehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'value' => [$this->warehouse->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_sales_channels(): void
    {
        $otherSalesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $this->salesChannel->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $otherSalesChannel->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'value' => [$this->salesChannel->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_employee_owners(): void
    {
        $otherEmployee = Employee::factory()->create();

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $this->employee->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $otherEmployee->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_department_owners(): void
    {
        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $this->department->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $otherDepartment->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_contractors(): void
    {
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $otherContractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$this->contractor->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_statuses(): void
    {
        $otherStatus = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $this->status->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $otherStatus->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$this->status->id],
                    'condition' => 'IN'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_search(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'number' => 'TEST123',
            'comment' => 'Test comment'
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'number' => 'OTHER456',
            'comment' => 'Other comment'
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'TEST'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_period(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'created_at' => '2024-03-15 12:00:00'
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'created_at' => '2024-03-20 12:00:00'
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'period' => [
                    'from' => '15.03.2024 00:00',
                    'to' => '16.03.2024 23:59'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_pagination_and_sorting(): void
    {
        Shipment::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10,
            'sortField' => 'created_at',
            'sortDirection' => 'desc'
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $data = $response->json('data');
        $this->assertCount(5, $data);
        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertEquals(15, $response->json('meta.total'));
    }

    public function test_index_filter_by_warehouses_in(): void
    {
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $otherWarehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'value' => [$this->warehouse->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_warehouses_not_in(): void
    {
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $otherWarehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'value' => [$this->warehouse->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_warehouses_empty(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    public function test_index_filter_by_warehouses_not_empty(): void
    {
        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(2, $data);
    }

    public function test_index_filter_by_employee_owners_in(): void
    {
        $otherEmployee = Employee::factory()->create();

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $this->employee->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $otherEmployee->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_employee_owners_not_in(): void
    {
        $otherEmployee = Employee::factory()->create();

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $this->employee->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $otherEmployee->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_employee_owners_empty(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $this->employee->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    public function test_index_filter_by_employee_owners_not_empty(): void
    {
        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'employee_id' => $this->employee->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(2, $data);
    }

    public function test_index_filter_by_department_owners_empty(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $this->department->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    public function test_index_filter_by_department_owners_not_empty(): void
    {
        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $this->department->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(2, $data);
    }

    public function test_index_filter_by_statuses_empty(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => null
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $this->status->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_statuses_not_empty(): void
    {
        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $this->status->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $this->status->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => 'NOT_EMPTY'
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(2, $data);
    }

    public function test_index_filter_by_is_common(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'is_common' => true
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'is_common' => false
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_is_held(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'held' => true
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'held' => false
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_held' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_department_owners_in(): void
    {
        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $this->department->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $otherDepartment->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_department_owners_not_in(): void
    {
        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $this->department->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'department_id' => $otherDepartment->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_sales_channels_in(): void
    {
        $otherSalesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $this->salesChannel->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $otherSalesChannel->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'value' => [$this->salesChannel->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_sales_channels_not_in(): void
    {
        $otherSalesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $this->salesChannel->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $otherSalesChannel->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'value' => [$this->salesChannel->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_sales_channels_empty(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => null
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $this->salesChannel->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_sales_channels_not_empty(): void
    {
        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => null
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'sales_channel_id' => $this->salesChannel->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_contractors_in(): void
    {
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $otherContractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$this->contractor->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_contractors_not_in(): void
    {
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $otherContractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$this->contractor->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_contractors_empty(): void
    {
        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    public function test_index_filter_by_contractors_not_empty(): void
    {
        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(2, $data);
    }

    public function test_index_filter_by_statuses_in(): void
    {
        $otherStatus = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $this->status->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $otherStatus->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$this->status->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_index_filter_by_statuses_not_in(): void
    {
        $otherStatus = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $nonMatchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $this->status->id
        ]);

        $matchingShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'contractor_id' => $this->contractor->id,
            'legal_entity_id' => $this->legalEntity->id,
            'status_id' => $otherStatus->id
        ]);

        $response = $this->getJson("/api/internal/shipments?" . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$this->status->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertOk();
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingShipment->id, $data[0]['id']);
    }

    public function test_can_create_shipment_with_minimum_fields(): void
    {
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $currency->id,
        ];

        $response = $this->postJson('/api/internal/shipments', $data);

        $response->assertCreated();

        $this->assertDatabaseHas('shipments', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $currency->id,
        ]);
    }

    public function test_can_create_shipment_with_all_fields(): void
    {
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $currency->id,
            'sales_channel_id' => $this->salesChannel->id,
            'status_id' => $this->status->id,
            'number' => 'TEST-001',
            'date_from' => now()->format('Y-m-d'),
            'held' => true,
            'currency_value' => '1.5',
            'consignee_id' => $this->contractor->id,
            'transporter_id' => $this->contractor->id,
            'cargo_name' => 'Test cargo',
            'shipper_instructions' => 'Test instructions',
            'venicle' => 'Test vehicle',
            'venicle_number' => 'A123BC',
            'total_seats' => 10,
            'goverment_contract_id' => 'GOV-001',
            'comment' => 'Test comment',
            'price_includes_vat' => true,
            'overhead_cost' => 100.50,
            'files' => [$file->id],
            'delivery_info' => [
                'comment' => 'Delivery comment',
                'post_code' => '123456',
                'country' => 'Test Country',
                'region' => 'Test Region',
                'city' => 'Test City',
                'street' => 'Test Street',
                'house' => '1',
                'office' => '2',
                'other' => 'Additional info'
            ]
        ];

        $response = $this->postJson('/api/internal/shipments', $data);

        $response->assertCreated();
        $id = $response->json('id');
        $this->assertDatabaseHas('shipments', [
            'id' => $id,
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $currency->id,
            'sales_channel_id' => $this->salesChannel->id,
            'status_id' => $this->status->id,
            'date_from' => now()->format('Y-m-d 00:00:00'),
            'held' => true,
            'currency_value' => '1.5',
            'consignee_id' => $this->contractor->id,
            'transporter_id' => $this->contractor->id,
            'cargo_name' => 'Test cargo',
            'shipper_instructions' => 'Test instructions',
            'venicle' => 'Test vehicle',
            'venicle_number' => 'A123BC',
            'total_seats' => 10,
            'goverment_contract_id' => 'GOV-001',
            'comment' => 'Test comment',
            'price_includes_vat' => true,
            'overhead_cost' => '100.5',
        ]);

        $this->assertDatabaseHas('shipment_delivery_infos', [
            'shipment_id' => $id,
            'comment' => 'Delivery comment',
            'post_code' => '123456',
            'country' => 'Test Country',
            'region' => 'Test Region',
            'city' => 'Test City',
            'street' => 'Test Street',
            'house' => '1',
            'office' => '2',
            'other' => 'Additional info'
        ]);
    }

    public function test_shipment_validation_rules(): void
    {
        $response = $this->postJson('/api/internal/shipments', []);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'employee_id',
                'department_id',
                'legal_entity_id',
                'contractor_id',
                'warehouse_id',
                'currency_id'
            ]);

        $response = $this->postJson('/api/internal/shipments', [
            'cabinet_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'warehouse_id' => 'invalid-uuid',
            'currency_id' => 'invalid-uuid',
            'sales_channel_id' => 'invalid-uuid',
            'status_id' => 'invalid-uuid',
            'consignee_id' => 'invalid-uuid',
            'transporter_id' => 'invalid-uuid',
            'currency_value' => 'not-a-number',
            'total_seats' => 'not-a-number',
            'overhead_cost' => 'not-a-number',
            'date_from' => 'invalid-date',
            'delivery_info' => [
                'comment' => str_repeat('a', 256),
                'post_code' => str_repeat('a', 256),
                'country' => str_repeat('a', 256),
                'region' => str_repeat('a', 256),
                'city' => str_repeat('a', 256),
                'street' => str_repeat('a', 256),
                'house' => str_repeat('a', 256),
                'office' => str_repeat('a', 256),
                'other' => str_repeat('a', 256)
            ]
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'employee_id',
                'department_id',
                'legal_entity_id',
                'contractor_id',
                'warehouse_id',
                'currency_id',
                'sales_channel_id',
                'status_id',
                'consignee_id',
                'transporter_id',
                'currency_value',
                'total_seats',
                'overhead_cost',
                'date_from',
                'delivery_info.comment',
                'delivery_info.post_code',
                'delivery_info.country',
                'delivery_info.region',
                'delivery_info.city',
                'delivery_info.street',
                'delivery_info.house',
                'delivery_info.office',
                'delivery_info.other'
            ]);
    }

    public function test_cannot_create_shipment_in_other_cabinet(): void
    {
        $otherCabinet = Cabinet::factory()->create();
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $data = [
            'cabinet_id' => $otherCabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $currency->id
        ];

        $response = $this->postJson('/api/internal/shipments', $data);

        $response->assertForbidden();
    }

    public function test_update_shipment_regenerates_number_on_legal_entity_change(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id,
            'number' => 'OLD-NUMBER'
        ]);

        $newLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $newLegalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id,
            'number' => 'OLD-NUMBER'
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertNoContent();

        $updatedShipment = Shipment::find($shipment->id);
        $this->assertNotEquals('OLD-NUMBER', $updatedShipment->number);
    }

    public function test_update_shipment_triggers_fifo_job_on_date_change(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        Queue::fake();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id,
            'date_from' => now()->addDay()->format('Y-m-d')
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertNoContent();
        Queue::assertPushed(HandleFifoJob::class);
    }

    public function test_update_shipment_delivery_info(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $deliveryInfo = [
            'comment' => 'Test delivery comment',
            'post_code' => '123456',
            'country' => 'Test country',
            'region' => 'Test region',
            'city' => 'Test city',
            'street' => 'Test street',
            'house' => '1',
            'office' => '123',
            'other' => 'Additional info'
        ];

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id,
            'delivery_info' => $deliveryInfo
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('shipment_delivery_infos', [
            'shipment_id' => $shipment->id,
            'comment' => 'Test delivery comment',
            'post_code' => '123456'
        ]);
    }

    public function test_update_shipment_with_invalid_delivery_info(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id,
            'delivery_info' => [
                'comment' => str_repeat('a', 256),
                'post_code' => str_repeat('1', 256),
                'country' => str_repeat('b', 256),
                'city' => str_repeat('c', 256)
            ]
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'delivery_info.comment',
                'delivery_info.post_code',
                'delivery_info.country',
                'delivery_info.city'
            ]);
    }

    public function test_cannot_update_shipment_from_another_cabinet(): void
    {
        $anotherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id
        ];

        $response = $this->putJson("/api/internal/shipments/{$anotherCabinetShipment->id}", $data);

        $response->assertNotFound();
    }

    public function test_cannot_update_non_existent_shipment(): void
    {
        $nonExistentId = Str::uuid();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id
        ];

        $response = $this->putJson("/api/internal/shipments/{$nonExistentId}", $data);

        $response->assertNotFound();
    }

    public function test_cannot_update_shipment_with_invalid_uuid(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $data = [
            'cabinet_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'warehouse_id' => 'invalid-uuid',
            'currency_id' => 'invalid-uuid'
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'employee_id',
                'department_id',
                'legal_entity_id',
                'contractor_id',
                'warehouse_id',
                'currency_id'
            ]);
    }

    public function test_update_shipment_validation(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $data = [
            'cabinet_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'warehouse_id' => 'invalid-uuid',
            'currency_id' => 'invalid-uuid',
            'sales_channel_id' => 'invalid-uuid',
            'status_id' => 'invalid-uuid',
            'consignee_id' => 'invalid-uuid',
            'transporter_id' => 'invalid-uuid',
            'currency_value' => 'not-a-number',
            'total_seats' => 'not-a-number',
            'overhead_cost' => 'not-a-number',
            'date_from' => 'invalid-date',
            'delivery_info' => [
                'comment' => str_repeat('a', 256),
                'post_code' => str_repeat('1', 256),
                'country' => str_repeat('b', 256),
                'city' => str_repeat('c', 256),
                'street' => str_repeat('d', 256),
                'house' => str_repeat('e', 256),
                'office' => str_repeat('f', 256),
                'other' => str_repeat('g', 256)
            ]
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'employee_id',
                'department_id',
                'legal_entity_id',
                'contractor_id',
                'warehouse_id',
                'currency_id',
                'sales_channel_id',
                'status_id',
                'consignee_id',
                'transporter_id',
                'currency_value',
                'total_seats',
                'overhead_cost',
                'date_from',
                'delivery_info.comment',
                'delivery_info.post_code',
                'delivery_info.country',
                'delivery_info.city',
                'delivery_info.street',
                'delivery_info.house',
                'delivery_info.office',
                'delivery_info.other'
            ]);
    }

    public function test_can_update_shipment(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id,
            'number' => 'OLD-NUMBER',
            'comment' => 'Old comment'
        ]);

        $newLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $newWarehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $newContractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $file = File::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $newLegalEntity->id,
            'contractor_id' => $newContractor->id,
            'warehouse_id' => $newWarehouse->id,
            'currency_id' => $this->currency->id,
            'sales_channel_id' => $this->salesChannel->id,
            'status_id' => $this->status->id,
            'date_from' => now()->format('Y-m-d'),
            'held' => true,
            'currency_value' => '1.5',
            'consignee_id' => $newContractor->id,
            'transporter_id' => $newContractor->id,
            'cargo_name' => 'Updated cargo',
            'shipper_instructions' => 'Updated instructions',
            'venicle' => 'Updated vehicle',
            'venicle_number' => 'X999XX',
            'total_seats' => 20,
            'goverment_contract_id' => 'GOV-002',
            'comment' => 'Updated comment',
            'price_includes_vat' => true,
            'overhead_cost' => 200.50,
            'files' => [$file->id],
            'delivery_info' => [
                'comment' => 'Updated delivery comment',
                'post_code' => '654321',
                'country' => 'Updated Country',
                'region' => 'Updated Region',
                'city' => 'Updated City',
                'street' => 'Updated Street',
                'house' => '2',
                'office' => '3',
                'other' => 'Updated additional info'
            ]
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('shipments', [
            'id' => $shipment->id,
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $newLegalEntity->id,
            'contractor_id' => $newContractor->id,
            'warehouse_id' => $newWarehouse->id,
            'currency_id' => $this->currency->id,
            'sales_channel_id' => $this->salesChannel->id,
            'status_id' => $this->status->id,
            'held' => true,
            'currency_value' => '1.5',
            'consignee_id' => $newContractor->id,
            'transporter_id' => $newContractor->id,
            'cargo_name' => 'Updated cargo',
            'shipper_instructions' => 'Updated instructions',
            'venicle' => 'Updated vehicle',
            'venicle_number' => 'X999XX',
            'total_seats' => 20,
            'goverment_contract_id' => 'GOV-002',
            'comment' => 'Updated comment',
            'price_includes_vat' => true,
            'overhead_cost' => '200.5'
        ]);

        $this->assertDatabaseHas('shipment_delivery_infos', [
            'shipment_id' => $shipment->id,
            'comment' => 'Updated delivery comment',
            'post_code' => '654321',
            'country' => 'Updated Country',
            'region' => 'Updated Region',
            'city' => 'Updated City',
            'street' => 'Updated Street',
            'house' => '2',
            'office' => '3',
            'other' => 'Updated additional info'
        ]);

        $this->assertDatabaseHas('file_relations', [
            'file_id' => $file->id,
            'related_id' => $shipment->id,
            'related_type' => 'shipments'
        ]);
    }

    public function test_can_update_shipment_with_minimum_fields(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id,
            'number' => 'OLD-NUMBER',
            'comment' => 'Old comment',
            'sales_channel_id' => $this->salesChannel->id,
            'status_id' => $this->status->id,
            'held' => true,
            'currency_value' => '1.50',
            'consignee_id' => $this->contractor->id,
            'transporter_id' => $this->contractor->id,
            'cargo_name' => 'Test cargo',
            'shipper_instructions' => 'Test instructions',
            'venicle' => 'Test vehicle',
            'venicle_number' => 'A123BC',
            'total_seats' => 10,
            'goverment_contract_id' => 'GOV-001',
            'price_includes_vat' => true,
            'overhead_cost' => 100.50
        ]);

        $newLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $newLegalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id
        ];

        $response = $this->putJson("/api/internal/shipments/{$shipment->id}", $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('shipments', [
            'id' => $shipment->id,
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => $newLegalEntity->id,
            'contractor_id' => $this->contractor->id,
            'warehouse_id' => $this->warehouse->id,
            'currency_id' => $this->currency->id
        ]);
    }

    public function test_can_delete_shipment(): void
    {
        Bus::fake();

        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $response = $this->deleteJson("/api/internal/shipments/{$shipment->id}");

        $response->assertNoContent();

        Bus::assertBatched(function ($batch) {
            return $batch->jobs->contains(function ($job) {
                return $job instanceof HandleFifoJob;
            });
        });
    }

    public function test_cannot_delete_shipment_from_another_cabinet(): void
    {
        $anotherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $response = $this->deleteJson("/api/internal/shipments/{$anotherCabinetShipment->id}");

        $response->assertNotFound();

        $this->assertDatabaseHas('shipments', [
            'id' => $anotherCabinetShipment->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_shipment(): void
    {
        $nonExistentId = Str::uuid();

        $response = $this->deleteJson("/api/internal/shipments/{$nonExistentId}");

        $response->assertNotFound();
    }

    public function test_can_bulk_delete_shipments(): void
    {
        Bus::fake();

        $shipments = Shipment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $response = $this->deleteJson('/api/internal/shipments/bulk-delete', [
            'ids' => $shipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNoContent();
    }

    public function test_cannot_bulk_delete_with_empty_ids(): void
    {
        $response = $this->deleteJson('/api/internal/shipments/bulk-delete', [
            'ids' => []
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_cannot_bulk_delete_shipments_from_another_cabinet(): void
    {
        $anotherCabinetShipments = Shipment::factory()->count(2)->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $response = $this->deleteJson('/api/internal/shipments/bulk-delete', [
            'ids' => $anotherCabinetShipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNotFound();

        foreach ($anotherCabinetShipments as $shipment) {
            $this->assertDatabaseHas('shipments', [
                'id' => $shipment->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_can_show_shipment(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id,
            'status_id' => $this->status->id
        ]);

        $response = $this->getJson("/api/internal/shipments/{$shipment->id}");

        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'deleted_at',
                'cabinet_id',
                'employee_id',
                'department_id',
                'is_common',
                'payment_status',
                'number',
                'date_from',
                'status_id',
                'held',
                'legal_entity_id',
                'contractor_id',
                'warehouse_id',
                'sales_channel_id',
                'currency_id',
                'currency_value',
                'consignee_id',
                'transporter_id',
                'cargo_name',
                'shipper_instructions',
                'venicle',
                'venicle_number',
                'total_seats',
                'goverment_contract_id',
                'comment',
                'price_includes_vat',
                'overhead_cost',
                'total_cost',
                'profit',
                'total_price',
                'status',
                'contractor',
                'warehouse',
                'files',
                'delivery_info'
            ]);
    }

    public function test_cannot_show_shipment_from_another_cabinet(): void
    {
        $anotherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $response = $this->getJson("/api/internal/shipments/{$anotherCabinetShipment->id}");

        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_shipment(): void
    {
        $nonExistentId = Str::uuid();

        $response = $this->getJson("/api/internal/shipments/{$nonExistentId}");

        $response->assertNotFound();
    }

    public function test_can_bulk_held_shipments(): void
    {
        $shipments = Shipment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id,
            'held' => false
        ]);

        $response = $this->patchJson('/api/internal/shipments/bulk-held', [
            'ids' => $shipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNoContent();

        foreach ($shipments as $shipment) {
            $this->assertDatabaseHas('shipments', [
                'id' => $shipment->id,
                'held' => true
            ]);
        }
    }

    public function test_can_bulk_unheld_shipments(): void
    {
        $shipments = Shipment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id,
            'held' => true
        ]);

        $response = $this->patchJson('/api/internal/shipments/bulk-unheld', [
            'ids' => $shipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNoContent();

        foreach ($shipments as $shipment) {
            $this->assertDatabaseHas('shipments', [
                'id' => $shipment->id,
                'held' => false
            ]);
        }
    }

    public function test_cannot_bulk_held_shipments_from_another_cabinet(): void
    {
        $anotherCabinetShipments = Shipment::factory()->count(2)->create([
            'cabinet_id' => $this->anotherCabinet->id,
            'held' => false
        ]);

        $response = $this->patchJson('/api/internal/shipments/bulk-held', [
            'ids' => $anotherCabinetShipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNotFound();

        foreach ($anotherCabinetShipments as $shipment) {
            $this->assertDatabaseHas('shipments', [
                'id' => $shipment->id,
                'held' => false
            ]);
        }
    }

    public function test_can_bulk_copy_shipments(): void
    {
        $shipments = Shipment::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'legal_entity_id' => $this->legalEntity->id,
            'contractor_id' => $this->contractor->id,
            'currency_id' => $this->currency->id
        ]);

        $response = $this->postJson('/api/internal/shipments/bulk-copy', [
            'ids' => $shipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNoContent();

        $this->assertEquals(4, Shipment::count());

        $this->assertEquals(
            $shipments->count(),
            Shipment::where('cabinet_id', $this->cabinet->id)
                ->where('held', false)
                ->whereNotIn('id', $shipments->pluck('id'))
                ->count()
        );
    }

    public function test_cannot_bulk_copy_shipments_from_another_cabinet(): void
    {
        $anotherCabinetShipments = Shipment::factory()->count(2)->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $initialCount = Shipment::count();

        $response = $this->postJson('/api/internal/shipments/bulk-copy', [
            'ids' => $anotherCabinetShipments->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertNotFound();

        // Проверяем что количество отгрузок не изменилось
        $this->assertEquals($initialCount, Shipment::count());
    }

    public function test_cannot_bulk_copy_with_empty_ids(): void
    {
        $response = $this->postJson('/api/internal/shipments/bulk-copy', [
            'ids' => [],
            'cabinet_id' => $this->cabinet->id
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['ids']);
    }
}
