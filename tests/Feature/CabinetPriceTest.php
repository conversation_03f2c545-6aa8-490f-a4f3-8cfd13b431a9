<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\CabinetPriceEnum;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetPrice;
use App\Models\CabinetSettings;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CabinetPriceTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_cabinet_prices_list(): void
    {
        // Arrange
        CabinetPrice::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем цену для другого кабинета, чтобы убедиться что не получим её
        CabinetPrice::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/cabinets/prices?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'name',
                        'sort',

                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_prices(): void
    {
        // Arrange
        CabinetPrice::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/cabinets/prices?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/cabinets/prices?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Arrange
        CabinetPrice::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/cabinets/prices?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/cabinets/prices');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/cabinets/prices?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $price1 = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'A Price'
        ]);
        $price2 = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Z Price'
        ]);

        // Act
        $response = $this->getJson('/api/internal/cabinets/prices?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'asc',
            'fields' => ['id', 'name']
        ]));

        // Assert
        $response->assertStatus(200);

        $names = collect($response->json('data'))->pluck('name')->values();
        $expectedNames = collect(['A Price', 'Z Price']);

        $this->assertEquals($expectedNames, $names);
    }

    public function test_can_create_cabinet_price(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Price',
            'sort' => 1
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinets/prices', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinet_prices', [
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'sort' => $data['sort']
        ]);
    }

    public function test_can_create_cabinet_price_with_minimal_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'TEST'
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinets/prices', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinet_prices', [
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name']
        ]);
    }

    public function test_cannot_create_cabinet_price_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/cabinets/prices', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id'
            ]);
    }

    public function test_cannot_create_cabinet_price_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'name' => str_repeat('a', 256), // превышает максимальную длину
            'sort' => 'not-an-integer'
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinets/prices', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'sort'
            ]);
    }

    public function test_cannot_create_cabinet_price_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Price',
            'sort' => 1
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinets/prices', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('cabinet_prices', [
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name']
        ]);
    }

    public function test_can_update_cabinet_price(): void
    {
        // Arrange
        $cabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Old Name',
            'sort' => 1
        ]);

        $data = [
            'name' => 'Updated Name',
            'type' => CabinetPriceEnum::PURCHASE_PRICE->value,
            'sort' => 2
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/prices/{$cabinetPrice->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_prices', [
            'id' => $cabinetPrice->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Name',
            'sort' => 2
        ]);
    }

    public function test_can_update_cabinet_price_with_partial_data(): void
    {
        // Arrange
        $cabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Old Name',
            'sort' => 1
        ]);

        $data = [
            'name' => 'Updated Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/prices/{$cabinetPrice->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_prices', [
            'id' => $cabinetPrice->id,
            'name' => 'Updated Name',
            'sort' => 0
        ]);
    }

    public function test_cannot_update_cabinet_price_with_invalid_data(): void
    {
        // Arrange
        $cabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => str_repeat('a', 256), // превышает максимальную длину
            'type' => 'INVALID_TYPE', // неверный тип
            'sort' => 'not-an-integer'
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/prices/{$cabinetPrice->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'sort'
            ]);

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('cabinet_prices', [
            'id' => $cabinetPrice->id,
            'name' => $cabinetPrice->name,
            'sort' => $cabinetPrice->sort
        ]);
    }

    public function test_cannot_update_non_existent_cabinet_price(): void
    {
        // Act
        $response = $this->putJson("/api/internal/cabinets/prices/" . $this->faker->uuid, [
            'name' => 'New Name'
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_cabinet_price_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Original Name'
        ]);

        $data = [
            'name' => 'Updated Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/prices/{$otherCabinetPrice->id}", $data);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('cabinet_prices', [
            'id' => $otherCabinetPrice->id,
            'name' => 'Original Name'
        ]);
    }

    public function test_can_show_cabinet_price(): void
    {
        // Arrange
        $cabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Price',
            'sort' => 1
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/prices/{$cabinetPrice->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'name',
                'sort'
            ])
            ->assertJson([
                'id' => $cabinetPrice->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Price',
                'sort' => 1
            ]);
    }

    public function test_cannot_show_cabinet_price_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/prices/{$otherCabinetPrice->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_cabinet_price(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/prices/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_destroy_cabinet_price(): void
    {
        // Arrange
        $cabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/prices/{$cabinetPrice->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('cabinet_prices', [
            'id' => $cabinetPrice->id
        ]);
    }

    public function test_cannot_destroy_cabinet_price_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/prices/{$otherCabinetPrice->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('cabinet_prices', [
            'id' => $otherCabinetPrice->id
        ]);
    }

    public function test_cannot_destroy_non_existent_cabinet_price(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/cabinets/prices/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }
}
