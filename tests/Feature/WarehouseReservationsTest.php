<?php

namespace Tests\Feature;

use App\Contracts\Services\Internal\WarehouseReservationsServiceContract;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\CustomerOrder;
use App\Models\CustomerOrderItem;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use App\Models\WarehouseReservation;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseReservationsTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $reservationService;
    protected $employee;
    protected $cabinet;
    protected $otherCabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $warehouseItem;
    protected $customerOrder;
    protected $customerOrderItem;

    /**
     * @throws BindingResolutionException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        $this->reservationService = $this->app->make(WarehouseReservationsServiceContract::class);

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем склад с ордерной схемой
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем товар на складе для резервирования
        $this->warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'available_quantity' => 100,
            'reserved_quantity' => 0,
            'quality_status' => 'good',
        ]);

        // Создаем заказ клиента
        $this->customerOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->customerOrderItem = CustomerOrderItem::factory()->create([
            'order_id' => $this->customerOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
        ]);
    }

    public function test_can_get_reservations_list(): void
    {
        // Arrange
        $cabinetId = $this->cabinet->id;

        // Создаем резервы для нашего кабинета
        WarehouseReservation::factory()->count(3)->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
        ]);

        $filters = [
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'cabinet_id' => $cabinetId
        ];

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/warehouse-reservations?' . http_build_query($filters));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'warehouse_item_id',
                    'customer_order_item_id',
                    'reserved_quantity',
                    'reservation_type',
                    'status',
                    'priority',
                    'expires_at',
                ]
            ],
            'meta'
        ]);

        $responseData = $response->json();
        $this->assertCount(3, $responseData['data']);
    }

    public function test_can_create_reservation(): void
    {
        // Arrange
        $reservationData = [
            'warehouse_item_id' => $this->warehouseItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
            'reserved_quantity' => 5,
            'reservation_type' => 'order',
            'document_type' => 'customer_order',
            'document_id' => $this->customerOrder->id,
            'priority' => 5,
            'expires_at' => '2024-02-15 23:59:59',
            'auto_release' => true,
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-reservations', $reservationData);

        // Assert
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'reservations' => [
                '*' => [
                    'id',
                    'warehouse_item_id',
                    'reserved_quantity',
                    'reservation_type',
                ]
            ],
            'message'
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'warehouse_item_id' => $this->warehouseItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
            'reserved_quantity' => 5,
            'reservation_type' => 'order',
            'status' => 'reserved',
        ]);

        // Проверяем, что количество зарезервировано на складе
        $this->warehouseItem->refresh();
        $this->assertEquals(95, $this->warehouseItem->available_quantity);
        $this->assertEquals(5, $this->warehouseItem->reserved_quantity);
    }

    public function test_can_cancel_reservation(): void
    {
        // Arrange
        $reservation = WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
            'reserved_quantity' => 5,
            'status' => 'reserved',
        ]);

        // Обновляем количества на складе
        $this->warehouseItem->update([
            'available_quantity' => 95,
            'reserved_quantity' => 5,
        ]);

        // Act
        $response = $this->postJson("/api/internal/warehouse-order-scheme/warehouse-reservations/{$reservation->id}/cancel");

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Резерв отменен'
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $reservation->id,
            'status' => 'cancelled',
        ]);

        // Проверяем, что количество освобождено на складе
        $this->warehouseItem->refresh();
        $this->assertEquals(100, $this->warehouseItem->available_quantity);
        $this->assertEquals(0, $this->warehouseItem->reserved_quantity);
    }

    public function test_can_mark_reservations_as_shipped(): void
    {
        // Arrange
        $reservation1 = WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 3,
            'status' => 'reserved',
        ]);

        $reservation2 = WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 2,
            'status' => 'reserved',
        ]);

        $requestData = [
            'reservation_ids' => [$reservation1->id, $reservation2->id]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-reservations/mark-as-shipped', $requestData);

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'updated_count',
            'message'
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $reservation1->id,
            'status' => 'shipped',
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $reservation2->id,
            'status' => 'shipped',
        ]);
    }

    public function test_can_release_expired_reservations(): void
    {
        // Arrange
        $expiredReservation = WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 5,
            'status' => 'reserved',
            'expires_at' => now()->subDay(),
        ]);

        $activeReservation = WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 3,
            'status' => 'reserved',
            'expires_at' => now()->addDay(),
        ]);

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-reservations/release-expired');

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'released_count',
            'message'
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $expiredReservation->id,
            'status' => 'cancelled',
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $activeReservation->id,
            'status' => 'reserved',
        ]);
    }

    public function test_can_check_product_availability(): void
    {
        // Arrange
        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 20,
            'status' => 'reserved',
        ]);

        // Обновляем количества на складе
        $this->warehouseItem->update([
            'available_quantity' => 80,
            'reserved_quantity' => 20,
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/warehouse-reservations/availability?' . http_build_query([
            'product_id' => $this->product->id,
            'warehouse_id' => $this->warehouse->id,
            'date' => '2024-01-15'
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'available',
            'reserved',
            'total'
        ]);

        $responseData = $response->json();
        $this->assertEquals(80, $responseData['available']);
        $this->assertEquals(20, $responseData['reserved']);
        $this->assertEquals(100, $responseData['total']);
    }

    public function test_can_get_reservation_types(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouse-order-scheme/warehouse-reservations/types');

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'types' => [
                'order',
                'production',
                'transfer',
                'marketing',
                'quality'
            ]
        ]);
    }

    public function test_cannot_reserve_more_than_available(): void
    {
        // Arrange
        $reservationData = [
            'warehouse_item_id' => $this->warehouseItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
            'reserved_quantity' => 150, // Больше чем доступно (100)
            'reservation_type' => 'order',
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-reservations', $reservationData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_cannot_reserve_defective_items(): void
    {
        // Arrange
        $defectiveItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'available_quantity' => 50,
            'quality_status' => 'defective',
        ]);

        $reservationData = [
            'warehouse_item_id' => $defectiveItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
            'reserved_quantity' => 10,
            'reservation_type' => 'order',
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-reservations', $reservationData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_order_scheme_for_warehouse(): void
    {
        // Arrange - создаем склад без ордерной схемы отгрузок
        $regularWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $regularItem = WarehouseItem::factory()->create([
            'warehouse_id' => $regularWarehouse->id,
            'product_id' => $this->product->id,
        ]);

        $reservationData = [
            'warehouse_item_id' => $regularItem->id,
            'customer_order_item_id' => $this->customerOrderItem->id,
            'reserved_quantity' => 5,
            'reservation_type' => 'order',
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouse-order-scheme/warehouse-reservations', $reservationData);

        // Assert
        $response->assertStatus(422);
    }
}
