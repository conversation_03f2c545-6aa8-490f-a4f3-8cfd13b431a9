<?php

namespace Tests\Feature;

use App\Models\Acceptance;
use App\Models\AcceptanceItem;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Country;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\VatRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Queue;
use Random\RandomException;
use Tests\TestCase;

class AcceptanceItemTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();
        Bus::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_acceptance_items_list(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем 3 item для нашей приемки
        AcceptanceItem::factory()->count(3)->create([
            'acceptance_id' => $acceptance->id
        ]);

        // Создаем item для другой приемки, чтобы убедиться что не получим его
        $otherAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        AcceptanceItem::factory()->create([
            'acceptance_id' => $otherAcceptance->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $acceptance->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'acceptance_id',
                        'product_id',
                        'quantity',
                        'price',
                        'vat_rate_id',
                        'discount',
                        'total_price',
                        'country_id',
                        'gtd_number',
                        'recidual'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашей приемки
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($acceptance->id, $item['acceptance_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_acceptance_items(): void
    {
        // Arrange
        // Создаем приемку в другом кабинете
        $otherCabinetAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем items для приемки другого кабинета
        AcceptanceItem::factory()->count(2)->create([
            'acceptance_id' => $otherCabinetAcceptance->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $otherCabinetAcceptance->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'acceptance_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'acceptance_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        $acceptance = Acceptance::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        AcceptanceItem::factory()->count(2)
            ->create(['acceptance_id' => $acceptance->id]);

        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $acceptance->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_acceptance_id(): void
    {
        $response = $this->getJson('/api/internal/acceptances/items');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['acceptance_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        $acceptance = Acceptance::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $acceptance->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        $acceptance = Acceptance::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $acceptance->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем items с разными ценами
        $item1 = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id,
            'price' => 100
        ]);
        $item2 = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id,
            'price' => 200
        ]);
        $item3 = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id,
            'price' => 150
        ]);

        // Act - получаем отсортированный по цене список
        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $acceptance->id,
            'sortField' => 'price',
            'sortDirection' => 'asc',
            'fields' => ['id', 'price']
        ]));

        // Assert
        $response->assertStatus(200);

        $prices = collect($response->json('data'))->pluck('price')->values();
        $expectedPrices = collect([$item1->price, $item3->price, $item2->price]);

        $this->assertEquals($expectedPrices, $prices);
    }

    public function test_can_create_acceptance_item(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'price_includes_vat' => true,
            'has_vat' => true
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20
        ]);

        $country = Country::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'acceptance_id' => $acceptance->id,
            'product_id' => $product->id,
            'quantity' => 3,
            'price' => '1000',
            'vat_rate_id' => $vatRate->id,
            'discount' => '15',
            'country_id' => $country->id,
            'gtd_number' => $this->faker->uuid(),
            'total_price' => '2550'
        ];

        // Рассчитываем ожидаемые значения
        // total_price = (price * quantity) - discount% = (1000 * 3) - 15% = 3000 - 450 = 2550
        $expectedTotalPrice = '2550';
        // total_vat_sum = НДС включен в цену, 20% от 2550 = 2550 * 20 / 120 = 425
        $expectedTotalVatSum = '425';

        // Act
        $response = $this->postJson('/api/internal/acceptances/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('acceptance_items', [
            'id' => $response->json('id'),
            'acceptance_id' => $data['acceptance_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
            'price' => $data['price'],
            'vat_rate_id' => $data['vat_rate_id'],
            'discount' => $data['discount'],
            'total_price' => $expectedTotalPrice,
            'total_vat_sum' => $expectedTotalVatSum,
            'country_id' => $data['country_id'],
            'gtd_number' => $data['gtd_number'],
        ]);
    }

    /**
     * @throws RandomException
     */
    public function test_cannot_create_acceptance_item_for_other_cabinet(): void
    {
        // Arrange
        $otherCabinetAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'acceptance_id' => $otherCabinetAcceptance->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => (string)random_int(0, 10000),
            'total_price' => (string)random_int(0, 99999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/acceptances/items', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_acceptance_item_with_invalid_data(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'acceptance_id' => $acceptance->id,
            'product_id' => 'invalid-uuid',
            'quantity' => -1, // отрицательное количество
            'price' => 'not-a-number', // неверный формат цены
            'vat_rate_id' => 'invalid-uuid',
            'discount' => 'not-a-number',
            'country_id' => 'invalid-uuid',
        ];

        // Act
        $response = $this->postJson('/api/internal/acceptances/items', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id',
                'quantity',
                'price',
                'vat_rate_id',
                'discount',
                'country_id',
            ]);
    }

    public function test_cannot_create_acceptance_item_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/acceptances/items', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'acceptance_id',
                'product_id',
                'quantity',
            ]);
    }

    public function test_can_create_acceptance_item_with_minimal_required_fields(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'acceptance_id' => $acceptance->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => (string)random_int(0, 99999999),
            'total_price' => (string)random_int(0, 99999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/acceptances/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('acceptance_items', [
            'id' => $response->json('id'),
            'acceptance_id' => $data['acceptance_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
            'price' => $data['price'],
        ]);
    }

    /**
     * @throws RandomException
     */
    public function test_cannot_create_acceptance_item_with_product_from_other_cabinet(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'acceptance_id' => $acceptance->id,
            'product_id' => $otherCabinetProduct->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => (string)random_int(0, 9999999),
            'total_price' => (string)random_int(0, 9999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/acceptances/items', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_acceptance_item(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'price_includes_vat' => false,
            'has_vat' => true
        ]);

        $acceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id,
            'quantity' => 1,
            'price' => '500',
            'total_price' => '500',
            'total_vat_sum' => '0'
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20
        ]);

        $country = Country::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $updateData = [
            'quantity' => 2,
            'price' => '1200',
            'vat_rate_id' => $vatRate->id,
            'discount' => '10',
            'country_id' => $country->id,
            'gtd_number' => $this->faker->uuid(),
            'total_price' => '2160',
        ];

        // Рассчитываем ожидаемые значения
        // total_price = (price * quantity) - discount% = (1200 * 2) - 10% = 2400 - 240 = 2160
        $baseAmount = '2160';
        // НДС сверх цены: 2160 + 20% = 2160 + 432 = 2592
        $expectedTotalPrice = '2160';
        // total_vat_sum = 432
        $expectedTotalVatSum = '432';

        // Act
        $response = $this->putJson("/api/internal/acceptances/items/{$acceptanceItem->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('acceptance_items', [
            'id' => $acceptanceItem->id,
            'quantity' => $updateData['quantity'],
            'price' => $updateData['price'],
            'vat_rate_id' => $updateData['vat_rate_id'],
            'discount' => $updateData['discount'],
            'total_price' => $expectedTotalPrice,
            'total_vat_sum' => $expectedTotalVatSum,
            'country_id' => $updateData['country_id'],
            'gtd_number' => $updateData['gtd_number'],
        ]);
    }

    public function test_cannot_update_acceptance_item_from_other_cabinet(): void
    {
        // Arrange
        $otherAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherAcceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $otherAcceptance->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => (string)random_int(0, 99999999),
            'total_price' => (string)random_int(0, 99999999),
        ];

        // Act
        $response = $this->putJson("/api/internal/acceptances/items/{$otherAcceptanceItem->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_acceptance_item_with_invalid_data(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $acceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id
        ]);

        $invalidData = [
            'product_id' => 'invalid-uuid',
            'quantity' => -1,
            'price' => 'not-a-number',
            'vat_rate_id' => 'invalid-uuid',
            'discount' => 'not-a-number',
            'country_id' => 'invalid-uuid',
        ];

        // Act
        $response = $this->putJson("/api/internal/acceptances/items/{$acceptanceItem->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'quantity',
                'price',
                'vat_rate_id',
                'discount',
                'country_id',
            ]);
    }

    public function test_cannot_update_non_existent_acceptance_item(): void
    {
        // Act
        $response = $this->putJson("/api/internal/acceptances/items/" . $this->faker->uuid(), [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => (string)random_int(0, 99999999),
            'total_price' => (string)random_int(0, 99999999),
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_acceptance_item(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $acceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/acceptances/items/{$acceptanceItem->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'acceptance_id',
                'product_id',
                'quantity',
                'price',
                'vat_rate_id',
                'discount',
                'total_price',
                'country_id',
                'gtd_number'
            ]);

        $this->assertEquals($acceptanceItem->id, $response->json('id'));
        $this->assertEquals($acceptance->id, $response->json('acceptance_id'));
    }

    public function test_cannot_show_acceptance_item_from_other_cabinet(): void
    {
        // Arrange
        $otherAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherAcceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $otherAcceptance->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/acceptances/items/{$otherAcceptanceItem->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_acceptance_item(): void
    {
        // Act
        $response = $this->getJson("/api/internal/acceptances/items/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_acceptance_item(): void
    {
        // Arrange
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $acceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $acceptance->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/acceptances/items/{$acceptanceItem->id}");

        // Assert
        $response->assertStatus(204);


    }

    public function test_cannot_delete_acceptance_item_from_other_cabinet(): void
    {
        // Arrange
        $otherAcceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherAcceptanceItem = AcceptanceItem::factory()->create([
            'acceptance_id' => $otherAcceptance->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/acceptances/items/{$otherAcceptanceItem->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('acceptance_items', [
            'id' => $otherAcceptanceItem->id
        ]);
    }

    public function test_cannot_delete_non_existent_acceptance_item(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/acceptances/items/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }
}
