<?php

namespace Tests\Unit\Rules;

use Tests\TestCase;
use App\Rules\ValidSortFieldRule;
use App\Entities\BaseEntity;
use Illuminate\Http\Request;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TestEntity extends BaseEntity
{
    public static string $table = 'test_entities';

    public static function getFields(): array
    {
        return ['id', 'name', 'email', 'created_at'];
    }
}

class ValidSortFieldRuleTest extends TestCase
{
    use RefreshDatabase;

    private ValidSortFieldRule $rule;
    private Request $request;

    protected function setUp(): void
    {
        parent::setUp();

        // Используем тестовую сущность
        $entity = new TestEntity();
        $this->request = new Request();
        $this->rule = new ValidSortFieldRule($entity, $this->request);
    }

    public function test_it_validates_empty_sort_field(): void
    {
        // Arrange
        $value = '';

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('sort', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for empty sort field');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_valid_entity_field(): void
    {
        // Arrange
        $value = 'id';

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('sort', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid entity field');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_valid_relation_field_when_relation_requested(): void
    {
        // Arrange
        $value = 'profile.name';
        $this->request->merge(['fields' => ['profile']]);

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('sort', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid relation field when relation is requested');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_valid_relation_field_when_full_field_requested(): void
    {
        // Arrange
        $value = 'profile.name';
        $this->request->merge(['fields' => ['profile.name']]);

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('sort', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid relation field when full field is requested');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_fails_for_invalid_entity_field(): void
    {
        // Arrange
        $value = 'invalid_field';

        // Act
        $errorMessage = null;
        $this->rule->validate('sort', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals("The sort field 'invalid_field' is not a valid field.", $errorMessage);
    }

    public function test_it_fails_for_relation_field_when_relation_not_requested(): void
    {
        // Arrange
        $value = 'profile.name';
        $this->request->merge(['fields' => ['name', 'email']]);

        // Act
        $errorMessage = null;
        $this->rule->validate('sort', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals("The sort field 'profile.name' must be included in the selected fields or its relation must be requested.", $errorMessage);
    }

    public function test_it_validates_multiple_fields_in_request(): void
    {
        // Arrange
        $value = 'profile.name';
        $this->request->merge(['fields' => ['name,email,profile']]);

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('sort', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for relation field when multiple fields are requested');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_empty_fields_array(): void
    {
        // Arrange
        $value = 'id';
        $this->request->merge(['fields' => []]);

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('sort', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid entity field when fields array is empty');
        $this->assertNull($errorMessage, 'No error message should be set');
    }
}
