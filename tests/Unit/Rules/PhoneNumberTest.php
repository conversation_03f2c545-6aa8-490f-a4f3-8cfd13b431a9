<?php

namespace Tests\Unit\Rules;

use Tests\TestCase;
use App\Rules\PhoneNumber;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PhoneNumberTest extends TestCase
{
    use RefreshDatabase;

    private PhoneNumber $rule;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new PhoneNumber();
    }

    public function test_it_validates_valid_phone_number_with_plus(): void
    {
        // Arrange
        $value = '+79001234567';

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('phone', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid phone number with plus');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_valid_phone_number_without_plus(): void
    {
        // Arrange
        $value = '79001234567';

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('phone', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for valid phone number without plus');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_minimum_length_phone_number(): void
    {
        // Arrange
        $value = '1';

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('phone', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for minimum length phone number');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_validates_maximum_length_phone_number(): void
    {
        // Arrange
        $value = '123456789012345';

        // Act
        $validationPassed = true;
        $errorMessage = null;

        $this->rule->validate('phone', $value, function ($message) use (&$validationPassed, &$errorMessage) {
            $validationPassed = false;
            $errorMessage = $message;
        });

        // Assert
        $this->assertTrue($validationPassed, 'Validation should pass for maximum length phone number');
        $this->assertNull($errorMessage, 'No error message should be set');
    }

    public function test_it_fails_for_empty_phone_number(): void
    {
        // Arrange
        $value = '';

        // Act
        $errorMessage = null;
        $this->rule->validate('phone', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals('The :attribute must be a valid phone number in E.164 format.', $errorMessage);
    }

    public function test_it_fails_for_phone_number_with_letters(): void
    {
        // Arrange
        $value = '+79001234567a';

        // Act
        $errorMessage = null;
        $this->rule->validate('phone', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals('The :attribute must be a valid phone number in E.164 format.', $errorMessage);
    }

    public function test_it_fails_for_phone_number_with_special_characters(): void
    {
        // Arrange
        $value = '+7900-123-45-67';

        // Act
        $errorMessage = null;
        $this->rule->validate('phone', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals('The :attribute must be a valid phone number in E.164 format.', $errorMessage);
    }

    public function test_it_fails_for_phone_number_with_multiple_plus(): void
    {
        // Arrange
        $value = '++79001234567';

        // Act
        $errorMessage = null;
        $this->rule->validate('phone', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals('The :attribute must be a valid phone number in E.164 format.', $errorMessage);
    }

    public function test_it_fails_for_phone_number_exceeding_max_length(): void
    {
        // Arrange
        $value = '1234567890123456'; // 16 digits

        // Act
        $errorMessage = null;
        $this->rule->validate('phone', $value, function ($message) use (&$errorMessage) {
            $errorMessage = $message;
        });

        // Assert
        $this->assertNotNull($errorMessage, 'Error message should be set');
        $this->assertEquals('The :attribute must be a valid phone number in E.164 format.', $errorMessage);
    }
}
