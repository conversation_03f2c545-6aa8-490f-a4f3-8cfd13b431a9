<?php

namespace Tests\Unit\Traits;

use App\Traits\SetAttributesTraits;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SetAttributesTraitsTest extends TestCase
{
    use RefreshDatabase;

    private $testClass;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a concrete class that uses the trait
        $this->testClass = new class () {
            use SetAttributesTraits;
        };
    }

    public function test_it_creates_attributes_with_empty_value(): void
    {
        $result = $this->testClass->setAttributes(
            name: 'test_field',
            type: 'string'
        );

        $this->assertEquals([
            '@attributes' => [
                'Имя' => 'test_field',
                'Тип' => 'string'
            ],
            'Пусто' => null
        ], $result);
    }

    public function test_it_creates_attributes_with_non_empty_value(): void
    {
        $result = $this->testClass->setAttributes(
            name: 'price',
            type: 'number',
            value: 100
        );

        $this->assertEquals([
            '@attributes' => [
                'Имя' => 'price',
                'Тип' => 'number'
            ],
            'Значение' => 100
        ], $result);
    }

    public function test_it_merges_additional_items(): void
    {
        $additionalItems = [
            'extra_field' => 'extra_value',
            'another_field' => 123
        ];

        $result = $this->testClass->setAttributes(
            name: 'status',
            type: 'string',
            value: 'active',
            item: $additionalItems
        );

        $this->assertEquals([
            '@attributes' => [
                'Имя' => 'status',
                'Тип' => 'string'
            ],
            'Значение' => 'active',
            'extra_field' => 'extra_value',
            'another_field' => 123
        ], $result);
    }

    public function test_it_handles_zero_value_correctly(): void
    {
        $result = $this->testClass->setAttributes(
            name: 'quantity',
            type: 'integer',
            value: 0
        );

        $this->assertEquals([
            '@attributes' => [
                'Имя' => 'quantity',
                'Тип' => 'integer'
            ],
            'Пусто' => 0
        ], $result);
    }

    public function test_it_handles_empty_string_value(): void
    {
        $result = $this->testClass->setAttributes(
            name: 'description',
            type: 'string',
            value: ''
        );

        $this->assertEquals([
            '@attributes' => [
                'Имя' => 'description',
                'Тип' => 'string'
            ],
            'Пусто' => ''
        ], $result);
    }
}
