<?php

namespace Tests\Unit\Traits;

use Tests\TestCase;
use App\Traits\HasEmployeeAndDepartment;
use Exception;
use RuntimeException;
use Mockery;
use stdClass;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Api\Internal\AuthorizationService;

class TestHasEmployeeAndDepartmentClass
{
    use HasEmployeeAndDepartment;

    public function __construct(
        public AuthorizationService $authService
    ) {
    }
}

class HasEmployeeAndDepartmentTest extends TestCase
{
    use RefreshDatabase;

    private TestHasEmployeeAndDepartmentClass $testClass;
    private AuthorizationService $authService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authService = Mockery::mock(AuthorizationService::class);
        $this->testClass = new TestHasEmployeeAndDepartmentClass($this->authService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_successfully_validates_employee_and_department_in_same_cabinet(): void
    {
        // Arrange
        $employeeId = '123';
        $departmentId = '456';
        $cabinetId = '789';

        $employee = new stdClass();
        $employee->cabinet_id = $cabinetId;

        $department = new stdClass();
        $department->cabinet_id = $cabinetId;

        $this->authService
            ->shouldReceive('validateRelationAccess')
            ->with('employees', $employeeId, null, null, null, null, [
                'table' => 'cabinet_employee',
                'field' => 'id',
                'value' => $employeeId
            ])
            ->andReturn($employee);

        $this->authService
            ->shouldReceive('validateRelationAccess')
            ->with('departments', $departmentId, $cabinetId)
            ->andReturn($department);

        // Act
        $this->testClass->checkEmployeeAndDepartmentIds($employeeId, $departmentId, $cabinetId);

        // Assert
        $this->assertEquals($cabinetId, $employee->cabinet_id);
        $this->assertEquals($cabinetId, $department->cabinet_id);
        $this->assertEquals($employee->cabinet_id, $department->cabinet_id);
    }

    /**
     * @throws Exception
     */
    public function test_it_throws_exception_when_employee_and_department_in_different_cabinets(): void
    {
        // Arrange
        $employeeId = '123';
        $departmentId = '456';
        $cabinetId = '789';
        $differentCabinetId = '999';

        $employee = new stdClass();
        $employee->cabinet_id = $cabinetId;

        $department = new stdClass();
        $department->cabinet_id = $differentCabinetId;

        $this->authService
            ->shouldReceive('validateRelationAccess')
            ->with('employees', $employeeId, null, null, null, null, [
                'table' => 'cabinet_employee',
                'field' => 'id',
                'value' => $employeeId
            ])
            ->andReturn($employee);

        $this->authService
            ->shouldReceive('validateRelationAccess')
            ->with('departments', $departmentId, $cabinetId)
            ->andReturn($department);

        // Act & Assert
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('The employee and the department are in different cabinets');

        $this->testClass->checkEmployeeAndDepartmentIds($employeeId, $departmentId, $cabinetId);
    }

    public function test_it_propagates_exception_from_validate_relation_access(): void
    {
        // Arrange
        $employeeId = '123';
        $departmentId = '456';
        $cabinetId = '789';

        $this->authService
            ->shouldReceive('validateRelationAccess')
            ->with('employees', $employeeId, null, null, null, null, [
                'table' => 'cabinet_employee',
                'field' => 'id',
                'value' => $employeeId
            ])
            ->andThrow(new Exception('Employee not found'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Employee not found');

        $this->testClass->checkEmployeeAndDepartmentIds($employeeId, $departmentId, $cabinetId);
    }
}
