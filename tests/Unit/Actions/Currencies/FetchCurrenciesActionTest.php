<?php

namespace Tests\Unit\Actions\Currencies;

use App\Actions\Currencies\FetchCurrenciesAction;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FetchCurrenciesActionTest extends TestCase
{
    use RefreshDatabase;

    private FetchCurrenciesAction $action;

    protected function setUp(): void
    {
        parent::setUp();
        $this->action = new FetchCurrenciesAction();
    }

    public function test_it_can_fetch_currencies_from_cbr(): void
    {
        // Подготавливаем мок ответа от ЦБ РФ
        $mockXmlResponse = '<?xml version="1.0" encoding="UTF-8"?>
            <ValCurs Date="01.01.2024" name="Foreign Currency Market">
                <Valute ID="R01235">
                    <NumCode>840</NumCode>
                    <CharCode>USD</CharCode>
                    <Nominal>1</Nominal>
                    <Name>Доллар США</Name>
                    <Value>91,2345</Value>
                </Valute>
            </ValCurs>';

        Http::fake([
            'https://www.cbr.ru/scripts/XML_daily.asp' => Http::response($mockXmlResponse, 200, [
                'Content-Type' => 'application/xml'
            ])
        ]);

        // Выполняем действие
        $result = $this->action->handle();

        // Проверяем результат
        $this->assertIsArray($result);
        $this->assertArrayHasKey('Valute', $result);
        $this->assertEquals('USD', $result['Valute']['CharCode']);
        $this->assertEquals('91,2345', $result['Valute']['Value']);
    }

    public function test_it_can_convert_comma_to_dot(): void
    {
        $value = '91,2345';
        $expected = '91.2345';

        $result = $this->action->convertCommaToDot($value);

        $this->assertEquals($expected, $result);
    }

    public function test_it_handles_empty_value_in_convert_comma_to_dot(): void
    {
        $value = '';
        $expected = '';

        $result = $this->action->convertCommaToDot($value);

        $this->assertEquals($expected, $result);
    }
}
