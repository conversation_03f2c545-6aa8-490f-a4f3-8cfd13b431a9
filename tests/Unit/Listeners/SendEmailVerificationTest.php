<?php

namespace Tests\Unit\Listeners;

use Tests\TestCase;
use Mockery;
use App\Models\User;
use App\Events\Registered;
use App\Listeners\SendEmailVerification;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SendEmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    private SendEmailVerification $listener;

    protected function setUp(): void
    {
        parent::setUp();
        $this->listener = new SendEmailVerification();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_sends_verification_email_for_unverified_user(): void
    {
        // Создаем мок пользователя
        $user = Mockery::mock(User::class);
        $user->shouldReceive('hasVerifiedEmail')
            ->once()
            ->andReturn(false);
        $user->shouldReceive('sendEmailVerificationNotification')
            ->once();

        // Создаем событие
        $event = new Registered($user);

        // Выполняем слушатель
        $this->listener->handle($event);

        // Проверяем, что все ожидаемые методы были вызваны
        $this->assertTrue(true);
    }

    public function test_it_does_not_send_verification_email_for_verified_user(): void
    {
        // Создаем мок пользователя
        $user = Mockery::mock(User::class);
        $user->shouldReceive('hasVerifiedEmail')
            ->once()
            ->andReturn(true);
        $user->shouldNotReceive('sendEmailVerificationNotification');

        // Создаем событие
        $event = new Registered($user);

        // Выполняем слушатель
        $this->listener->handle($event);

        // Проверяем, что метод sendEmailVerificationNotification не был вызван
        $this->assertTrue(true);
    }
}
