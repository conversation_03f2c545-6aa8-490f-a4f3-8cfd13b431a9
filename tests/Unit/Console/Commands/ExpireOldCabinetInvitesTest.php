<?php

namespace Tests\Unit\Console\Commands;

use Carbon\Carbon;
use Tests\TestCase;
use App\Models\Cabinet;
use App\Models\Employee;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\WithFaker;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class ExpireOldCabinetInvitesTest extends TestCase
{
    use DatabaseTransactions;

    use WithFaker;
    private Carbon $now;

    protected function setUp(): void
    {
        parent::setUp();

        $this->now = Carbon::now();
        Carbon::setTestNow($this->now);

        // Clear the cabinet invites table before each test
        DB::table('cabinet_invites')->truncate();
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Reset Carbon's test time
        Carbon::setTestNow();
    }

    public function test_it_should_expire_waiting_invites_older_than_one_day(): void
    {
        // Arrange
        $this->createCabinetInvite(
            status: CabinetInviteStatusEnum::WAITING->value,
            daysOld: 2 // created 2 days ago
        );

        // Act
        $this->artisan('app:delete-old-cabinet-invites')->assertSuccessful();

        // Assert
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::EXPIRED,
            'created_at' => $this->now->copy()->subDays(2)
        ]);
    }

    public function test_it_should_not_expire_waiting_invites_newer_than_one_day(): void
    {
        // Arrange
        $this->createCabinetInvite(
            status: CabinetInviteStatusEnum::WAITING->value,
            daysOld: 0 // created today
        );

        // Act
        $this->artisan('app:delete-old-cabinet-invites')->assertSuccessful();

        // Assert
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::WAITING,
            'created_at' => $this->now
        ]);
    }

    public function test_it_should_not_expire_non_waiting_invites_regardless_of_age(): void
    {
        // Arrange
        $this->createCabinetInvite(
            status: CabinetInviteStatusEnum::ACCEPTED->value,
            daysOld: 2 // created 2 days ago
        );

        $this->createCabinetInvite(
            status: CabinetInviteStatusEnum::DECLINED->value,
            daysOld: 2 // created 2 days ago
        );

        // Act
        $this->artisan('app:delete-old-cabinet-invites')->assertSuccessful();

        // Assert
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::ACCEPTED,
            'created_at' => $this->now->copy()->subDays(2)
        ]);

        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::DECLINED,
            'created_at' => $this->now->copy()->subDays(2)
        ]);
    }

    public function test_it_should_handle_mixed_scenarios_correctly(): void
    {
        // Arrange
        // Old waiting invites that should be expired
        $this->createCabinetInvite(status: CabinetInviteStatusEnum::WAITING->value, daysOld: 2);
        $this->createCabinetInvite(status: CabinetInviteStatusEnum::WAITING->value, daysOld: 3);

        // Recent waiting invites that should not be expired
        $this->createCabinetInvite(status: CabinetInviteStatusEnum::WAITING->value, daysOld: 0);

        // Old non-waiting invites that should not be expired
        $this->createCabinetInvite(status: CabinetInviteStatusEnum::ACCEPTED->value, daysOld: 2);
        $this->createCabinetInvite(status: CabinetInviteStatusEnum::DECLINED->value, daysOld: 2);

        // Act
        $this->artisan('app:delete-old-cabinet-invites')->assertSuccessful();

        // Assert
        // Verify old waiting invites were expired
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::EXPIRED,
            'created_at' => $this->now->copy()->subDays(2)
        ]);
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::EXPIRED,
            'created_at' => $this->now->copy()->subDays(3)
        ]);

        // Verify recent waiting invites were not expired
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::WAITING,
            'created_at' => $this->now
        ]);

        // Verify old non-waiting invites were not expired
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::ACCEPTED,
            'created_at' => $this->now->copy()->subDays(2)
        ]);
        $this->assertDatabaseHas('cabinet_invites', [
            'status' => CabinetInviteStatusEnum::DECLINED,
            'created_at' => $this->now->copy()->subDays(2)
        ]);
    }

    /**
     * Helper method to create a cabinet invite record
     */
    private function createCabinetInvite(string $status, int $daysOld): void
    {
        DB::table('cabinet_invites')->insert([
            'id' => Str::orderedUuid(),
            'cabinet_id' => Cabinet::factory()->create()->id,
            'employee_id' => Employee::factory()->create()->id,
            'token' => $this->faker()->unique()->word,
            'email' => '<EMAIL>',
            'status' => $status,
            'created_at' => $this->now->copy()->subDays($daysOld),
            'updated_at' => $this->now
        ]);
    }
}
