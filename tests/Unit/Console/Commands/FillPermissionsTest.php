<?php

namespace Tests\Unit\Console\Commands;

use Tests\TestCase;
use Mockery;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class FillPermissionsTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем тестовые данные
        $this->createMockDataFiles();

        // По умолчанию мокаем DB для успешного выполнения
        DB::shouldReceive('table')->andReturnSelf()->byDefault();
        DB::shouldReceive('join')->andReturnSelf()->byDefault();
        DB::shouldReceive('where')->andReturnSelf()->byDefault();
        DB::shouldReceive('get')->andReturn(collect([
            (object)['id' => 'test-id', 'require_scope' => true]
        ]))->byDefault();
        DB::shouldReceive('insert')->andReturn(true)->byDefault();
    }

    protected function createMockDataFiles(): void
    {
        // Создаем тестовые данные для разрешений
        $permissionsData = [
            'groups' => [
                ['name' => 'Товары'],
                ['name' => 'Сотрудники']
            ],
            'categories' => [
                ['name' => 'Базовые'],
                ['name' => 'Расширенные']
            ],
            'permissions' => [
                [
                    'require_scope' => true,
                    'guard_name' => 'product',
                    'operations' => 'read',
                    'category_name' => 'Базовые',
                    'group_name' => 'Товары'
                ],
                [
                    'require_scope' => false,
                    'guard_name' => 'employee',
                    'operations' => 'update',
                    'category_name' => 'Расширенные',
                    'group_name' => 'Сотрудники'
                ]
            ]
        ];

        // Создаем тестовые данные для ролей
        $rolesData = [
            'Администратор' => [],
            'Менеджер' => [
                [
                    'guard_name' => 'product',
                    'operations' => ['read'],
                    'scope' => 'company'
                ]
            ]
        ];

        // Мокаем base_path
        $this->app->bind('path.base', function () {
            return __DIR__;
        });

        // Создаем директории и файлы данных
        if (!file_exists(__DIR__ . '/app/Data')) {
            mkdir(__DIR__ . '/app/Data', 0777, true);
        }

        file_put_contents(
            __DIR__ . '/app/Data/Permissions.php',
            '<?php return ' . var_export($permissionsData, true) . ';'
        );

        file_put_contents(
            __DIR__ . '/app/Data/Roles.php',
            '<?php return ' . var_export($rolesData, true) . ';'
        );
    }

    protected function tearDown(): void
    {
        // Удаляем тестовые файлы
        if (file_exists(__DIR__ . '/app/Data/Permissions.php')) {
            unlink(__DIR__ . '/app/Data/Permissions.php');
        }

        if (file_exists(__DIR__ . '/app/Data/Roles.php')) {
            unlink(__DIR__ . '/app/Data/Roles.php');
        }

        if (is_dir(__DIR__ . '/app/Data')) {
            rmdir(__DIR__ . '/app/Data');
        }

        if (is_dir(__DIR__ . '/app')) {
            rmdir(__DIR__ . '/app');
        }

        Mockery::close();
        parent::tearDown();
    }

    public function test_it_should_fill_permissions_successfully(): void
    {
        // Тестируем только успешный вызов и вывод команды
        $this->artisan('app:fill-permissions')
            ->assertExitCode(0)
            ->expectsOutput('Получаем данные по умолчанию...')
            ->expectsOutput('Заполняем таблицу "permission_groups"...')
            ->expectsOutput('Заполняем таблицу "permission_categories"...')
            ->expectsOutput('Заполняем таблицу "permissions"...')
            ->expectsOutput('Заполняем стандартные роли...')
            ->expectsOutput('Заполнение таблицы "permissions" завершено.');
    }
}
