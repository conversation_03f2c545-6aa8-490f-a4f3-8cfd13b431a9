<?php

namespace Tests\Unit\Jobs\FIFOJobs;

use Tests\TestCase;
use Mockery;
use App\Jobs\FIFOJobs\HandleFifoJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Contracts\Services\Internal\FifoServiceContract;
use Illuminate\Foundation\Testing\WithFaker;

class HandleFifoJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;


    private FifoServiceContract $fifoService;

    protected function setUp(): void
    {
        parent::setUp();

        // Мокаем FifoService
        $this->fifoService = Mockery::mock(FifoServiceContract::class);
        $this->app->instance(FifoServiceContract::class, $this->fifoService);
    }

    public function test_handles_collection_of_items(): void
    {
        // Arrange
        $items = collect([
            (object)['shipment_item_id' => '1'],
            (object)['shipment_item_id' => '2'],
        ]);

        $this->fifoService->shouldReceive('handle')
            ->once()
            ->with('1', false);

        $this->fifoService->shouldReceive('handle')
            ->once()
            ->with('2', false);

        // Act
        $job = new HandleFifoJob($items);
        $job->handle();
    }

    public function test_handles_single_item(): void
    {
        // Arrange
        $itemId = $this->faker()->uuid;

        $this->fifoService->shouldReceive('handle')
            ->once()
            ->with($itemId, false);

        // Act
        $job = new HandleFifoJob($itemId);
        $job->handle();
    }

    public function test_handles_delete_flag(): void
    {
        // Arrange
        $itemId = $this->faker()->uuid;

        $this->fifoService->shouldReceive('handle')
            ->once()
            ->with($itemId, true);

        // Act
        $job = new HandleFifoJob($itemId, true);
        $job->handle();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
