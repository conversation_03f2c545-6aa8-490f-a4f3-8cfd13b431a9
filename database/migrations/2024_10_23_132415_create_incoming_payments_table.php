<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('incoming_payments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('status_id')->nullable()->constrained()->nullOnDelete();

            $table->string('number')->nullable();
            $table->dateTime('date_from')->nullable();

            $table->boolean('held')->default(true); //Проведено

            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('contractor_id')->constrained();
            $table->foreignUuid('sales_channel_id')->nullable()->constrained();
            $table->string('sum')->default(0);
            $table->string('included_vat')->default(0);
            $table->text('comment')->nullable();
            $table->string('incoming_number')->nullable();
            $table->date('incoming_date')->nullable();

            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');
            $table->string('currency_value')->default('1');

            $table->string('bounded_sum')->default(0);
            $table->string('not_bounded_sum')->default(0);

            $table->boolean('is_imported')->default(false);

            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_common')->default(false);

            $table->boolean('is_default')->default(false);
        });

        Schema::create('incoming_payment_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('incoming_payment_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('document_id')->references('documentable_id')->on('documents');

            $table->string('paid_in')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('incoming_payment_items');
        Schema::dropIfExists('incoming_payments');
    }
};
