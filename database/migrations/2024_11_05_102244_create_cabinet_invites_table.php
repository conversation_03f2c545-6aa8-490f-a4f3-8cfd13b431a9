<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cabinet_invites', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->nullable()->constrained();
            $table->string('email');
            $table->string('status')->default(CabinetInviteStatusEnum::WAITING);
            $table->string('token', 16)->unique();
            $table->foreignUuid('role_id')->nullable()->constrained('roles')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cabinet_invites');
    }
};
