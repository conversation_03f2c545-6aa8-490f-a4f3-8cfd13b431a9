<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->unsignedBigInteger('ozon_company_id');
            $table->bigInteger('operation_id')->unique(); //
            $table->string('operation_type'); //
            $table->datetime('operation_date')->nullable(); //
            $table->string('operation_type_name'); //
            $table->decimal('delivery_charge', 20, 4)->nullable()->default(0);
            $table->decimal('return_delivery_charge', 20, 4)->nullable()->default(0);
            $table->decimal('accruals_for_sale', 20, 4)->nullable()->default(0);
            $table->decimal('sale_commission', 20, 4)->nullable()->default(0);
            $table->decimal('amount', 20, 4)->nullable()->default(0);
            $table->string('type'); //
            $table->string('delivery_schema')->nullable(); //
            $table->datetime('order_date')->nullable(); //
            $table->string('posting_number')->nullable(); //
            $table->bigInteger('warehouse_id'); //
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_transactions');
    }
};
