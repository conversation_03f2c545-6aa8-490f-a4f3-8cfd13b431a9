<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();

            $table->string('number')->nullable();
            $table->dateTime('date_from')->nullable();

            $table->foreignUuid('status_id')->nullable()->constrained();
            $table->boolean('held')->default(true); //Проведено
            $table->boolean('waiting')->default(false);

            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('contractor_id')->constrained();
            $table->date('plan_date')->nullable();

            $table->foreignUuid('warehouse_id')->nullable()->constrained();
            $table->string('total_price')->default('0');

            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();

            $table->boolean('is_default')->default(false);
            $table->boolean('is_common')->default(false);

            $table->text('comment')->nullable();
        });

        Schema::create('vendor_order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('order_id')->references('id')->on('vendor_orders')->cascadeOnDelete();
            $table->foreignUuid('product_id')->references('id')->on('products');
            $table->unsignedBigInteger('quantity');

            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');
            $table->string('price_in_currency')->default('0');
            $table->string('currency_rate_to_base')->default('1');
            $table->string('price_in_base')->default('0');
            $table->string('amount_in_base')->default('0');

            $table->foreignUuid('vat_rate_id')->nullable()->references('id')->on('vat_rates');
            $table->string('discount')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_order_items');
        Schema::dropIfExists('vendor_orders');
    }
};
