<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->softDeletes();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();    // привязка к кабинету

            $table->string('name', 255);                                     // Название упаковки
            $table->text('description')->nullable();                                // описание

            $table->decimal('length', 8, 2)->nullable()->default(0); // длина
            $table->decimal('width', 8, 2)->nullable()->default(0);  // ширина
            $table->decimal('height', 8, 2)->nullable()->default(0); // высота
            $table->foreignUuid('measurement_unit_size_id')
                ->references('id')
                ->on('measurement_units');

            $table->decimal('weight')->nullable(); // вес
            $table->foreignUuid('measurement_unit_weight_id')
                ->references('id')
                ->on('measurement_units');

            $table->decimal('volume')->nullable();// Объем
            $table->foreignUuid('measurement_unit_volume_id')
                ->references('id')
                ->on('measurement_units');

            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packings');
    }
};
