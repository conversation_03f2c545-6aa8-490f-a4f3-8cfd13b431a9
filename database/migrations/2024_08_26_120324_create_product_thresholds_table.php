<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_threshold_warehouses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->constrained()->cascadeOnDelete();
            // $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            // $table->tinyInteger('type');
            $table->uuid('warehouse_id')->nullable(); // Внешний ключ будет добавлен позже
            $table->integer('threshold_count')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_threshold_warehouses', function (Blueprint $table) {
            if (Schema::hasColumn('product_threshold_warehouses', 'warehouse_id')) {
                $table->dropForeign(['warehouse_id']);
            }
        });
        Schema::dropIfExists('product_threshold_warehouses');
    }
};
