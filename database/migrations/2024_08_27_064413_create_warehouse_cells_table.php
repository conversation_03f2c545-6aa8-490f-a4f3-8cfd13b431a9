<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_cells', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('group_id')->nullable()->references('id')->on('warehouse_cell_groups')->nullOnDelete();
            $table->foreignUuid('warehouse_id')->constrained()->cascadeOnDelete();
            $table->string('type', 255)->nullable();

            $table->string('address');
            $table->text('description')->nullable();
            $table->string('section')->nullable(); // Секция
            $table->string('line')->nullable(); // Линия
            $table->string('rack')->nullable(); // Стеллаж
            $table->string('tier')->nullable(); // Ярус
            $table->string('position')->nullable(); // Позиция
            $table->string('separator')->nullable(); // Разделитель

            //Размещение и отбор
            $table->integer('availability_level')->default(0);
            //$table->foreignUuid('storage_area_id')->nullable()->references('id')->on('warehouse_storage_areas')->cascadeOnDelete();
            //$table->foreignUuid('work_area_id')->nullable()->references('id')->on('warehouse_work_areas')->cascadeOnDelete();
            $table->integer('circumvention_order')->default(0);

            //Наполнение
            $table->foreignUuid('size_id')->nullable()->references('id')->on('warehouse_cell_sizes')->nullOnDelete();
            $table->integer('filling_volume')->default(0);
            $table->integer('filling_weight')->default(0);

            //Инвентаризация
            $table->integer('stocktake')->default(0);
            $table->integer('recalculate_days')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_cells');
    }
};
