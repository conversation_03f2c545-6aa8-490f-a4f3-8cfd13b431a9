<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();
            $table->dateTime('archived_at')->nullable();

            $table->string('name');
            $table->foreignUuid('cabinet_id')->nullable()->constrained()->cascadeOnDelete();

            $table->foreignUuid('work_schedule_id')->nullable()->references('id')->on('warehouse_work_schedules')->nullOnDelete();
            //контроль свободных остатков
            $table->boolean('control_free_residuals')->default(false);
            //контактная информация склада
            $table->foreignUuid('address_id')->nullable()->references('id')->on('warehouse_addresses')->nullOnDelete();
            $table->foreignUuid('phone_id')->nullable()->references('id')->on('warehouse_phones')->nullOnDelete();

            //Ордерная схема
            //$table->foreignUuid('order_scheme_id')->nullable()->references('id')->on('warehouse_order_schemes')->nullOnDelete();
            //Ордерная схема и структура
            //$table->foreignUuid('order_scheme_id')->nullable()->constrained()->nullOnDelete();
            //Ответственное хранение
            //$table->foreignUuid('responsible_id')->nullable()->constrained()->nullOnDelete();

            //TODO надо ли группы складов
            //$table->foreignUuid('group_id')->nullable->references('id')->on('warehouse_groups')->cascadeOnDelete();

            //TODO материально-ответственное лицо, должность
            $table->foreignUuid('responsible_employee_id')->nullable()->references('id')->on('employees');

            //TODO печатать цены по
            //$table->foreignUuid('price_print_id')->references('id')->on('warehouse_price_prints')->cascadeOnDelete();

            //TODO учетный вид цены
            //$table->foreignUuid('price_type_id')->references('id')->on('warehouse_price_types')->cascadeOnDelete();

            $table->boolean('is_default')->default(false);
            $table->foreignUuid('department_id')->nullable()->constrained();
            $table->foreignUuid('employee_id')->nullable()->constrained();
            $table->boolean('is_common')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouses');
    }
};
