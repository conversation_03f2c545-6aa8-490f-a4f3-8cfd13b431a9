<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('warehouse_transactions', function (Blueprint $table) {
            // Тип документа-основания
            $table->string('document_type')->nullable()->after('warehouse_id')->comment('Тип документа-основания');
            $table->uuid('document_id')->nullable()->after('document_type')->comment('ID документа-основания');
            
            // Тип операции
            $table->enum('operation_type', [
                'receipt', 'issue', 'transfer_out', 'transfer_in', 
                'reserve', 'unreserve', 'adjustment_plus', 'adjustment_minus',
                'quality_hold', 'quality_release'
            ])->after('document_id')->comment('Тип операции');
            
            // Информация о партии
            $table->string('batch_number')->nullable()->after('operation_type')->comment('Номер партии');
            $table->string('lot_number')->nullable()->after('batch_number')->comment('Номер лота/серии');
            $table->date('expiry_date')->nullable()->after('lot_number')->comment('Срок годности');
            
            // Статус качества
            $table->enum('quality_status', ['good', 'defective', 'quarantine', 'expired'])
                  ->default('good')
                  ->after('expiry_date')
                  ->comment('Статус качества');
            
            // Себестоимость за единицу
            $table->string('cost_per_unit')->nullable()->after('quality_status')->comment('Себестоимость за единицу');
            
            // Связь с резервом (если применимо)
            $table->foreignUuid('reservation_id')
                  ->nullable()
                  ->after('cost_per_unit')
                  ->references('id')
                  ->on('warehouse_reservations')
                  ->comment('Связь с резервом');
            
            // Индексы для быстрого поиска
            $table->index(['document_type', 'document_id']);
            $table->index(['operation_type', 'created_at']);
            $table->index(['batch_number', 'lot_number']);
            $table->index(['expiry_date', 'quality_status']);
            $table->index(['reservation_id']);
        });
    }

    public function down(): void
    {
        Schema::table('warehouse_transactions', function (Blueprint $table) {
            $table->dropForeign(['reservation_id']);
            $table->dropIndex(['document_type', 'document_id']);
            $table->dropIndex(['operation_type', 'created_at']);
            $table->dropIndex(['batch_number', 'lot_number']);
            $table->dropIndex(['expiry_date', 'quality_status']);
            $table->dropIndex(['reservation_id']);
            
            $table->dropColumn([
                'document_type',
                'document_id',
                'operation_type',
                'batch_number',
                'lot_number',
                'expiry_date',
                'quality_status',
                'cost_per_unit',
                'reservation_id'
            ]);
        });
    }
};
