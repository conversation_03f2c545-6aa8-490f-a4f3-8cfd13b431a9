<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contractor_addresses', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('contractor_id')->constrained()->cascadeOnDelete();

            $table->string('postcode')->nullable();
            $table->string('country')->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->string('street')->nullable();
            $table->string('house')->nullable();
            $table->string('office')->nullable();
            $table->string('other')->nullable();
            $table->text('comment')->nullable();

            $table->unique('contractor_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contractor_addresses');
    }
};
