<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_receipt_order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            // Связь с приходным ордером
            $table->foreignUuid('receipt_order_id')
                  ->references('id')
                  ->on('warehouse_receipt_orders')
                  ->cascadeOnDelete();
            
            // Товар
            $table->foreignUuid('product_id')->constrained();
            
            // Количество и цены
            $table->integer('quantity')->comment('Количество к оприходованию');
            $table->string('unit_price')->comment('Цена за единицу');
            $table->string('total_price')->comment('Общая стоимость позиции');
            
            // Партия и качество
            $table->string('batch_number')->nullable()->comment('Номер партии');
            $table->string('lot_number')->nullable()->comment('Номер лота/серии');
            $table->date('expiry_date')->nullable()->comment('Срок годности');
            $table->enum('quality_status', ['good', 'defective', 'quarantine'])
                  ->default('good')
                  ->comment('Статус качества');
            
            // Место хранения
            $table->string('storage_location')->nullable()->comment('Место хранения (ячейка, стеллаж)');
            
            // НДС
            $table->foreignUuid('vat_rate_id')->nullable()->constrained('vat_rates');
            
            // Индексы
            $table->index(['receipt_order_id', 'product_id']);
            $table->index(['product_id', 'batch_number']);
            $table->index('expiry_date');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_receipt_order_items');
    }
};
