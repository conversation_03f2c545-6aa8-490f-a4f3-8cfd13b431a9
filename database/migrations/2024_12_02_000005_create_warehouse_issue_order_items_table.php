<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_issue_order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            // Связь с расходным ордером
            $table->foreignUuid('issue_order_id')
                  ->references('id')
                  ->on('warehouse_issue_orders')
                  ->cascadeOnDelete();
            
            // Товар
            $table->foreignUuid('product_id')->constrained();
            
            // Связь с партией товара на складе
            $table->foreignUuid('warehouse_item_id')
                  ->references('id')
                  ->on('warehouse_items')
                  ->comment('Партия товара для списания');
            
            // Количество и цены
            $table->integer('quantity')->comment('Количество к списанию');
            $table->string('unit_price')->comment('Цена за единицу');
            $table->string('total_price')->comment('Общая стоимость позиции');
            
            // Информация о партии (дублируется для истории)
            $table->string('batch_number')->nullable()->comment('Номер партии');
            $table->string('lot_number')->nullable()->comment('Номер лота/серии');
            $table->date('expiry_date')->nullable()->comment('Срок годности');
            
            // НДС
            $table->foreignUuid('vat_rate_id')->nullable()->constrained('vat_rates');
            
            // Индексы
            $table->index(['issue_order_id', 'product_id']);
            $table->index(['warehouse_item_id', 'quantity']);
            $table->index(['product_id', 'batch_number']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_issue_order_items');
    }
};
