<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('warehouse_items', function (Blueprint $table) {
            // Номер лота/серии
            $table->string('lot_number')->nullable()->after('quantity')->comment('Номер лота/серии');
            
            // Срок годности
            $table->date('expiry_date')->nullable()->after('lot_number')->comment('Срок годности');
            
            // Номер партии поставщика
            $table->string('supplier_batch')->nullable()->after('expiry_date')->comment('Номер партии поставщика');
            
            // Статус качества
            $table->enum('quality_status', ['good', 'defective', 'quarantine', 'expired'])
                  ->default('good')
                  ->after('supplier_batch')
                  ->comment('Статус качества товара');
            
            // Количество в резерве
            $table->integer('reserved_quantity')->default(0)->after('quality_status')->comment('Количество в резерве');
            
            // Доступное количество (вычисляемое поле, но можем хранить для производительности)
            $table->integer('available_quantity')->default(0)->after('reserved_quantity')->comment('Доступное количество');
            
            // Дата последнего движения
            $table->dateTime('last_movement_date')->nullable()->after('available_quantity')->comment('Дата последнего движения');
            
            // Место хранения
            $table->string('storage_location')->nullable()->after('last_movement_date')->comment('Место хранения (ячейка, стеллаж)');
            
            // Индексы для быстрого поиска
            $table->index(['lot_number', 'expiry_date']);
            $table->index(['supplier_batch']);
            $table->index(['quality_status', 'expiry_date']);
            $table->index(['reserved_quantity', 'available_quantity']);
            $table->index(['last_movement_date']);
            $table->index(['storage_location']);
        });
    }

    public function down(): void
    {
        Schema::table('warehouse_items', function (Blueprint $table) {
            $table->dropIndex(['lot_number', 'expiry_date']);
            $table->dropIndex(['supplier_batch']);
            $table->dropIndex(['quality_status', 'expiry_date']);
            $table->dropIndex(['reserved_quantity', 'available_quantity']);
            $table->dropIndex(['last_movement_date']);
            $table->dropIndex(['storage_location']);
            
            $table->dropColumn([
                'lot_number',
                'expiry_date',
                'supplier_batch',
                'quality_status',
                'reserved_quantity',
                'available_quantity',
                'last_movement_date',
                'storage_location'
            ]);
        });
    }
};
