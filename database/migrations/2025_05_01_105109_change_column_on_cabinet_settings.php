<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cabinet_settings', function (Blueprint $table) {
            $table->dropColumn('image');
            $table->foreignUuid('logo_image_id')->nullable()->references('id')->on('files');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cabinet_settings', function (Blueprint $table) {
            $table->dropForeign(['logo_image_id']);
            $table->dropColumn('logo_image_id');
            $table->string('image')->nullable();
        });
    }
};
