<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_reservations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            // Связь с партией товара на складе
            $table->foreignUuid('warehouse_item_id')->constrained()->cascadeOnDelete();
            
            // Связь с позицией заказа
            $table->foreignUuid('customer_order_item_id')->constrained()->cascadeOnDelete();
            
            // Количество зарезервированного товара из данной партии
            $table->integer('reserved_quantity');
            
            // Дата резервирования
            $table->dateTime('reserved_at');
            
            // Статус резерва (reserved, shipped, cancelled)
            $table->string('status')->default('reserved');
            
            // Тип резервирования
            $table->enum('reservation_type', ['order', 'production', 'transfer', 'marketing', 'quality'])
                  ->default('order')
                  ->comment('Тип резервирования');
            
            // Документ-основание для резерва
            $table->string('document_type')->nullable()->comment('Тип документа-основания');
            $table->uuid('document_id')->nullable()->comment('ID документа-основания');
            
            // Приоритет резерва (1-10, где 1 - самый высокий)
            $table->integer('priority')->default(5)->comment('Приоритет резерва');
            
            // Срок действия резерва
            $table->dateTime('expires_at')->nullable()->comment('Срок действия резерва');
            
            // Автоматически снимать при истечении
            $table->boolean('auto_release')->default(true)->comment('Автоматически снимать при истечении');
            
            // Индексы для быстрого поиска
            $table->index(['warehouse_item_id', 'status']);
            $table->index(['customer_order_item_id', 'status']);
            $table->index(['reserved_at', 'status']);
            $table->index(['expires_at', 'auto_release']);
            $table->index(['document_type', 'document_id']);
            $table->index(['reservation_type', 'priority']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_reservations');
    }
};
