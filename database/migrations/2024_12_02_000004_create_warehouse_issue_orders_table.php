<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_issue_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            // Основные поля документа
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->foreignUuid('warehouse_id')->constrained();
            
            // Номер и дата документа
            $table->string('number')->nullable();
            $table->dateTime('date_from');
            
            // Статус и проведение
            $table->foreignUuid('status_id')->nullable()->constrained();
            $table->boolean('held')->default(false);
            
            // Документ-основание
            $table->string('document_basis_type')->nullable()->comment('Тип документа-основания');
            $table->uuid('document_basis_id')->nullable()->comment('ID документа-основания');
            
            // Причина списания
            $table->enum('write_off_reason', [
                'defective', 'expired', 'shortage', 'internal_use', 
                'return_to_supplier', 'damage', 'other'
            ])->comment('Причина списания');
            $table->text('reason_description')->nullable()->comment('Описание причины списания');
            
            // Общие суммы
            $table->integer('total_quantity')->default(0)->comment('Общее количество товаров');
            $table->string('total_cost')->default('0')->comment('Общая стоимость');
            
            // Комментарий
            $table->text('comment')->nullable();
            
            // Индексы
            $table->index(['warehouse_id', 'date_from']);
            $table->index(['document_basis_type', 'document_basis_id']);
            $table->index(['held', 'date_from']);
            $table->index('write_off_reason');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_issue_orders');
    }
};
