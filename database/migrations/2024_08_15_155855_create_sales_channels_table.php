<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_channels', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();
            $table->dateTime('archived_at')->nullable();

            $table->foreignUuid('cabinet_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->foreignUuid('sales_channel_type_id')->constrained();
            $table->text('description')->nullable();

            $table->foreignUuid('employee_id')->nullable()->constrained();
            $table->foreignUuid('department_id')->nullable()->constrained();

            $table->boolean('is_default')->default(false);
            $table->boolean('is_common')->default(false);

            $table->integer('sort')->default(0);
        });

        Schema::table('departments', function (Blueprint $table) {
            $table->foreignUuid('sales_channel_id')->nullable()->constrained();
            $table->boolean('is_common')->default(false);

            $table->integer('sort')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            $table->dropForeign(['sales_channel_id']);
        });
        Schema::dropIfExists('sales_channels');
    }
};
