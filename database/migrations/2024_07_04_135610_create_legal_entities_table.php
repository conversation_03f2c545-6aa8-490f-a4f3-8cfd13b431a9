<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_entities', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->softDeletes();
            $table->timestamps();
            $table->dateTime('archived_at')->nullable();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            //$table->string('logo')->nullable();

            $table->string('short_name');
            $table->string('code')->nullable();
            $table->string('phone', 15)->nullable();
            $table->string('fax')->nullable();
            $table->string('email')->nullable();

            $table->string('discount_card')->nullable();

            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_default')->default(false);
            $table->boolean('shared_access')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_entities');
    }
};
