<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_siz_types', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_siz_name_id')->constrained()->cascadeOnDelete();
            $table->string('code');                                 // Коды типа для Средств индивидуальной защиты
            $table->string('title');                                // название типа для Средств индивидуальной защиты
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_siz_types');
    }
};
