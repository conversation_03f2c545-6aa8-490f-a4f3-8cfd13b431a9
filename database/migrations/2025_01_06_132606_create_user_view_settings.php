<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_view_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name');
            $table->foreignUuid('employee_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();

            $table->unique(['name', 'employee_id', 'cabinet_id']);
            $table->index(['employee_id', 'cabinet_id']);

            $table->json('settings');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_view_settings');
    }
};
