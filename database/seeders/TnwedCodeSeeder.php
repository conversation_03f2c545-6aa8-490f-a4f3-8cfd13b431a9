<?php

namespace Database\Seeders;

use App\Models\TnwedCode;
use Illuminate\Database\Seeder;

class TnwedCodeSeeder extends Seeder
{
    public function dataTnwed($data, $id): void
    {

        foreach($data as $item) {

            TnwedCode::create([
                'product_type_id' => $id,
                'code' => $item
            ]);

        }

    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $data3 = [
            '6401100000',
            '6401921000',
            '6401929000',
            '6401990000',
            '6402121000',
            '6402129000',
            '6402190000',
            '6402200000',
            '6402911000',
            '6402919000',
            '6402990500',
            '6402991000',
            '6402993100',
            '6402993900',
            '6402995000',
            '6402999100',
            '6402999300',
            '6402999600',
            '6402999800',
            '6403120000',
            '6403190000',
            '6403200000',
            '6403400000',
            '6403510500',
            '6403511100',
            '6403511500',
            '6403511900',
            '6403519100',
            '6403519500',
            '6403519900',
            '6403590500',
            '6403591100',
            '6403593100',
            '6403593500',
            '6403593900',
            '6403595000',
            '6403599100',
            '6403599500',
            '6403599900',
            '6403910500',
            '6403911100',
            '6403911300',
            '6403911600',
            '6403911800',
            '6403919100',
            '6403919300',
            '6403919600',
            '6403919800',
            '6403990500',
            '6403991100',
            '6403993100',
            '6403993300',
            '6403993600',
            '6403993800',
            '6403995000',
            '6403999100',
            '6403999300',
            '6403999600',
            '6403999800',
            '6404110000',
            '6404191000',
            '6404199000',
            '6404201000',
            '6404209000',
            '6405100001',
            '6405100009',
            '6405201000',
            '6405209100',
            '6405209900',
            '6405901000',
            '6405909000',
        ];

        $data4 = [
            '4203100001',
            '4203100009',
            '4304000000',
            '6101201000',
            '6101209000',
            '6101301000',
            '6101309000',
            '6101902000',
            '6101908000',
            '6102101000',
            '6102109000',
            '6102201000',
            '6102209000',
            '6102301000',
            '6102309000',
            '6102901000',
            '6102909000',
            '6103100000',
            '6103101000',
            '6103109000',
            '6103220000',
            '6103230000',
            '6103290000',
            '6103290001',
            '6103290009',
            '6103310000',
            '6103320000',
            '6103330000',
            '6103390000',
            '6103410000',
            '6103420000',
            '6103420001',
            '6103420009',
            '6103430000',
            '6103430001',
            '6103430009',
            '6103490000',
            '6103490001',
            '6103490002',
            '6103490009',
            '6104130000',
            '6104190000',
            '6104192000',
            '6104199001',
            '6104199009',
            '6104220000',
            '6104230000',
            '6104290000',
            '6104291000',
            '6104299000',
            '6104310000',
            '6104320000',
            '6104330000',
            '6104390000',
            '6104410000',
            '6104420000',
            '6104430000',
            '6104440000',
            '6104490000',
            '6104510000',
            '6104520000',
            '6104530000',
            '6104590000',
            '6104610000',
            '6104610001',
            '6104610009',
            '6104620000',
            '6104630000',
            '6104690000',
            '6104690001',
            '6104690002',
            '6104690009',
            '6105100000',
            '6105201000',
            '6105209000',
            '6105901000',
            '6105909000',
            '6106100000',
            '6106200000',
            '6106901000',
            '6106903000',
            '6106905000',
            '6106909000',
            '6110111000',
            '6110113000',
            '6110119000',
            '6110121000',
            '6110121001',
            '6110121009',
            '6110129000',
            '6110129001',
            '6110129009',
            '6110191000',
            '6110191001',
            '6110191009',
            '6110199000',
            '6110199001',
            '6110199009',
            '6110201000',
            '6110209100',
            '6110209900',
            '6110301000',
            '6110309100',
            '6110309900',
            '6110901000',
            '6110909000',
            '6112110000',
            '6112120000',
            '6112190000',
            '6112200000',
            '6113001000',
            '6113009000',
            '6201200000',
            '6201300000',
            '6201400000',
            '6201900000',
            '6202200000',
            '6202300000',
            '6202400001',
            '6202400009',
            '6202900001',
            '6202900009',
            '6203110000',
            '6203120000',
            '6203191000',
            '6203193000',
            '6203199000',
            '6203221000',
            '6203228000',
            '6203231000',
            '6203238000',
            '6203291100',
            '6203291800',
            '6203293000',
            '6203298000',
            '6203299000',
            '6203310000',
            '6203321000',
            '6203329000',
            '6203331000',
            '6203339000',
            '6203391100',
            '6203391900',
            '6203399000',
            '6203411000',
            '6203413000',
            '6203419000',
            '6203421100',
            '6203423100',
            '6203423300',
            '6203423500',
            '6203425100',
            '6203425900',
            '6203429000',
            '6203431100',
            '6203431900',
            '6203433100',
            '6203433900',
            '6203439000',
            '6203491100',
            '6203491900',
            '6203493100',
            '6203493900',
            '6203495000',
            '6203499000',
            '6204110000',
            '6204120000',
            '6204130000',
            '6204191000',
            '6204199000',
            '6204210000',
            '6204221000',
            '6204228000',
            '6204231000',
            '6204238000',
            '6204291100',
            '6204291800',
            '6204299000',
            '6204310000',
            '6204321000',
            '6204329000',
            '6204331000',
            '6204339000',
            '6204391100',
            '6204391900',
            '6204399000',
            '6204410000',
            '6204420000',
            '6204430000',
            '6204440000',
            '6204490000',
            '6204491000',
            '6204499000',
            '6204510000',
            '6204520000',
            '6204530000',
            '6204591000',
            '6204599000',
            '6204611000',
            '6204618500',
            '6204621100',
            '6204623100',
            '6204623300',
            '6204623900',
            '6204625100',
            '6204625900',
            '6204629000',
            '6204631100',
            '6204631800',
            '6204633100',
            '6204633900',
            '6204639000',
            '6204691100',
            '6204691800',
            '6204693100',
            '6204693900',
            '6204695000',
            '6204699000',
            '6205200000',
            '6205300000',
            '6205901000',
            '6205908000',
            '6205908001',
            '6205908009',
            '6206100000',
            '6206200000',
            '6206300000',
            '6206400000',
            '6206901000',
            '6206909000',
            '6210101000',
            '6210109000',
            '6210109200',
            '6210109800',
            '6210200000',
            '6210300000',
            '6210400000',
            '6210500000',
            '6211200000',
            '6211321000',
            '6211323100',
            '6211324100',
            '6211324200',
            '6211329000',
            '6211331000',
            '6211333100',
            '6211334100',
            '6211334200',
            '6211339000',
            '6211390000',
            '6211421000',
            '6211423100',
            '6211424100',
            '6211424200',
            '6211429000',
            '6211431000',
            '6211433100',
            '6211434100',
            '6211434200',
            '6211439000',
            '6211490000',
            '6211490001',
            '6211490009',
            '6214100000',
            '6214200000',
            '6214300000',
            '6214400000',
            '6214900000',
            '6215100000',
            '6215200000',
            '6215900000',

        ];

        $data5 = [
            '6302100001',
            '6302100009',
            '6302210000',
            '6302221000',
            '6302229000',
            '6302291000',
            '6302299000',
            '6302310001',
            '6302310009',
            '6302321000',
            '6302329000',
            '6302392001',
            '6302392009',
            '6302399000',
            '6302400000',
            '6302510001',
            '6302510009',
            '6302531000',
            '6302539000',
            '6302591000',
            '6302599000',
            '6302600000',
            '6302910000',
            '6302931000',
            '6302939000',
            '6302991000',
            '6302999000',
        ];

        $data6 = [
            '3303001000',
            '3303009000',
        ];

        $data7 = [
            '9006300000',
            '9006400000',
            '9006510000',
            '9006520001',
            '9006520009',
            '9006531000',
            '9006538001',
            '9006538008',
            '9006538009',
            '9006590001',
            '9006590008',
            '9006590009',
            '9006610000',
            '9006690001',
            '9006690009',
        ];

        $data8 = [
            '4011400000',
            '4011700000',
            '4011800000',
            '4011900000',
        ];

        $data9 = [
            '401101000',
            '401109000',
            '401201101',
            '401201109',
            '401201900',
            '401209101',
            '401209109',
            '401209900',
            '401401000',
            '401409000',
            '401501100',
            '401501900',
            '401503100',
            '401503900',
            '401509100',
            '401509900',
            '402101100',
            '402101900',
            '402109100',
            '402109900',
            '402211100',
            '402211800',
            '402219100',
            '402219900',
            '402291100',
            '402291500',
            '402291900',
            '402299100',
            '402299900',
            '402911000',
            '402913000',
            '402915100',
            '402915900',
            '402919100',
            '402919900',
            '402991000',
            '402993100',
            '402993900',
            '402999100',
            '403101100',
            '403101300',
            '403101900',
            '403103100',
            '403103300',
            '403103900',
            '403105100',
            '403105300',
            '403105900',
            '403109100',
            '403109300',
            '403109900',
            '403201100',
            '403201300',
            '403201900',
            '403203100',
            '403203300',
            '403203900',
            '403205100',
            '403205300',
            '403205900',
            '403207100',
            '403207300',
            '403207900',
            '403209001',
            '403209009',
            '403901100',
            '403901300',
            '403901900',
            '403903100',
            '403903300',
            '403903900',
            '403905101',
            '403905102',
            '403905109',
            '403905301',
            '403905302',
            '403905309',
            '403905900',
            '403906100',
            '403906300',
            '403906900',
            '403907100',
            '403907300',
            '403907900',
            '403909100',
            '403909300',
            '403909900',
            '404100200',
            '404100400',
            '404100600',
            '404101201',
            '404101209',
            '404101400',
            '404101601',
            '404101609',
            '404102600',
            '404102800',
            '404103200',
            '404103400',
            '404103600',
            '404103800',
            '404104800',
            '404105200',
            '404105400',
            '404105600',
            '404105800',
            '404106200',
            '404107200',
            '404107400',
            '404107600',
            '404107800',
            '404108200',
            '404108400',
            '404902100',
            '404902300',
            '404902900',
            '404908100',
            '404908300',
            '404908900',
            '405101100',
            '405101900',
            '405103000',
            '405105000',
            '405109000',
            '405201000',
            '405203000',
            '405209000',
            '405901000',
            '405909000',
            '406103000',
            '406105001',
            '406105002',
            '406105009',
            '406108000',
            '406200000',
            '406301000',
            '406303100',
            '406303900',
            '406309000',
            '406401000',
            '406405000',
            '406409000',
            '406900100',
            '406901300',
            '406901500',
            '406901700',
            '406901800',
            '406902100',
            '406902300',
            '406902500',
            '406902900',
            '406903201',
            '406903209',
            '406903500',
            '406903700',
            '406903900',
            '406905000',
            '406906100',
            '406906300',
            '406906900',
            '406907300',
            '406907400',
            '406907500',
            '406907600',
            '406907800',
            '406907900',
            '406908100',
            '406908200',
            '406908400',
            '406908500',
            '406908600',
            '406908900',
            '406909200',
            '406909300',
            '406909901',
            '406909909',
            '2105001000',
            '2105009100',
            '2105009900',
            '2202999100',
            '2202999500',
            '2202999900',

        ];

        $data10 = [
            '2201101100',
            '2201101900',
            '2201109000',
            '2201900000',
        ];

        $data11 = [
            '2402100000',
            '2402201000',
            '2402209000',
            '2403110000',
            '2403191000',
            '2403991000',
        ];

        $data12 = [
            '2403999008',
            '2403999009',
            '2404110001',
            '2404110009',
            '2404120000',
            '2404190001',
            '2404190009',
        ];

        $data15 = [
            '8421392008',
            '8421398006',
            '8539490000',
            '8713100000',
            '8713900000',
            '9018200000',
            '9021101000',
            '9021400000',
            '9021909001',
            '9022120000',
            '9022130000',
            '9022140000',
            '9022190000',
            '9619008901',
            '9619008909',
        ];


        $this->dataTnwed($data3, 3);
        $this->dataTnwed($data4, 4);
        $this->dataTnwed($data5, 5);
        $this->dataTnwed($data6, 6);
        $this->dataTnwed($data7, 7);
        $this->dataTnwed($data8, 8);
        $this->dataTnwed($data9, 9);
        $this->dataTnwed($data10, 10);
        $this->dataTnwed($data11, 11);
        $this->dataTnwed($data12, 12);
        $this->dataTnwed($data15, 15);

    }


}
