<?php

namespace Database\Factories;

use App\Models\Warehouse;
use App\Models\WarehouseCellGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseCellGroup>
 */
class WarehouseCellGroupFactory extends Factory
{
    protected $model = WarehouseCellGroup::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'address' => fake()->streetAddress(),
            'description' => fake()->sentence(),
            'warehouse_id' => Warehouse::factory(),
            'parent_id' => null,
        ];
    }

    /**
     * Create a warehouse cell group with a parent.
     */
    public function withParent(string $parentId): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentId,
        ]);
    }
} 