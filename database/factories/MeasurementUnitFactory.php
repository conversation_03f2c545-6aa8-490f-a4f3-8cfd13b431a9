<?php

namespace Database\Factories;

use App\Models\MeasurementUnit;
use App\Models\MeasurementUnitGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

class MeasurementUnitFactory extends Factory
{
    protected $model = MeasurementUnit::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->word(),
            'conversion_factor' => $this->faker->randomFloat(4, 0.001, 1000),
            'group_id' => MeasurementUnitGroup::factory(),
        ];
    }
}
