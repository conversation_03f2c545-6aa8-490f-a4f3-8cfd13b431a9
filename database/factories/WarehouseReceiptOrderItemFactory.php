<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\VatRate;
use App\Models\WarehouseReceiptOrder;
use App\Models\WarehouseReceiptOrderItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseReceiptOrderItem>
 */
class WarehouseReceiptOrderItemFactory extends Factory
{
    protected $model = WarehouseReceiptOrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 100);
        $unitPrice = $this->faker->randomFloat(2, 10, 1000);
        $totalPrice = $quantity * $unitPrice;

        return [
            'receipt_order_id' => WarehouseReceiptOrder::factory(),
            'product_id' => Product::factory(),
            'quantity' => $quantity,
            'unit_price' => number_format($unitPrice, 2, '.', ''),
            'total_price' => number_format($totalPrice, 2, '.', ''),
            'batch_number' => 'BATCH-' . $this->faker->unique()->numberBetween(1000, 9999),
            'lot_number' => $this->faker->optional()->regexify('LOT[0-9]{4}'),
            'expiry_date' => $this->faker->optional(0.7)->dateTimeBetween('+30 days', '+2 years')->format('Y-m-d'),
            'quality_status' => $this->faker->randomElement(['good', 'defective', 'quarantine']),
            'storage_location' => $this->faker->optional()->regexify('[A-Z][0-9]-[0-9]{2}'),
            'vat_rate_id' => VatRate::factory(),
        ];
    }

    /**
     * Indicate that the item has good quality.
     */
    public function goodQuality(): static
    {
        return $this->state(fn (array $attributes) => [
            'quality_status' => 'good',
        ]);
    }

    /**
     * Indicate that the item is defective.
     */
    public function defective(): static
    {
        return $this->state(fn (array $attributes) => [
            'quality_status' => 'defective',
        ]);
    }

    /**
     * Indicate that the item is in quarantine.
     */
    public function quarantine(): static
    {
        return $this->state(fn (array $attributes) => [
            'quality_status' => 'quarantine',
        ]);
    }

    /**
     * Set specific batch number.
     */
    public function withBatch(string $batchNumber): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_number' => $batchNumber,
        ]);
    }

    /**
     * Set expiry date.
     */
    public function withExpiry(string $expiryDate): static
    {
        return $this->state(fn (array $attributes) => [
            'expiry_date' => $expiryDate,
        ]);
    }

    /**
     * Set storage location.
     */
    public function withLocation(string $location): static
    {
        return $this->state(fn (array $attributes) => [
            'storage_location' => $location,
        ]);
    }

    /**
     * Set specific quantity and recalculate total.
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(function (array $attributes) use ($quantity) {
            $unitPrice = (float) $attributes['unit_price'];
            $totalPrice = $quantity * $unitPrice;

            return [
                'quantity' => $quantity,
                'total_price' => number_format($totalPrice, 2, '.', ''),
            ];
        });
    }

    /**
     * Set specific unit price and recalculate total.
     */
    public function withUnitPrice(float $unitPrice): static
    {
        return $this->state(function (array $attributes) use ($unitPrice) {
            $quantity = $attributes['quantity'];
            $totalPrice = $quantity * $unitPrice;

            return [
                'unit_price' => number_format($unitPrice, 2, '.', ''),
                'total_price' => number_format($totalPrice, 2, '.', ''),
            ];
        });
    }
}
