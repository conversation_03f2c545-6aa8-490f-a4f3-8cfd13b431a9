<?php

namespace Database\Factories;

use App\Models\Contractor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContractorAccount>
 */
class ContractorAccountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'contractor_id' => Contractor::factory(),
            'is_main' => $this->faker->boolean,
            'bik' => $this->faker->randomElement(['*********', '*********']),
            'correspondent_account' => random_int(1,99999),
            'payment_account' => random_int(1,99999),
            'balance' => random_int(1,********),
            'bank' => $this->faker->randomElement(['Сбер','Тинькофф','РНКБ','ВТБ','Карман']),
            'address' => $this->faker->address,
        ];
    }
}
