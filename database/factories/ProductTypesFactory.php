<?php

namespace Database\Factories;

use App\Models\Employee;
use App\Models\Department;
use App\Models\ProductTypes;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductTypesFactory extends Factory
{
    protected $model = ProductTypes::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory()
        ];
    }
}
