<?php

namespace Database\Factories;

use App\Enums\Api\Internal\ComissionTypeEnum;
use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\Contract;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\SalesChannel;
use App\Models\Status;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReceivedComissionReport>
 */
class ReceivedComissionReportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'number' => $this->faker->unique()->numberBetween(100000, 999999),
            'date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'status_id' => Status::factory(),
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_common' => $this->faker->boolean(),
            'legal_seller_id' => LegalEntity::factory(),
            'contractor_comissioner_id' => Contractor::factory(),
            'incoming_number' => $this->faker->unique()->numberBetween(100000, 999999),
            'incoming_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'sales_channel_id' => SalesChannel::factory(),
            'currency_id' => CabinetCurrency::factory(),
            'period_from' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'period_to' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'contract_id' => Contract::factory(),
            'comission_type' => $this->faker->randomElement(ComissionTypeEnum::cases())->value,
            'other_services_price' => $this->faker->randomFloat(2, 0, 100000),
        ];
    }
}
