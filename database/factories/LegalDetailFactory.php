<?php

namespace Database\Factories;

use App\Models\VatRate;
use App\Models\LegalEntity;
use App\Models\ProfitTaxRate;
use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\LegalEntityTaxation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LegalDetail>
 */
class LegalDetailFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'legal_entity_id' => LegalEntity::factory(),
            'taxation_type' => $this->faker->randomElement(LegalEntityTaxation::class)->value,
            'tax_rate' => ProfitTaxRate::factory(),
            'vat_rate' => VatRate::factory(),
            'type' => $this->faker->randomElement(LegalEntityType::class)->value,
            'prefix' => $this->faker->word,
            'inn' => random_int(*********000,*********999),
            'kpp' => random_int(*********,*********),
            'ogrn' => random_int(*********000000,*********999999),
            'okpo' => random_int(*********0,*********9),
            'full_name' => $this->faker->firstName . ' ' . $this->faker->name,
            'firstname' => $this->faker->firstName,
            'patronymic' => $this->faker->lastName,
            'ogrnip' => $this->faker->word,
            'certificate_number' => $this->faker->word,
            'certificate_date' => $this->faker->date,
        ];
    }
}
