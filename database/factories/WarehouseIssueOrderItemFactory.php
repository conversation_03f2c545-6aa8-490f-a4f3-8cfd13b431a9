<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\VatRate;
use App\Models\WarehouseIssueOrder;
use App\Models\WarehouseIssueOrderItem;
use App\Models\WarehouseItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseIssueOrderItem>
 */
class WarehouseIssueOrderItemFactory extends Factory
{
    protected $model = WarehouseIssueOrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 50);
        $unitPrice = $this->faker->randomFloat(2, 10, 1000);
        $totalPrice = $quantity * $unitPrice;

        return [
            'issue_order_id' => WarehouseIssueOrder::factory(),
            'product_id' => Product::factory(),
            'warehouse_item_id' => WarehouseItem::factory(),
            'quantity' => $quantity,
            'unit_price' => number_format($unitPrice, 2, '.', ''),
            'total_price' => number_format($totalPrice, 2, '.', ''),
            'batch_number' => 'BATCH-' . $this->faker->numberBetween(1000, 9999),
            'lot_number' => $this->faker->optional()->regexify('LOT[0-9]{4}'),
            'expiry_date' => $this->faker->optional(0.7)->dateTimeBetween('-1 year', '+1 year')->format('Y-m-d'),
            'vat_rate_id' => VatRate::factory(),
        ];
    }

    /**
     * Set specific batch number.
     */
    public function withBatch(string $batchNumber): static
    {
        return $this->state(fn (array $attributes) => [
            'batch_number' => $batchNumber,
        ]);
    }

    /**
     * Set expiry date.
     */
    public function withExpiry(string $expiryDate): static
    {
        return $this->state(fn (array $attributes) => [
            'expiry_date' => $expiryDate,
        ]);
    }

    /**
     * Set specific quantity and recalculate total.
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(function (array $attributes) use ($quantity) {
            $unitPrice = (float) $attributes['unit_price'];
            $totalPrice = $quantity * $unitPrice;

            return [
                'quantity' => $quantity,
                'total_price' => number_format($totalPrice, 2, '.', ''),
            ];
        });
    }

    /**
     * Set specific unit price and recalculate total.
     */
    public function withUnitPrice(float $unitPrice): static
    {
        return $this->state(function (array $attributes) use ($unitPrice) {
            $quantity = $attributes['quantity'];
            $totalPrice = $quantity * $unitPrice;

            return [
                'unit_price' => number_format($unitPrice, 2, '.', ''),
                'total_price' => number_format($totalPrice, 2, '.', ''),
            ];
        });
    }

    /**
     * Set specific warehouse item.
     */
    public function forWarehouseItem(WarehouseItem $warehouseItem): static
    {
        return $this->state(fn (array $attributes) => [
            'warehouse_item_id' => $warehouseItem->id,
            'product_id' => $warehouseItem->product_id,
            'batch_number' => $warehouseItem->batch_number,
            'lot_number' => $warehouseItem->lot_number,
            'expiry_date' => $warehouseItem->expiry_date?->format('Y-m-d'),
        ]);
    }
}
