<?php

namespace Database\Factories;

use App\Models\Warehouse;
use App\Models\WarehouseOrderScheme;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseOrderScheme>
 */
class WarehouseOrderSchemeFactory extends Factory
{
    protected $model = WarehouseOrderScheme::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'warehouse_id' => Warehouse::factory(),
            'on_coming_from' => $this->faker->optional()->date(),
            'on_shipment_from' => $this->faker->optional()->date(),
            'control_operational_balances' => $this->faker->boolean(),
        ];
    }

    /**
     * Indicate that the order scheme is active for acceptances.
     */
    public function withAcceptances(): static
    {
        return $this->state(fn (array $attributes) => [
            'on_coming_from' => now()->subDays(30)->toDateString(),
        ]);
    }

    /**
     * Indicate that the order scheme is active for shipments.
     */
    public function withShipments(): static
    {
        return $this->state(fn (array $attributes) => [
            'on_shipment_from' => now()->subDays(30)->toDateString(),
        ]);
    }

    /**
     * Indicate that the order scheme is active for both acceptances and shipments.
     */
    public function withBoth(): static
    {
        return $this->state(fn (array $attributes) => [
            'on_coming_from' => now()->subDays(30)->toDateString(),
            'on_shipment_from' => now()->subDays(30)->toDateString(),
        ]);
    }

    /**
     * Indicate that the order scheme is not active.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'on_coming_from' => null,
            'on_shipment_from' => null,
        ]);
    }
}
