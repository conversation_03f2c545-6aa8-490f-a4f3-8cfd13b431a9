<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\SalesChannel;
use App\Traits\HasOrderedUuid;
use App\Models\SalesChannelType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SalesChannel>
 */
class SalesChannelFactory extends Factory
{
    use HasOrderedUuid;

    protected $model = SalesChannel::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
            'archived_at' => null,
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->word,
            'description' => $this->faker->text,
            'sales_channel_type_id' => SalesChannelType::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_default' => false,
            'sort' => 0
        ];
    }
}
