<?php

namespace Database\Factories;

use App\Models\BinItem;
use App\Models\Cabinet;
use App\Models\Acceptance;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

class BinItemFactory extends Factory
{
    use HasOrderedUuid;
    protected $model = BinItem::class;

    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'record_id' => Acceptance::factory()->create(
                [
                    'deleted_at' => now()
                ]
            )->id,
            'table_name' => 'acceptances',
            'record_name' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
