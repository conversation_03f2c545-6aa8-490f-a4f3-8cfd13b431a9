<?php

namespace Database\Factories;

use App\Models\Contractor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContractorAddress>
 */
class ContractorAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'contractor_id' => Contractor::factory(),
            'postcode' => $this->faker->postcode,
            'country' => $this->faker->country,
            'region' => $this->faker->country,
            'city' => $this->faker->city,
            'street' => $this->faker->streetName,
            'house' => $this->faker->streetAddress,
            'office' => random_int(1,10000),
            'other' => $this->faker->text(255),
            'comment' => $this->faker->text
        ];
    }
}
