<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Acceptance;
use App\Models\Contractor;
use Illuminate\Support\Str;
use App\Models\LegalEntity;
use App\Models\Department;
use App\Models\CabinetCurrency;
use Illuminate\Database\Eloquent\Factories\Factory;

class AcceptanceFactory extends Factory
{
    protected $model = Acceptance::class;

    public function definition(): array
    {
        return [
            'id' => Str::orderedUuid()->toString(),
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'number' => random_int(1, 100000),
            'date_from' => $this->faker->date(),
            'status_id' => null,
            'held' => $this->faker->boolean(),
            'legal_entity_id' => LegalEntity::factory(),
            'contractor_id' => Contractor::factory(),
            'warehouse_id' => Warehouse::factory(),
            'incoming_number' => mt_rand(1, 100000),
            'incoming_date' => $this->faker->date,
            'currency_id' => CabinetCurrency::factory(),
            'comment' => $this->faker->text(),
            'price_includes_vat' => $this->faker->boolean,
            'overhead_cost' => random_int(1, 1000)
        ];
    }
}
