<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\Warehouse;
use App\Models\WarehouseReceiptOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseReceiptOrder>
 */
class WarehouseReceiptOrderFactory extends Factory
{
    protected $model = WarehouseReceiptOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'warehouse_id' => Warehouse::factory(),
            'number' => 'ПО-' . $this->faker->unique()->numberBetween(1000, 9999),
            'date_from' => $this->faker->date(),
            'status_id' => Status::factory(),
            'held' => $this->faker->boolean(30), // 30% проведенных
            'document_basis_type' => $this->faker->optional()->randomElement(['invoice', 'delivery_note', 'purchase_order']),
            'document_basis_id' => $this->faker->optional()->uuid(),
            'reason' => $this->faker->optional()->sentence(),
            'total_quantity' => $this->faker->numberBetween(1, 1000),
            'total_cost' => $this->faker->randomFloat(2, 100, 50000),
            'comment' => $this->faker->optional()->text(200),
        ];
    }

    /**
     * Indicate that the order is held (проведен).
     */
    public function held(): static
    {
        return $this->state(fn (array $attributes) => [
            'held' => true,
        ]);
    }

    /**
     * Indicate that the order is not held (не проведен).
     */
    public function notHeld(): static
    {
        return $this->state(fn (array $attributes) => [
            'held' => false,
        ]);
    }

    /**
     * Set specific reason.
     */
    public function withReason(string $reason): static
    {
        return $this->state(fn (array $attributes) => [
            'reason' => $reason,
        ]);
    }

    /**
     * From supplier.
     */
    public function fromSupplier(): static
    {
        return $this->withReason('Поступление от поставщика')->state([
            'document_basis_type' => 'invoice',
        ]);
    }

    /**
     * Return from customer.
     */
    public function returnFromCustomer(): static
    {
        return $this->withReason('Возврат от клиента')->state([
            'document_basis_type' => 'return_note',
        ]);
    }

    /**
     * Production output.
     */
    public function production(): static
    {
        return $this->withReason('Выпуск продукции')->state([
            'document_basis_type' => 'production_order',
        ]);
    }
}
