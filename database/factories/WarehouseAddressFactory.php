<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseAddress>
 */
class WarehouseAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cabinet = Cabinet::factory()->create();
        return [
            'cabinet_id' => $cabinet->id,
            'country_id' => Country::factory()->create([
                'cabinet_id' => $cabinet->id,
            ]),
            'postcode' => $this->faker->postcode,
            'region' => $this->faker->country,
            'city' => $this->faker->city,
            'street' => $this->faker->streetName,
            'house' => $this->faker->streetSuffix,
            'office' => $this->faker->streetSuffix,
            'other' => $this->faker->streetAddress,
            'comment' => $this->faker->text,
        ];
    }
}
