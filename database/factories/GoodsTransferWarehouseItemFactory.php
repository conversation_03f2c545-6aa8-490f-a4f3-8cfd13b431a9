<?php

namespace Database\Factories;

use App\Models\GoodsTransferItem;
use App\Models\WarehouseItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GoodsTransferWarehouseItem>
 */
class GoodsTransferWarehouseItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'goods_transfer_item_id' => GoodsTransferItem::factory(),
            'warehouse_item_id' => WarehouseItem::factory(),
            'quantity' => $this->faker->numberBetween(1, 100),
            'transfer_date' => now(),
        ];
    }
}
