<?php

namespace Database\Factories;

use App\Models\Status;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Department;
use App\Models\Contractor;
use App\Models\LegalEntity;
use App\Models\SalesChannel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerOrder>
 */
class CustomerOrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_common' => $this->faker->boolean,
            'number' => $this->faker->word . random_int(0,10000),
            'date_from' => $this->faker->date,
            'payment_status' => 'unpaid',
            'status_id' => Status::factory(),
            'held' => $this->faker->boolean,
            'reserve' => $this->faker->boolean,
            'legal_entity_id' => LegalEntity::factory(),
            'contractor_id' => Contractor::factory(),
            'plan_date' => $this->faker->date,
            'sales_channel_id' => SalesChannel::factory(),
            'warehouse_id' => Warehouse::factory(),
            'total_price' => random_int(0, 999999),
            'comment' => $this->faker->text,
            'has_vat' => true,
            'price_includes_vat' => true,
        ];
    }
}
