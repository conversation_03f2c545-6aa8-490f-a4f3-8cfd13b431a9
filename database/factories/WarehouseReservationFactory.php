<?php

namespace Database\Factories;

use App\Models\CustomerOrderItem;
use App\Models\WarehouseItem;
use App\Models\WarehouseReservation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseReservation>
 */
class WarehouseReservationFactory extends Factory
{
    protected $model = WarehouseReservation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'warehouse_item_id' => WarehouseItem::factory(),
            'customer_order_item_id' => CustomerOrderItem::factory(),
            'reserved_quantity' => $this->faker->numberBetween(1, 100),
            'reservation_type' => $this->faker->randomElement([
                'order', 'production', 'transfer', 'marketing', 'quality'
            ]),
            'document_type' => $this->faker->randomElement([
                'customer_order', 'production_order', 'transfer_order'
            ]),
            'document_id' => $this->faker->uuid(),
            'priority' => $this->faker->numberBetween(1, 10),
            'status' => $this->faker->randomElement(['reserved', 'shipped', 'cancelled']),
            'reserved_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'expires_at' => $this->faker->optional(0.8)->dateTimeBetween('now', '+90 days'),
            'auto_release' => $this->faker->boolean(70),
        ];
    }

    /**
     * Indicate that the reservation is active (reserved).
     */
    public function reserved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'reserved',
            'expires_at' => $this->faker->dateTimeBetween('+1 day', '+30 days'),
        ]);
    }

    /**
     * Indicate that the reservation is shipped.
     */
    public function shipped(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'shipped',
        ]);
    }

    /**
     * Indicate that the reservation is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }

    /**
     * Indicate that the reservation is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'reserved',
            'expires_at' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
        ]);
    }

    /**
     * Set specific reservation type.
     */
    public function forOrder(): static
    {
        return $this->state(fn (array $attributes) => [
            'reservation_type' => 'order',
            'document_type' => 'customer_order',
            'priority' => $this->faker->numberBetween(1, 5),
        ]);
    }

    /**
     * Set production reservation type.
     */
    public function forProduction(): static
    {
        return $this->state(fn (array $attributes) => [
            'reservation_type' => 'production',
            'document_type' => 'production_order',
            'priority' => $this->faker->numberBetween(3, 7),
        ]);
    }

    /**
     * Set transfer reservation type.
     */
    public function forTransfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'reservation_type' => 'transfer',
            'document_type' => 'transfer_order',
            'priority' => $this->faker->numberBetween(4, 8),
        ]);
    }

    /**
     * Set high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(1, 3),
        ]);
    }

    /**
     * Set low priority.
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => $this->faker->numberBetween(7, 10),
        ]);
    }
}
