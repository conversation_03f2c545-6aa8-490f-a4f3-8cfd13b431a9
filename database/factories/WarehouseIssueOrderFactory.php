<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\Warehouse;
use App\Models\WarehouseIssueOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseIssueOrder>
 */
class WarehouseIssueOrderFactory extends Factory
{
    protected $model = WarehouseIssueOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'warehouse_id' => Warehouse::factory(),
            'number' => 'РО-' . $this->faker->unique()->numberBetween(1000, 9999),
            'date_from' => $this->faker->date(),
            'status_id' => Status::factory(),
            'held' => $this->faker->boolean(30), // 30% проведенных
            'document_basis_type' => $this->faker->optional()->randomElement(['invoice', 'act', 'order']),
            'document_basis_id' => $this->faker->optional()->uuid(),
            'write_off_reason' => $this->faker->randomElement([
                'defective', 'expired', 'shortage', 'internal_use', 
                'return_to_supplier', 'damage', 'other'
            ]),
            'reason_description' => $this->faker->optional()->sentence(),
            'total_quantity' => $this->faker->numberBetween(1, 1000),
            'total_cost' => $this->faker->randomFloat(2, 100, 50000),
            'comment' => $this->faker->optional()->text(200),
        ];
    }

    /**
     * Indicate that the order is held (проведен).
     */
    public function held(): static
    {
        return $this->state(fn (array $attributes) => [
            'held' => true,
        ]);
    }

    /**
     * Indicate that the order is not held (не проведен).
     */
    public function notHeld(): static
    {
        return $this->state(fn (array $attributes) => [
            'held' => false,
        ]);
    }

    /**
     * Set specific write-off reason.
     */
    public function withReason(string $reason): static
    {
        return $this->state(fn (array $attributes) => [
            'write_off_reason' => $reason,
        ]);
    }

    /**
     * Set defective reason.
     */
    public function defective(): static
    {
        return $this->withReason('defective')->state([
            'reason_description' => 'Обнаружен брак при проверке качества',
        ]);
    }

    /**
     * Set expired reason.
     */
    public function expired(): static
    {
        return $this->withReason('expired')->state([
            'reason_description' => 'Истек срок годности товара',
        ]);
    }

    /**
     * Set shortage reason.
     */
    public function shortage(): static
    {
        return $this->withReason('shortage')->state([
            'reason_description' => 'Выявлена недостача при инвентаризации',
        ]);
    }
}
