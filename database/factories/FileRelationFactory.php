<?php

namespace Database\Factories;

use App\Models\File;
use App\Models\Acceptance;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FileRelation>
 */
class FileRelationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'file_id' => File::factory(),
            'related_id' => Acceptance::factory(),
            'related_type' => 'acceptances',
        ];
    }
}
