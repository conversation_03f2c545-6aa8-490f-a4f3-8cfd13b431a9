<?php

namespace Database\Factories;

use App\Enums\Api\Internal\StatusTypeEnum;
use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class ContractorFactory extends Factory
{
    use HasOrderedUuid;

    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'cabinet_id' => Cabinet::factory(),
            'title' => $this->faker->word(),
            'is_buyer' => $this->faker->boolean(),
            'is_supplier' => $this->faker->boolean(),
            'phone' => '88005553535',
            'fax' => $this->faker->word(),
            'email' => $this->faker->email(),
            'description' => $this->faker->name,
            'code' => $this->faker->countryCode(),
            'external_code' => $this->faker->languageCode(),
            'discounts_and_prices' => $this->faker->word(),
            'discount_card_number' => $this->faker->creditCardNumber(),
            'employee_id' => Employee::factory(),
            'shared_access' => $this->faker->boolean,
            'department_id' => Department::factory(),
            'status_id' => Status::factory()->create(
                [
                    'type' => StatusTypeEnum::CONTRACTORS->value
                ]
            ),
        ];
    }
}
