<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GlobalCurrency>
 */
class GlobalCurrencyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'num_code' => $this->faker->currencyCode,
            'char_code' => $this->faker->currencyCode,
            'short_name' => $this->faker->currencyCode,
            'external_id' => $this->faker->word,
            'name' => $this->faker->name,
            'value' => $this->faker->randomFloat(9,0,9999),
            'currency_date' => $this->faker->date,
            'is_default' => $this->faker->boolean(),
            'pluralization' => '{}'
        ];
    }
}
