<?php

namespace Database\Factories;

use App\Models\Shipment;
use App\Models\WarehouseItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseTransaction>
 */
class WarehouseTransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'item_id' => WarehouseItem::factory(),
            'shipment_id' => mt_rand(0, 1) ? Shipment::factory() : null,
            'transaction_type' => mt_rand(0, 1) ? 'inbound' : 'outbound',
            'quantity' => mt_rand(1, 100000),
            'total_cost' => mt_rand(1, 100000),
            'transaction_date' => $this->faker->date()
        ];
    }
}
