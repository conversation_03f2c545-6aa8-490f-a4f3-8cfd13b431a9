<?php

namespace Database\Factories;

use App\Models\LegalEntity;
use App\Traits\HasOrderedUuid;
use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\LegalEntityTaxation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LegalEntityDetail>
 */
class LegalEntityDetailFactory extends Factory
{
    use HasOrderedUuid;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'legal_entity_id' => LegalEntity::factory(),
            'taxation_type' => LegalEntityTaxation::eshn->value,
            'type' => LegalEntityType::INDIVIDUAL->value,
            'firstname' => $this->faker->firstName(),
            'patronymic' => $this->faker->firstName(),
            'lastname' => $this->faker->lastName(),
        ];
    }
}
