<?php

namespace Database\Factories;

use App\Models\Status;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Contractor;
use App\Models\LegalEntity;
use App\Models\CabinetCurrency;
use App\Enums\Api\Internal\ContractTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Contract>
 */
class ContractFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'number' => $this->faker->word(),
            'legal_entity_id' => LegalEntity::factory(),
            'currency_id' => CabinetCurrency::factory(),
            'code' => $this->faker->word(),
            'amount' => $this->faker->word(),
            'comment' => $this->faker->text(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'contractor_id' => Contractor::factory(),
            'cabinet_id' => Cabinet::factory(),
            'type' => $this->faker->randomElement(ContractTypeEnum::cases())->value,
            'date_from' => $this->faker->date(),
            'status_id' => Status::factory(),
            'shared_access' => $this->faker->boolean(),
            'is_printed' => $this->faker->boolean(),
            'is_sended' => $this->faker->boolean(),
        ];
    }
}
