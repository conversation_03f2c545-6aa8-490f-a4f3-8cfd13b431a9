<?php

namespace Database\Factories;

use App\Models\GoodsTransfer;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GoodsTransferItem>
 */
class GoodsTransferItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'goods_transfer_id' => GoodsTransfer::factory(),
            'product_id' => Product::factory(),
            'quantity' => random_int(1, 100000),
            'price' => random_int(1, 100000),
            'total_price' => random_int(1, 100000),
            'recidual_from' => random_int(1, 100000),
            'recidual_to' => random_int(1, 100000)
        ];
    }
}
