<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Warehouse;
use App\Models\ProductThresholdWarehouses;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductThresholdWarehousesFactory extends Factory
{
    protected $model = ProductThresholdWarehouses::class;

    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'warehouse_id' => Warehouse::factory(),
            'threshold_count' => random_int(0, 100000)
        ];
    }
}
