<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserViewSetting>
 */
class UserViewSettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'employee_id' => Employee::factory(),
            'cabinet_id' => Cabinet::factory(),
            'settings' => '{}',
        ];
    }
}
