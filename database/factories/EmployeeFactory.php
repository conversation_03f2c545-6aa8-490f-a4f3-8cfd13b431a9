<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Employee;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeFactory extends Factory
{
    protected $model = Employee::class;

    public function definition(): array
    {
        return [
            'id' => Str::orderedUuid()->toString(),
            'user_id' => User::factory(),
            'lastname' => $this->faker->name,
            'firstname' => $this->faker->firstName,
            'patronymic' => $this->faker->name('male') . 'ич',
            'telephone' => $this->faker->phoneNumber(),
            'email' => $this->faker->email,
            'status' => $this->faker->word
        ];
    }
}
