<?php

namespace Database\Factories;

use App\Models\WarehouseGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseGroup>
 */
class WarehouseGroupFactory extends Factory
{
    protected $model = WarehouseGroup::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true),
            'cabinet_id' => fake()->uuid(),
            'parent_id' => null,
        ];
    }

    /**
     * Create a warehouse group with a parent.
     */
    public function withParent(string $parentId): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentId,
        ]);
    }
}
