---
inclusion: always
---

# Языковые правила для русскоязычного проекта

## Основные требования к языку

### Русский язык в коммуникации
- Все ответы и объяснения должны быть написаны на русском языке
- Комментарии в коде должны быть на русском языке, где это возможно
- Технические термины следует указывать на английском с русским переводом в скобках (например, "container (контейнер)")
- Сообщения об ошибках и логи должны сохраняться в оригинальном виде, но объяснения предоставляются на русском

### Исключения
- Код должен быть написан на английском языке (имена переменных, функций, классов)
- Официальная документация и команды остаются в оригинальном виде
- Исключения применяются только при явном запросе пользователя на другой язык

## Локализация проекта

### Файлы локализации
- Используйте директорию `resources/lang/` для языковых файлов
- Основные сообщения должны быть переведены на русский язык
- Валидационные сообщения должны быть понятными для русскоязычных пользователей

### API ответы
- Сообщения об ошибках в API должны быть на русском языке
- Описания полей и валидационные сообщения - на русском
- Техническая информация (коды ошибок, логи) остается на английском

### Документация
- Комментарии к методам и классам на русском языке
- README и техническая документация на русском
- API документация (Scramble) с русскими описаниями