# Project Structure

## Code Organization

### Application Layer (`app/`)
The application follows <PERSON><PERSON>'s standard structure with domain-driven organization:

- **Actions/** - Single-purpose action classes for business logic
- **Clients/** - External API client implementations
- **Console/** - Artisan commands and console kernels
- **Contracts/** - Interface definitions and contracts
- **Data/** - Data transfer objects and value objects
- **DTO/** - Data Transfer Objects for API communication
- **Entities/** - Domain entities and business objects
- **Enums/** - Enumeration classes for constants
- **Events/** - Event classes for event-driven architecture
- **Exceptions/** - Custom exception classes
- **Extensions/** - Framework extensions and customizations
- **Helpers/** - Utility and helper functions
- **Http/** - Controllers, middleware, requests, and resources
- **Jobs/** - Queue job classes
- **Listeners/** - Event listener classes
- **Mail/** - Email templates and mailable classes
- **Models/** - Eloquent models and database interactions
- **Modules/** - Feature modules (if using modular architecture)
- **Notifications/** - Notification classes
- **Policies/** - Authorization policies
- **Providers/** - Service providers
- **Repositories/** - Repository pattern implementations
- **Rules/** - Custom validation rules
- **Services/** - Business logic service classes
- **Traits/** - Reusable trait classes

### Configuration (`config/`)
- Standard Laravel configuration files
- Custom configs for integrations: `dadata.php`, `ozon.php`, `wildberries.php`

### Database (`database/`)
- **migrations/** - Database schema migrations
- **seeders/** - Database seeders
- **factories/** - Model factories for testing

### Testing (`tests/`)
- **Feature/** - Integration and feature tests
- **Unit/** - Unit tests
- **TestCase.php** - Base test case class

### Resources (`resources/`)
- **views/** - Blade templates
- **lang/** - Localization files
- **css/** - Stylesheets
- **js/** - JavaScript assets

## Architectural Patterns

### Domain-Driven Design
- Clear separation between Actions, Services, and Repositories
- DTOs for data transfer between layers
- Entities for domain logic
- Contracts for interface definitions

### API-First Architecture
- Controllers focused on HTTP concerns
- Business logic in Action/Service classes
- Consistent API responses via Resources
- API documentation via Scramble

### Repository Pattern
- Data access abstraction via Repository classes
- Models handle Eloquent relationships
- Services orchestrate business operations

## File Naming Conventions
- **Classes**: PascalCase (e.g., `UserService.php`)
- **Methods**: camelCase
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Database**: snake_case for tables and columns
- **Routes**: kebab-case for URLs

## Code Style
- **PSR-12** coding standard enforced by Laravel Pint
- **4 spaces** for indentation
- **LF** line endings
- **UTF-8** encoding
- **Final newline** required in all files