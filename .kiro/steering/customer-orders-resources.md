# Руководство по созданию HTTP Resources для API - На примере CustomerOrders

## Обзор

HTTP Resources используются для формирования JSON ответов API и автоматической генерации OpenAPI документации через Dedoc Scramble. Данное руководство показывает, как правильно создавать ресурсы на основе данных, возвращаемых из репозиториев.

## Принцип создания ресурсов

### Основа - данные из репозитория

HTTP Resources создаются на основе структуры данных, которые возвращают методы `get()` и `show()` в репозиториях. Важно понимать, какие данные и в каком формате возвращает репозиторий, чтобы правильно их обработать в ресурсе.

#### Пример: CustomerOrdersRepository

**Метод `show()`** возвращает объект с джойнами и JSON-агрегацией:
```php
public function show(string $id): ?object
{
    return DB::table(self::TABLE . ' as co')
        ->leftJoin('contractors as c', 'co.contractor_id', '=', 'c.id')
        ->leftJoin('statuses as s', 'co.status_id', '=', 's.id')
        ->leftJoin('warehouses as w', 'co.warehouse_id', '=', 'w.id')
        ->select([
            'co.*',
            DB::raw("json_build_object('id', c.id, 'title', c.title) as contractor"),
            DB::raw("CASE WHEN s.id is not null THEN json_build_object('id', s.id, 'name', s.name) ELSE NULL END as status"),
            DB::raw("CASE WHEN w.id is not null THEN json_build_object('id', w.id, 'name', w.name) ELSE NULL END as warehouse"),
            DB::raw("coalesce(jsonb_agg(f) filter (where f.id is not null), '[]') AS files"),
            DB::raw("coalesce(jsonb_agg(cid) filter (where cid.id is not null), '[]') AS delivery_info"),
        ])
        ->where('co.id', $id)
        ->groupBy(['co.id', 'c.id', 's.id', 'w.id'])
        ->first();
}
```

**Метод `get()`** использует Entity Builder для пагинированных списков:
```php
public function get(string $id = null, array $filters = [], array $fields = ['*']): Collection
{
    $query = $this->getEntity();
    [$baseFields, $relationFields] = $query->parseFields($fields);
    
    $query->select($baseFields)
        ->where('cabinet_id', $id)
        ->where('deleted_at', null);
    
    // Применение фильтров авторизации
    $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::CUSTOMER_ORDERS->value);
    
    return $query->paginate($perPage, $page)->get();
}
```

## Соответствие данных репозитория и ресурсов

### Анализ данных из репозитория

Перед созданием ресурса необходимо понять структуру данных, возвращаемых репозиторием:

#### Данные из `show()` метода:
- **Базовые поля**: все поля из таблицы `customer_orders` (`co.*`)
- **JSON объекты**: `contractor`, `status`, `warehouse` - созданы через `json_build_object()`
- **JSON массивы**: `files`, `delivery_info` - созданы через `jsonb_agg()`
- **Nullable поля**: `status`, `warehouse` могут быть `NULL`

#### Данные из `get()` метода:
- **Базовые поля**: определяются через `parseFields()` из Entity Builder
- **Связанные данные**: загружаются через `with()` при наличии в `$relationFields`
- **Фильтрация**: применяется авторизация через `queryFilter()`

### Создание ресурсов на основе данных

## Пошаговый процесс создания ресурсов

### Шаг 1: Анализ репозитория
Перед созданием ресурса изучите методы `get()` и `show()` в соответствующем репозитории:

1. **Определите базовые поля** - какие поля таблицы возвращаются
2. **Найдите JSON объекты** - поля, созданные через `json_build_object()`
3. **Найдите JSON массивы** - поля, созданные через `jsonb_agg()`
4. **Определите nullable поля** - поля, которые могут быть `NULL`
5. **Изучите связанные данные** - какие связи загружаются через `with()`

### Шаг 2: Создание IndexResource
На основе метода `get()` репозитория:

```php
class CustomerOrderIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            // Базовые поля из таблицы (co.*)
            'id' => $this->id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Приведение типов для консистентности API
            'number' => (string) $this->number,
            'payment_status' => (string) $this->payment_status,
            'total_price' => (string) $this->total_price,
            
            // Булевы поля с явным приведением типа
            'held' => (bool) $this->held,
            'reserve' => (bool) $this->reserve,
            'has_vat' => (bool) $this->has_vat,
            
            // ID связанных сущностей (без джойнов для производительности)
            'cabinet_id' => $this->cabinet_id,
            'contractor_id' => $this->contractor_id,
            'warehouse_id' => $this->warehouse_id,
        ];
    }
}
```

### Шаг 3: Создание ShowResource
На основе метода `show()` репозитория с учетом JSON объектов:

```php
class CustomerOrderShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            // Все поля из IndexResource
            ...parent::toArray($request), // или дублируем поля
            
            // JSON объекты из репозитория (json_build_object)
            'contractor' => $this->when(!empty($this->contractor), [
                'id' => $this->contractor['id'],
                'title' => $this->contractor['title'],
            ]),
            
            // Nullable JSON объекты
            'status' => $this->status ? [
                'id' => $this->status['id'],
                'name' => $this->status['name'],
            ] : [],
            
            // JSON массивы (jsonb_agg) через коллекции ресурсов
            'files' => $this->files ? FileResource::collection($this->files) : [],
            
            // Вложенные ресурсы для сложных объектов
            'delivery_info' => $this->delivery_info ? 
                new CustomerOrderDeliveryInfoResource($this->delivery_info) : [],
        ];
    }
}
```

### Шаг 4: Обработка специальных случаев

#### JSON объекты из PostgreSQL
Когда репозиторий возвращает JSON через `json_build_object()`:
```php
// В репозитории
DB::raw("json_build_object('id', c.id, 'title', c.title) as contractor")

// В ресурсе - проверяем на пустоту и извлекаем поля
'contractor' => $this->when(!empty($this->contractor), [
    'id' => $this->contractor['id'],
    'title' => $this->contractor['title'],
]),
```

#### JSON массивы из PostgreSQL
Когда репозиторий возвращает массивы через `jsonb_agg()`:
```php
// В репозитории
DB::raw("coalesce(jsonb_agg(f) filter (where f.id is not null), '[]') AS files")

// В ресурсе - используем коллекции ресурсов
'files' => $this->files ? FileResource::collection($this->files) : [],
```

#### Nullable поля
Для полей, которые могут быть NULL:
```php
// Простая проверка
'status' => $this->status ? [...] : [],

// Или через when()
'warehouse' => $this->when($this->warehouse, [
    'id' => $this->warehouse['id'],
    'name' => $this->warehouse['name']
]),
```

## Структура ресурсов

### 1. IndexResource - для списков (метод `get()`)
**Файл:** `app/Http/Resources/CustomerOrderIndexResource.php`

Создается на основе базовых полей, возвращаемых Entity Builder в методе `get()` репозитория. IndexResource содержит только основные поля без связанных данных для оптимизации производительности при загрузке списков.

#### Основные поля:
```php
[
    /** @var string $id Уникальный идентификатор записи */
    'id' => $this->id,
    
    /** @var string $number Номер документа */
    'number' => (string) $this->number,
    
    /** @var string $date_from Дата начала действия */
    'date_from' => $this->date_from,
    
    /** @var string $payment_status Статус оплаты (paid|unpaid) */
    'payment_status' => (string) $this->payment_status,
    
    /** @var string $total_price Общая стоимость */
    'total_price' => (string) $this->total_price,
    
    /** @var bool $held Флаг удержания */
    'held' => (bool) $this->held,
    
    /** @var bool $reserve Флаг резерва */
    'reserve' => (bool) $this->reserve,
    
    /** @var bool $has_vat Наличие НДС */
    'has_vat' => (bool) $this->has_vat,
    
    /** @var bool $price_includes_vat Цена включает НДС */
    'price_includes_vat' => (bool) $this->price_includes_vat,
]
```

#### Связанные поля:
- `cabinet_id` - Идентификатор кабинета
- `employee_id` - Идентификатор сотрудника  
- `department_id` - Идентификатор отдела
- `legal_entity_id` - Идентификатор юридического лица
- `contractor_id` - Идентификатор контрагента
- `sales_channel_id` - Идентификатор канала продаж
- `warehouse_id` - Идентификатор склада
- `status_id` - Идентификатор статуса

### 2. CustomerOrderShowResource
**Файл:** `app/Http/Resources/CustomerOrderShowResource.php`

Используется для детального отображения заказа покупателя в методе `show()`.

#### Расширенные поля (дополнительно к IndexResource):
```php
[
    /** @var array{id: string, title: string} $contractor Контрагент */
    'contractor' => $this->when(!empty($this->contractor), [
        'id' => $this->contractor['id'],
        'title' => $this->contractor['title'],
    ]),

    /** @var array{id: string, name: string}|null $status Статус */
    'status' => $this->status ? [
        'id' => $this->status['id'],
        'name' => $this->status['name'],
    ] : [],

    /** @var array{id: string, name: string}|null $warehouse Склад */
    'warehouse' => $this->warehouse ? [
        'id' => $this->warehouse['id'],
        'name' => $this->warehouse['name']
    ] : [],

    /** @var AnonymousResourceCollection<FileResource>|null $files Файлы */
    'files' => $this->files ? FileResource::collection($this->files) : [],

    /** @var CustomerOrderDeliveryInfoResource|null $delivery_info Информация о доставке */
    'delivery_info' => $this->delivery_info ? new CustomerOrderDeliveryInfoResource($this->delivery_info) : [],
]
```

### 3. CustomerOrderIndexCollection
**Файл:** `app/Http/Resources/CustomerOrderIndexCollection.php`

Коллекция для пагинированного списка заказов.

#### Структура ответа:
```php
[
    'data' => $this->collection, // Массив CustomerOrderIndexResource
    'meta' => [
        /** @var int $current_page Текущая страница */
        'current_page' => $this->additional['meta']['current_page'],
        
        /** @var int $per_page Количество элементов на странице */
        'per_page' => $this->additional['meta']['per_page'],
        
        /** @var int $total Общее количество элементов */
        'total' => $this->additional['meta']['total'],
        
        /** @var int $last_page Последняя страница */
        'last_page' => $this->additional['meta']['last_page']
    ]
]
```

### 4. CustomerOrderDeliveryInfoResource
**Файл:** `app/Http/Resources/CustomerOrderDeliveryInfoResource.php`

Ресурс для информации о доставке заказа.

#### Поля доставки:
```php
[
    'id' => $this->id,
    'created_at' => $this->created_at,
    'updated_at' => $this->updated_at,
    'comment' => $this->comment,
    'post_code' => $this->post_code,
    'country' => $this->country,
    'region' => $this->region,
    'city' => $this->city,
    'street' => $this->street,
    'house' => $this->house,
    'office' => $this->office,
    'other' => $this->other,
    'deleted_at' => $this->deleted_at,
]
```

## Связанные ресурсы

### FileResource
**Файл:** `app/Http/Resources/FileResource.php`

Используется для отображения прикрепленных файлов к заказу.

```php
[
    'id' => $this->id,
    'name' => $this->name,
    'path' => $storageService->getUrl($this->path, $this->is_private), // URL через S3
    'size' => $this->size,
    'mime_type' => $this->mime_type,
    'type' => $this->type,
    'created_at' => $this->created_at,
]
```

## Использование в контроллерах

### CustomerOrdersController

#### Аннотации Scramble:
```php
/**
 * @response CustomerOrderIndexCollection<CustomerOrderIndexResource>
 */
public function index(CustomerOrderIndexRequest $request): ?JsonResponse

/**
 * @response CustomerOrderShowResource
 */
public function show(Request $request, string $id): JsonResponse
```

#### Применение ресурсов:
```php
// Список заказов с пагинацией
$collection = new CustomerOrderIndexCollection($data['data']);
return $this->successResponse($collection->additional(['meta' => $data['meta']]));

// Детальный просмотр заказа
return $this->successResponse(CustomerOrderShowResource::make($data));
```

### CustomerOrderItemController

Для позиций заказа используется прямой возврат данных без специальных ресурсов:
```php
public function show(Request $request, string $id): JsonResponse
{
    $data = $this->service->show($id);
    return $this->successResponse($data);
}
```

## Особенности для Scramble

### 1. PHPDoc аннотации
Все поля содержат подробные аннотации с типами:
```php
/** @var string $id Уникальный идентификатор записи */
/** @var bool $held Флаг удержания */
/** @var array{id: string, title: string} $contractor Контрагент */
```

### 2. Условные поля
Используется метод `when()` для условного включения полей:
```php
'contractor' => $this->when(!empty($this->contractor), [...])
```

### 3. Коллекции ресурсов
Для связанных данных используются коллекции:
```php
'files' => $this->files ? FileResource::collection($this->files) : []
```

### 4. Типизация в контроллерах
Аннотации `@response` указывают Scramble, какой ресурс использовать для документации:
```php
/**
 * @response CustomerOrderShowResource
 */
```

## Принципы работы с ресурсами

### 1. Разделение ответственности
- **IndexResource** - минимальный набор полей для списков
- **ShowResource** - полная информация с связанными данными
- **Collection** - обертка для пагинации

### 2. Типизация данных
- Явное приведение типов: `(string)`, `(bool)`
- Подробные PHPDoc аннотации
- Указание возможных значений: `(paid|unpaid)`

### 3. Безопасность
- Условное включение чувствительных данных
- Фильтрация полей через `when()`
- Использование S3StorageService для безопасных URL файлов

### 4. Производительность
- Ленивая загрузка связанных данных
- Минимальный набор полей в индексных ресурсах
- Кэширование URL файлов через S3

## Генерация OpenAPI документации

Scramble автоматически анализирует:
1. PHPDoc аннотации в ресурсах
2. Аннотации `@response` в контроллерах
3. Структуру возвращаемых данных
4. Типы полей и их описания

Результат - автоматически сгенерированная OpenAPI спецификация с подробным описанием всех полей ответов API.