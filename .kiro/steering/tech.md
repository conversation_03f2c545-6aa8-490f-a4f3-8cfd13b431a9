# Technology Stack

## Core Framework
- **Laravel 11.9+** - PHP web framework
- **PHP 8.2+** - Required PHP version

## Database & Storage
- **PostgreSQL 15** - Primary database
- **Redis** - Caching and session storage
- **AWS S3** - File storage via Flysystem

## Development Environment
- **Docker Compose** - Containerized development environment
- **Laravel Sail** - Docker development environment for Laravel

## Frontend Build Tools
- **Vite** - Frontend build tool and dev server
- **Laravel Vite Plugin** - Laravel integration

## Key Dependencies
- **<PERSON>vel Sanctum** - API authentication
- **Laravel Pulse** - Application monitoring
- **Scramble** - API documentation generation
- **Maatwebsite Excel** - Excel import/export
- **DaData** - Russian address validation service

## Code Quality & Testing
- **PHPUnit** - Testing framework
- **Paratest** - Parallel test execution
- **Laravel Pint** - Code formatting (PSR-12 preset)
- **Larastan/PHPStan** - Static analysis (Level 6)

## Common Commands

### Development Setup
```bash
# Initial setup
cp .env.example .env
docker compose up --build

# Install dependencies
composer install
npm install
```

### Development
```bash
# Start development environment
docker compose up

# Run frontend dev server
npm run dev

# Build frontend assets
npm run build
```

### Code Quality
```bash
# Format code
./vendor/bin/pint

# Run static analysis
./vendor/bin/phpstan analyse

# Run tests
./vendor/bin/phpunit

# Run tests in parallel
./vendor/bin/paratest
```

### Laravel Commands
```bash
# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```