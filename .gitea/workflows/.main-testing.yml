name: Main test
run-name: ${{ gitea.actor }} is testing feature
on:
  pull_request:
    types: [opened, reopened, edited]
    branches:
      - main

jobs:
  testing:
    runs-on: ubuntu-latest
    # services:
    #   mysql:
    #     image: 'mysql:latest'
    #     env:
    #       MYSQL_USER: laravel
    #       MYSQL_DB: laravel
    #       MYSQL_PASSWORD: passwddb
    #       MYSQL_ROOT_PASSWORD: passwddb
    #     ports:
    #       - 5432:5432
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ gitea.event_name }} event."

      - name: Let's look at your code
        uses: actions/checkout@v4

      - name: Setup PHP Action
        uses: shivammathur/setup-php@2.30.5
        with:
          php-version: '8.2'
          extensions: pdo, pdo_sqlite, dom, curl, libxml, mbstring, zip, pcntl, bcmath, soap, intl, gd, exif, iconv
          tools: composer:v2

      - name: Install composer dependencies
        run: composer install -n --prefer-dist

      - name: Prepare environment
        run: |
          cp .env.ci .env
          php artisan key:generate
          php artisan config:cache
          touch db.sqlite

      - name: Test Pint
        run: ./vendor/bin/pint --test

      - name: Test PHPStan
        run: ./vendor/bin/phpstan analyze

      - name: Run Artisan tests
        run: |
          php artisan test

