# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Языковые правила для русскоязычного проекта

## Основные требования к языку

### Русский язык в коммуникации

- Все ответы и объяснения должны быть написаны на русском языке
- Комментарии в коде должны быть на русском языке, где это возможно
- Технические термины следует указывать на английском с русским переводом в скобках (например, "container (контейнер)")
- Сообщения об ошибках и логи должны сохраняться в оригинальном виде, но объяснения предоставляются на русском

### Исключения

- Код должен быть написан на английском языке (имена переменных, функций, классов)
- Официальная документация и команды остаются в оригинальном виде
- Исключения применяются только при явном запросе пользователя на другой язык

## Локализация проекта

### Файлы локализации

- Используйте директорию `resources/lang/` для языковых файлов
- Основные сообщения должны быть переведены на русский язык
- Валидационные сообщения должны быть понятными для русскоязычных пользователей

### API ответы

- Сообщения об ошибках в API должны быть на русском языке
- Описания полей и валидационные сообщения - на русском
- Техническая информация (коды ошибок, логи) остается на английском

### Документация

- Комментарии к методам и классам на русском языке
- README и техническая документация на русском
- API документация (Scramble) с русскими описаниями

## Project Overview

This is a Laravel 11 multi-tenant e-commerce/marketplace management system that integrates with major Russian
marketplaces (Ozon, Wildberries). The system manages products, inventory, orders, and financial transactions across
multiple marketplaces with complex business logic for multi-cabinet organizations.

## Development Commands

### Core Laravel Commands

```bash
# Run development server
php artisan serve

# Run migrations
php artisan migrate

# Seed the database
php artisan db:seed

# Generate application key
php artisan key:generate

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Queue processing
php artisan queue:work
```

### Testing

```bash
# Run all tests
php artisan test

# Run PHPUnit directly
vendor/bin/phpunit

# Run parallel tests
vendor/bin/paratest

# Run specific test
php artisan test --filter=TestName
```

### Code Quality

```bash
# Run Laravel Pint (PSR-12 code formatting)
vendor/bin/pint

# Run Larastan (static analysis)
vendor/bin/phpstan analyse

# Generate API documentation
php artisan scramble:generate
```

### Frontend Assets

```bash
# Development build
npm run dev

# Production build
npm run build
```

### Docker Development

```bash
# Start development environment
docker compose up --build

# Run commands in container
docker compose exec app php artisan migrate
```

## Architecture Overview

### Modular Structure

- `app/Modules/` - Self-contained feature modules (Marketplaces, AI)
- `app/Entities/` - Custom entity layer with BaseEntity pattern
- `app/Repositories/` - Repository pattern implementation organized by domain
- `app/Services/` - Business logic services
- `app/Clients/` - External API clients (Ozon, Wildberries)

### Key Business Domains

- **Multi-tenant Cabinet System** - Organization/workspace isolation
- **Product Management** - Complex product entities with attributes and categories
- **Warehouse Management** - Multi-location inventory with detailed organization
- **Order Management** - B2C/B2B order processing (FBO, FBS, DBS)
- **Financial Management** - Multi-currency payments and commission tracking
- **Marketplace Integration** - Ozon and Wildberries API integration

### Database Architecture

- Uses UUIDs for primary keys
- Comprehensive soft delete implementation
- Multi-tenancy through cabinet-based data isolation
- PostgreSQL as primary database

### API Design

- Laravel Sanctum for authentication
- API resources for consistent responses
- Rate limiting implementation
- Modular API structure in `app/Modules/`

## Key Development Patterns

### Laravel patterns

Models

- Laravel models are only used in tests for factories and quick database populating. Otherwise we use Query Builder.

### Entity System

The application uses a custom entity system instead of relying solely on Eloquent:

- Extend `BaseEntity` for custom query building
- Use `EntityBuilder` for advanced filtering
- Implement repository contracts for consistent data access

### Repository Pattern

- All repositories implement contracts from `app/Contracts/Repositories/`
- Domain-specific organization (Sales, Procurement, Warehouses, etc.)
- Registered in `RepositoryServiceProvider`

### Marketplace Integration

- Uses driver pattern with `MarketplaceDriverFactory`
- Separate drivers for each marketplace (Ozon, Wildberries)
- Background synchronization jobs for real-time data updates

### Multi-tenancy

- Cabinet-based data isolation
- Middleware for permission checking
- Department and employee role management

## Important File Locations

### Configuration

- `config/ozon.php` - Ozon marketplace configuration
- `config/wildberries.php` - Wildberries marketplace configuration
- `config/dadata.php` - Russian address service configuration

### Core Models

- `app/Models/Cabinet.php` - Multi-tenancy core model
- `app/Models/Product.php` - Product management
- `app/Models/Warehouse.php` - Inventory management
- `app/Models/User.php` - User management with multi-cabinet support

### API Controllers

- `app/Http/Controllers/` - Base controllers
- `app/Modules/Marketplaces/Http/Controllers/` - Marketplace-specific endpoints

### Background Jobs

- `app/Jobs/` - General background jobs
- `app/Jobs/Ozon/` - Ozon-specific processing
- `app/Jobs/FIFOJobs/` - Inventory processing jobs

## Testing Guidelines

### Test Structure

- `tests/Feature/` - Integration tests covering full workflows
- `tests/Unit/` - Unit tests for individual components
- Tests are organized by feature/domain

### Test Database

- Uses PostgreSQL for testing (configured in phpunit.xml)
- Database: `laravel_utf8`
- Tests use database transactions for isolation

### Running Tests

- Always run tests before committing changes
- Use `php artisan test` for Laravel test runner
- Use `vendor/bin/paratest` for parallel execution

## Development Best Practices

### Code Style

- Follow PSR-12 standards (enforced by Laravel Pint)
- Use typed properties and return types
- Implement proper error handling

### Database

- Always use migrations for schema changes
- Use UUIDs for primary keys
- Implement soft deletes for data integrity

### API Development

- Use API resources for response formatting
- Implement proper authentication with Sanctum
- Follow RESTful conventions

### Queue Jobs

- Use background jobs for long-running operations
- Implement proper error handling and retries
- Use FIFO jobs for inventory operations

## Common Troubleshooting

### Database Issues

- Run `php artisan migrate:fresh --seed` for clean database
- Check database connection in `.env` file
- Ensure PostgreSQL is running

### Queue Processing

- Start queue worker: `php artisan queue:work`
- Check failed jobs: `php artisan queue:failed`
- Restart workers after code changes

### API Integration

- Check API credentials in environment file
- Verify webhook endpoints are accessible
- Monitor API rate limits

## External Dependencies

### Required Services

- PostgreSQL database
- Redis for caching and queues
- AWS S3 for file storage
- DaData API for Russian address validation

### Marketplace APIs

- Ozon API credentials required for integration
- Wildberries API credentials required for integration
- Webhook endpoints must be configured in marketplace settings
