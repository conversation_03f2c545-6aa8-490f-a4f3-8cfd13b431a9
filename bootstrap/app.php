<?php

use App\Http\Middleware\LogRequests;
use App\Http\Middleware\Api\Internal\ForceJsonResponse;
use App\Http\Middleware\Api\Internal\SalesChannel\CheckCabinetScope;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->prepend(LogRequests::class);

        $middleware->prepend(ForceJsonResponse::class);
        $middleware->alias([
            'check-cabinet-access' => CheckCabinetScope::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
