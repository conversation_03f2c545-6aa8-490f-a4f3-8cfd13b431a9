<?php

use Tests\TestCase;
use App\Models\Acceptance;
use App\Models\AcceptanceItem;

class DebugTest extends TestCase
{
    public function test_debug_acceptance_items()
    {
        $this->actingAs($this->user);
        
        $acceptance = Acceptance::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        AcceptanceItem::factory()->count(2)
            ->create(['acceptance_id' => $acceptance->id]);

        $response = $this->getJson('/api/internal/acceptances/items?' . http_build_query([
            'acceptance_id' => $acceptance->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        echo "Status: " . $response->getStatusCode() . "\n";
        echo "Response: " . $response->getContent() . "\n";
        
        $this->assertTrue(true);
    }
}
